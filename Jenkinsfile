pipeline {
  agent any

  tools {
    jdk 'Open JDK 11'
  }

  stages {

    stage('Compile') {
      steps {
        script {
          buildNumber = createBuildNumber()
          projectVersion = readProjectVersion()
          gitHash = readGitHash()
          gitMessage = readGitMessage ()
          gradlew 'clean compileJava'
        }
      }
    }

    stage('Unit Test') {
      steps {
        gradlew 'test'
      }
    }

    stage('Integration Test') {
      when { environment name: 'SKIP_INTEGRATION_TESTS', value: 'false' }
      steps {
        gradlew 'integrationTest'
      }
    }

    stage('Static Analysis') {
      steps {
        withSonarQubeEnv('SonarQube Server') {
          gradlew "--info sonarqube"
        }
      }
    }

    stage('Package') {
      steps {
        gradlew "docker"
      }
    }

    stage('Component Test') {
      when { environment name: 'SKIP_COMPONENT_TESTS', value: 'false' }
      steps {
        gradlew 'componentTest -PuseComponentTestHarness'
      }
    }

    stage('Publish') {
      steps {
        dockerPublish()
      }
    }

    stage('System Test') {
      steps {
        echo 'Run system tests'
      }
    }
  }

  post {
    always {
      archiveArtifacts artifacts: 'service/build/libs/**/*.jar', fingerprint: true
      junit '**/test-results/**/*.xml'
      deleteDir()
    }
    success {
      sendEmail("Successful")
      jiraSendBuildInfo site: 'peterpal.atlassian.net', branch: gitMessage
    }
    unstable {
      sendEmail("Unstable")
    }
    failure {
      sendEmail("Failed")
    }
  }

  options {
    buildDiscarder(logRotator(numToKeepStr: '5'))
    timeout(time: 25, unit: 'MINUTES')
  }

}

import java.lang.Math

def buildNumber = null
def projectVersion = null
def gitHash = null

def gradlew(command) {
  def buildProperties = "-PbuildNumber=${buildNumber} -PgitHash=${gitHash}"
  sh "chmod +x gradlew; ./gradlew ${buildProperties} ${command}"
}

// get change log to be send over the mail
@NonCPS
def getChangeString() {
  MAX_MSG_LEN = 100
  def changeString = ""

  echo "Gathering SCM changes"
  def changeLogSets = currentBuild.changeSets
  for (int i = 0; i < changeLogSets.size(); i++) {
    def entries = changeLogSets[i].items
    for (int j = 0; j < entries.length; j++) {
      def entry = entries[j]
      truncated_msg = entry.msg.take(MAX_MSG_LEN)
      changeString += " - ${truncated_msg} [${entry.author}]\n"
    }
  }

  if (!changeString) {
    changeString = " - No new changes"
  }
  return changeString
}

def sendEmail(status) {
  mail(
      to: "${env.BUILD_STATUS_EMAIL_RECIPIENTS}",
      subject: "Build $BUILD_NUMBER - " + status + " (${currentBuild.fullDisplayName})",
      body: "Changes:\n " + getChangeString() + "\n\n Check console output at: $BUILD_URL/console" + "\n")
}

def getDockerImageTag() {
  return env.BRANCH_NAME != 'master' ? "${projectVersion}-${branchName}.${buildNumber}" : "${projectVersion}.${buildNumber}"
}

def getReleaseNumber() {
  return "${projectVersion}.${buildNumber}"
}

def getBranchName() {
  return env.BRANCH_NAME.substring(0, Math.min(env.BRANCH_NAME.length(), 20))
}

def getTagLatest() {
  return getBranchName() == 'master'
}

def getDockerRegistries() {
  return "${env.DOCKER_REGISTRIES}".replaceAll("\\s","").tokenize(',')
}

def dockerPublish() {
  for (registry in getDockerRegistries())  {
    println "ians registry: ${registry}"
    if (getTagLatest()) {
      gradlew "dockerPushlatest -PreleaseNumber=latest -PdockerRegistry=${registry}"
    }
    gradlew "dockerPush${releaseNumber} -PreleaseNumber=${releaseNumber} -PdockerRegistry=${registry}"
  }
}

def createBuildNumber() {
  return env.BRANCH_NAME == 'master' ?
      VersionNumber('${BUILD_DATE_FORMATTED, \"yyyyMMdd\"}${BUILDS_TODAY, XX}') :
      "${branchName}-" + VersionNumber('${BUILDS_ALL_TIME, XXX}')
}

def readProjectVersion() {
  return sh (
      script: "chmod +x gradlew; ./gradlew properties -q | grep \"version:\" | awk '{print \$2}'",
      returnStdout: true
  ).trim() - '-SNAPSHOT'
}

def readGitHash() {
  def gitHash = sh(returnStdout: true, script: 'git rev-parse HEAD').trim()
  if (gitHash == null) {
    return env.BUILD_NUMBER
  } else {
    return gitHash.take(8)
  }
}

def readGitMessage() {
  if (env.BRANCH_NAME == 'master') {
    def gitMessage = sh(returnStdout: true, script: 'git show -s --format=%s').trim()
    println gitMessage
    return gitMessage
    } else {
    return null
    }
}

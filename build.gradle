buildscript {
    ext {
        springBootVersion = "2.2.6.RELEASE"
    }
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
    }
}

plugins {
    id "org.sonarqube" version "2.6.2"
}

group = 'au.com.peterpal'
version = '2.0'

if (project.hasProperty("buildNumber")) {
    version = "${version}.${project.buildNumber}"
} else {
    version = "${version}-SNAPSHOT"
}

ext {
    commonDddVersion = "0.4.2019091305"
    commonMoneyVersion = "0.5.2020022702"
    commonRestVersion = "0.9.2020042001"
    commonSequenceVersion = "0.5.2024060602"
    commonAuditVersion = "1.2.2023053102"
    commonUtilsVersion = "2.0.2021120902"

    postgresqlSchemaTools = "0.1+"

    springBootVersion = "2.2.6.RELEASE"

    jsonpVersion = "1.1.2"
    jacksonDatabindVersion = "2.9.5"
    jacksonModuleVersion = "2.9.6"

    hibernateVersion = "5.2.16.Final"
    querydslVersion = "4.2.1"
    h2Version = "1.4.197"

    springFoxSwaggerVersion = "2.9.2"
    log4jVersion = "2.10.0"
    guavaVersion = "24.1-jre"
    commonsLang3Version = "3.7"
    lombokVersion = "1.18.12"
    flywayVersion="5.2.1"

    testToolsVersion = "0.1"
    integrationTestToolsVersion = "0.1"
    junitVersion = "4.12"
    assertJVersion = "3.15.0"
    hamcrestVersion = "1.3"
    mockitoVersion = "2.22.0"

    commonSecurityVersion = "1.0.2020091403"
    springDocVersion = "1.4.1"
    keycloakVersion = "10.0.2"

    spockVersion = "1.2-groovy-2.4"
    spockSpringVersion = "1.2-groovy-2.4"
    groovyVersion = "2.4.15"
}

subprojects {
    apply plugin: "java"
    apply plugin: "eclipse"
    apply plugin: "idea"
    apply plugin: "jacoco"

    group = parent.group
    version = parent.version
    sourceCompatibility = JavaVersion.VERSION_11
    targetCompatibility = JavaVersion.VERSION_11

    repositories {
        mavenCentral()
        maven {
            url 'https://dl.bintray.com/palantir/releases' // docker-compose-rule is published on bintray
        }
    }

    dependencies {
        // Utility libraries
        compileOnly("org.projectlombok:lombok:$lombokVersion")
        annotationProcessor("org.projectlombok:lombok:$lombokVersion")
        implementation("org.apache.logging.log4j:log4j-web:$log4jVersion")
        implementation("com.google.guava:guava:$guavaVersion")
        implementation("org.apache.commons:commons-lang3:$commonsLang3Version")

        // Dependencies that are not included in Java 11
        implementation("javax.xml.bind:jaxb-api:2.3.0")
        implementation("com.sun.xml.bind:jaxb-core:2.3.0")
        implementation("com.sun.xml.bind:jaxb-impl:2.3.0")

        implementation("org.javassist:javassist:3.23.1-GA")

        testCompileOnly("org.projectlombok:lombok:$lombokVersion")
        testAnnotationProcessor("org.projectlombok:lombok:$lombokVersion")

        testImplementation("au.com.peterpal:test-tools:$testToolsVersion")
        testImplementation("junit:junit:$junitVersion")
        testImplementation("org.hamcrest:hamcrest-core:$hamcrestVersion")
        testImplementation("org.assertj:assertj-core:$assertJVersion")
        testImplementation("org.mockito:mockito-core:$mockitoVersion")
        testImplementation("org.spockframework:spock-core:$spockVersion")
        testImplementation("org.spockframework:spock-spring:$spockSpringVersion")
        testImplementation("org.codehaus.groovy:groovy-all:$groovyVersion")
    }

    compileJava {
        options.compilerArgs << "-parameters"
    }

    cleanTest {
        delete "${buildDir}/reports/tests/"
        delete "${buildDir}/test-results/"
    }

    test {
        useJUnit {
            excludeCategories 'au.com.peterpal.common.test.IntegrationTest',
                'au.com.peterpal.common.test.ComponentTestHarness',
                'au.com.peterpal.common.test.ComponentTest'
        }
    }

    task integrationTest(type: Test) {
        dependsOn test
        useJUnit {
            includeCategories 'au.com.peterpal.common.test.IntegrationTest'
        }
    }
}

//project(':test') {
//    task componentTest(type: Test) {
//        useJUnit {
//            includeCategories project.hasProperty('useComponentTestHarness') ?
//                'au.com.peterpal.common.test.ComponentTestHarness' : 'au.com.peterpal.common.test.ComponentTest'
//        }
//    }
//}

//task injectLocalData(type: Exec) {
//    commandLine 'sh', './data/entryPoint.sh', 'local'
//}

//apply plugin: "java"

//jar {
//    enabled = false
//}

wrapper {
    gradleVersion = "6.4.1"
}

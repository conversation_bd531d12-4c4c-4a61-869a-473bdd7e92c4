@startuml
'https://plantuml.com/class-diagram
title Standing Orders Class Diagram

class OrderDefinition
class SelectingRule
class Selection
class Term
class Allocation
class Release
class AllocationPreference
class ReleasePreference
class PendingOrder

class Customer <<external>>
class Supplier <<external>>
class Fund <<external>>
class Branch <<external>>

PendingOrder -- Supplier
PendingOrder -- Release
PendingOrder -- Customer
PendingOrder -- Fund
PendingOrder -- Branch

Customer "1" --> "0..*" Fund
SelectingRule "1" *--> "1..*" Term
Term --> Term
SelectingRule "1" <-- "0..*" OrderDefinition
SelectingRule "1" --> "0..*" Selection

OrderDefinition --> "1" Customer

OrderDefinition "1" *--> "1..*"Allocation

Allocation --> Release : INITIAL
Allocation --> Release : FSFP
note bottom
  FSFP stands for
  FIRST_SMALL_FORMAT_PAPERBACK
end note
Allocation --> Release : REISSUE
Allocation --> "0..*" AllocationPreference

AllocationPreference --> Customer

AllocationPreference --> ReleasePreference
AllocationPreference --> ReleasePreference
AllocationPreference --> ReleasePreference

note left of OrderDefinition
Any order is defined by SelectingRule,
belongs to Customer and has
one or more Allocation
end note

note bottom of Selection: Used to create\nPendingOrder

abstract class AbstractList
abstract AbstractCollection
interface List
interface Collection

List <|-- AbstractList
Collection <|-- AbstractCollection

Collection <|- List
AbstractCollection <|- AbstractList
AbstractList <|-- ArrayList

class ArrayList {
Object[] elementData
size()
}

enum TimeUnit {
DAYS
HOURS
MINUTES
}

hide AbstractList
hide List
hide Collection
hide AbstractCollection
hide ArrayList
hide TimeUnit
@enduml

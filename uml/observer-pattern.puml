@startuml
title Observer Pattern

interface Subject
interface Observer

class ConcreteSubject
class ConcreteObserver

Subject : registerObserver(Observer)
Subject : removeObserver(Observer)
Subject : notifyObservers()

Observer : update()
ConcreteObserver : update()
ConcreteObserver : // otherObserverMethods()

ConcreteSubject : registerObserver(Observer) {…}
ConcreteSubject : removeObserver(Observer) {…}
ConcreteSubject : notifyObservers() {…}

Subject "1" -right-> "*" Observer : "observers"

ConcreteSubject <-left- ConcreteObserver : "subject"
Subject <|– ConcreteSubject
Observer <|– ConcreteObserver
@enduml

@startuml
boundary StandingOrderController
control MatchingService
database ClientWeb
boundary ProductListener

StandingOrderController -> MatchingService : matchNew
MatchingService -> ProductHandler

'Get all active standing orders
MatchingService -> StandingOrderBL : getAll
MatchingService -> StandingOrderObserver : new
MatchingService -> ProductHandler : addObserver
MatchingService -> ClientWeb : getAll
ProductListener -> MatchingService : matchNew
MatchingService -> ProductHandler : match product
ProductHandler -> ProductHandler : notify observers
ProductHandler -> StandingOrderObserver : update
StandingOrderObserver -> StandingOrder : get terms
StandingOrderObserver -> Matcher : match product with terms
@enduml

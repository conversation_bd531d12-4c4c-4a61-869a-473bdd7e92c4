@startuml

title Standing Order - Matching Products

class ProductListener {
}

class Product {
}

interface PropertyChangeListener {
  propertyChange(String property, Object oldValue, Object newValue)
}

class ProductChangeListener {
}

class StandingOrder {
  - Terms terms
  - List<Title> titleList
  - Matcher matcher
}

' Observable is the Subject in the pattern
class PropertyChangeSupport {
  addPropertyChangeListener(String property, ProductChangeListener observer)
  removePropertyChangeListener(String property, ProductChangeListener observer)
  firePropertyChange(String property, Object old, Object new)
}

class MatchingService {
  + void matchNew(Product product)
  + void matchExisting(Product product)
}

class Terms {
}

class Title {
}

class Matcher {
  boolean match(Product product, Terms terms)
}

PropertyChangeSupport -left- PropertyChangeListener
PropertyChangeSupport *-- MatchingService
MatchingService -down- ProductListener
ProductChangeListener *-- StandingOrder
PropertyChangeListener <.. ProductChangeListener
StandingOrder *-right- "1" Terms
ProductChangeListener *-left- Matcher
StandingOrder o-down- "0..*" Title
@enduml

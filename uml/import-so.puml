@startuml

class ImportController {
  + uploadStandingOrders()
  + uploadPreferences()
  + uploadAllocations()
}

abstract class ExcelImportAgent {
  # {abstract} void doImport()
}

class StdOrderImportAgent {
}

class AllocPrefImportAgent {
}

class AllocationImportAgent {
}

class Importer {
  + importStandingOrders()
  + ProcessReport importAllocationPrefs()
}

ExcelImportAgent <|=up= StdOrderImportAgent
ExcelImportAgent <|=up= AllocPrefImportAgent
ExcelImportAgent <|=up= AllocationImportAgent
ImportController *-right- Importer
Importer o-- StdOrderImportAgent
Importer o-- AllocPrefImportAgent
Importer o-- AllocationImportAgent
@enduml

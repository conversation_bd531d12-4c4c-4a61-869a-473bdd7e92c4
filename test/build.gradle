
configurations {
  all {
    exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    exclude group: "ch.qos.logback", module: "logback-classic"
    exclude group: "org.apache.logging.log4j", module: "log4j-to-slf4j"
    exclude group: 'org.apache.geronimo.specs', module: 'geronimo-json_1.0_spec'
    exclude group: 'org.apache.johnzon', module: 'johnzon-core'
  }
}

dependencies {
  testImplementation("au.com.peterpal:integration-test-tools:$integrationTestToolsVersion")
  testImplementation group: 'io.rest-assured', name: 'rest-assured', version: '3.0.3'
  testImplementation group: 'io.rest-assured', name: 'json-schema-validator', version: '3.0.3'
  testImplementation "org.glassfish:javax.json:$jsonpVersion"
}

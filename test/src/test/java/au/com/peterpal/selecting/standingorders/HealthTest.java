package au.com.peterpal.selecting.standingorders;

import au.com.peterpal.common.test.ComponentTest;
import io.restassured.http.ContentType;
import org.hamcrest.CoreMatchers;
import org.junit.BeforeClass;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.junit.runners.MethodSorters;

import static io.restassured.RestAssured.given;
import static org.junit.Assume.assumeThat;

@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@Category(ComponentTest.class)
public class HealthTest {

  @BeforeClass
  public static void init() {
    int statusCode;
    try {
      statusCode =
          given()
              .accept(ContentType.JSON)
              .log()
              .uri()
              .when()
              .get("/standing-orders/actuator/health")
              .statusCode();
    } catch (Exception e) {
      statusCode = 503;
    }
    assumeThat(statusCode, CoreMatchers.is(200));
  }

  public void runAllTestsInOrder() {
    step01_healthy();
  }

  @Test
  public void step01_healthy() {
    given()
        .accept(ContentType.JSON)
        .log()
        .all()
        .when()
        .get("/standing-orders/actuator/health")
        .then()
        .contentType(ContentType.JSON)
        .log()
        .all();
  }
}

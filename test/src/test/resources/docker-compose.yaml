version: '3.5'
services:
  postgres:
    container_name: postgres
    image: "postgres:9.6.9-alpine"
    ports:
    - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 2m
      retries: 10
      start_period: 20s
  artemis:
    container_name: artemis
    image: "vromero/activemq-artemis:2.6.0-alpine"
    ports:
    - "8161:8161"
    - "61616:61616"
    environment:
    - ENABLE_JMX_EXPORTER=true
    healthcheck:
      test: wget --quiet --tries=1 --spider http://localhost:9404 || exit 1
      interval: 10s
      timeout: 1m
      retries: 5
      start_period: 10s
  wiremock:
    container_name: wiremock
    image: "rodolpheche/wiremock:2.18.0-alpine"
    ports:
    - "8081:8080"
    healthcheck:
      test: wget --quiet --tries=1 --spider http://localhost:8080/__admin/mappings || exit 1
      interval: 10s
      timeout: 2m
      retries: 10
      start_period: 10s
  standing-orders-service:
    container_name: standing-orders-service
    image: "docker.peterpal.com.au/standing-orders-service:latest"
    depends_on:
    - postgres
    - artemis
    - wiremock
    ports:
    - "8080:8080"
    healthcheck:
      test: wget --quiet --tries=1 --spider http://localhost:8080/standing-orders/actuator/health || exit 1
      interval: 10s
      timeout: 10m
      retries: 60
      start_period: 20s
networks:
  default:
    driver: bridge
    ipam:
      driver: default
      config:
      - subnet:  ***********/24

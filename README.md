# Standing Orders Service

The standing orders application provides ...

## Prerequisites

* Java 8 - Runtime and build requires Java 8. Won't work with Java 9 due without explicit inclusion of JAX-B module.

## Build

### Java Build

1. Clone the git repo `<NAME_EMAIL>:peterpal/standing-orders-service.git`
2. `cd standing-orders-service`
3. `./gradlew clean build`

### Docker Build

If using the Minikube Docker daemon, configure the Docker environment to match the Minikube instance. Do this with:

```bash
eval $(minikube docker-env)
```

Docker build:

```bash
./gradlew docker
```

## Running

### Minikube

Start a Minikube cluster. All Minikube commands require the Docker environment to be setup to match the Minikube instance. Do this with:

```bash
eval $(minikube docker-env)
```

### Run as a standalone Spring Boot service

Start the Spring Boot service (replace `${version}` with the current version from `build.gradle`) in another console window

```bash
java -Dspring.artemis.host=localhost -Dspring.profiles.active=development -jar standing-orders-service-${version}.jar
```

### Run in Minikube

Apply the Kubernetes deployment that define the runtime objects. The service will start automatically.

```bash
kubectl apply -f kubernetes/deployment.yml
```

Open in a browser

```bash
kubectrl service standing-orders-service
```

## Runtime Configuration

### Port Binding

The service runs on port 8080 by default. To start on another port set the `server.port` system property to the preferred port number.

### Profiles

Select a runtime profile by setting the `spring.profiles.active` system property.

e.g. `java -Dspring.profiles.active=development`

#### Development Profile

The `development` profile uses a H2 in-memory database for simplicity and speed. The H2 database properties are set in `application-development.properties`

H2 Console Path: `/h2-console`

JDBC URL: `jdbc:h2:file:~/h2-despatch-db;DB_CLOSE_ON_EXIT=FALSE;DB_CLOSE_DELAY=-1;AUTO_SERVER=TRUE`

Username: `sa`

Password: Leave empty

## API Documentation

API's are documented online. With the service running in standalone mode, go to: http://localhost:8080/swagger-ui.html . Remember to change the port number in the URL if running on a port other than the default (8080).

Running in Minikube, use `kubectl service standing-orders-service` to open in a browser and append `/swagger-ui.html` to the path.

## Maintenance

Change the version as necessary (see `build.gradle` for the version number).

### IntelliJ Build

Building in IntelliJ requires the following preparation.

* Lombok plugin
* Java Compiler: Additional command line settings `-parameters` (in File | Settings search for "additional command line parameters")
* Annotation Processors: Store generated sources relative to `Module content root` ( in File | Settings search for "store generated sources relative to")

`-parameters` is required by Jackson to deserialize into an immutable value object. This was brought about by a recent change to Jackson.

After applying the above changes, delete the `out` directory and rebuilding before starting the service.
                        
### To run multiple horizontally scaled instances

java    -Dspring.profiles.active=development-postgres -jar  service/build/libs/standing-orders-service-2.0-SNAPSHOT.jar  
java    -Dspring.profiles.active=development-horizontal-scale-2 -jar  service/build/libs/standing-orders-service-2.0-SNAPSHOT.jar  
etc



standingorders.standingOrderNumberMask=SO-%08d
standingorders.standingOrderNumberInitialValue=1
spring.flyway.enabled=false

lucy-api-service.url=http://internal-services-test:8080/lucy/api
catalogue-service.url=http://catalogue.cw-uat.peterpal.local:8280/cat

# Keycloak properties
keycloak.realm = ppls-dev
keycloak.resource = standing-orders-api
keycloak.auth-server-url = http://keycloak.k8s-test.peterpal.local/auth
keycloak.ssl-required = external
keycloak.bearer-only = true
keycloak.cors = true
keycloak.credentials.secret = 21cb2e1b-f015-4982-b2c9-b8fca41c7401
ppls.keycloak.client-id=standing-orders-ui
# Switch on/off security
security.enabled=true

# batch size
spring.jpa.properties.hibernate.jdbc.batch_size=30

standing-orders.post-flyway-script=SELECT 1

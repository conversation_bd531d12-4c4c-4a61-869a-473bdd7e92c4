package au.com.peterpal.selecting.standingorders.pendingorder.control

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus
import au.com.peterpal.selecting.standingorders.allocation.model.Release
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType
import au.com.peterpal.selecting.standingorders.allocationpreference.model.SmallFormatPaperbackRule
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.ext.fund.boundary.dto.FundStatus
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId
import au.com.peterpal.selecting.standingorders.pendingorder.dto.POCreateInfo
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder
import au.com.peterpal.selecting.standingorders.standingorder.dto.ProductInfo
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus
import au.com.peterpal.selecting.standingorders.titles.entity.Title
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId
import  au.com.peterpal.selecting.standingorders.ext.category.entity.Category
import spock.lang.Specification

import java.time.LocalDate
import java.time.Month

class AssignmentRulesSpec extends Specification {

    Customer customer
    Fund fund
    ProductInfo hbProduct
    ProductInfo pbProduct
    Title title
    Allocation allocation
    Category categoryY

    def setup() {
        customer = Customer.of(CustomerId.of(UUID.randomUUID()), "BCC")
        fund = Fund.builder()
                .code("FUND01")
                .customer(customer)
                .fundId(FundId.of("c6aeeb5d-c093-49d2-9c7d-b27099b868b3"))
                .name("FUND01")
                .status(FundStatus.ACTIVE)
                .build()

        hbProduct = ProductInfo.builder()
                .format(ReleaseFormat.HB)
                .isbn("9799156483362")
                .pubDate(LocalDate.of(2022, Month.JULY, 18))
                .build()

        pbProduct = ProductInfo.builder()
                .format(ReleaseFormat.PB)
                .isbn("9792448811911")
                .pubDate(LocalDate.of(2022, Month.JULY, 16))
                .build()

        title = Title.builder()
                .titleId(TitleId.of(UUID.randomUUID()))
                .title("a title")
                .personName("a person name")
                .build()

        categoryY = Category.builder().categoryId(new CategoryId()).code("Y")
                .description("desc").status(CategoryStatus.ACTIVE).build()

        allocation = Allocation.builder()
                .allocationId(AllocationId.of("56921280-ff76-467e-b8be-4e5fe9ab1fa8"))
                .allocationPreference(null)
                .categories(Arrays.asList(categoryY))
                .customerReference("CUSTREF01")
                .customerStandingOrder(CustomerStandingOrder.builder()
                        .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
                        .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                        .customer(customer)
                        .build())
                .deliveryInstructions("deliveryInstructions")
                .fund(fund)
                .status(AllocationStatus.ACTIVE)
                .notes("note")
                .build()

    }

    def "if split, one pending order for one product"() {
        given:
        Release release = Release.builder()
                .releaseId(ReleaseId.of(UUID.randomUUID()))
                .actionType(ActionType.ORDER)
                .fund(null)
                .hardbackfund(null)
                .paperbackfund(null)
                .initialAssignmentRule(AssignmentRule.SPLIT)
                .quantity(3)
                .hardbackQuantity(2)
                .paperbackQuantity(0)
                .preference(null)
                .releaseType(ReleaseType.INITIAL)
                .smallFormatPaperbackRule(SmallFormatPaperbackRule.ACCEPT)
                .build()
        allocation.addRelease(release)
        SplitRule rule = new SplitRule()
        List<ProductInfo> products = Arrays.asList(hbProduct)

        when:
        List<PendingOrder> result = rule.createPOList(POCreateInfo.builder()
                .title(title)
                .products(products)
                .release(release)
                .allocation(allocation)
                .pref(null)
                .build())

        then:
        result != null
        result.size() == 1
        result.get(0).quantity == release.quantity
    }

    def "if split, two pending order for two products"() {
        given:
        Release release = Release.builder()
                .releaseId(ReleaseId.of(UUID.randomUUID()))
                .actionType(ActionType.ORDER)
                .fund(null)
                .hardbackfund(null)
                .paperbackfund(null)
                .initialAssignmentRule(AssignmentRule.SPLIT)
                .quantity(5)
                .hardbackQuantity(2)
                .paperbackQuantity(3)
                .preference(null)
                .releaseType(ReleaseType.INITIAL)
                .smallFormatPaperbackRule(SmallFormatPaperbackRule.ACCEPT)
                .build()
        allocation.addRelease(release)
        SplitRule rule = new SplitRule()
        List<ProductInfo> products = Arrays.asList(hbProduct, pbProduct)

        when:
        List<PendingOrder> result = rule.createPOList(POCreateInfo.builder()
                .title(title)
                .products(products)
                .release(release)
                .allocation(allocation)
                .pref(null)
                .build())

        then:
        result != null
        result.size() == 2

        for (PendingOrder po : result) {
            if (po.getFormat().equals("HB")) {
                po.getQuantity() == release.getPaperbackQuantity()
            } else if (po.getFormat().equals("PB")) {
                po.getQuantity() == release.getPaperbackQuantity()
            }
        }
    }

    def "if first available, one pending order for one product"() {
        given:
        Release release = Release.builder()
                .releaseId(ReleaseId.of(UUID.randomUUID()))
                .actionType(ActionType.ORDER)
                .fund(null)
                .hardbackfund(null)
                .paperbackfund(null)
                .initialAssignmentRule(AssignmentRule.FIRST_AVAILABLE)
                .quantity(3)
                .hardbackQuantity(2)
                .paperbackQuantity(0)
                .preference(null)
                .releaseType(ReleaseType.INITIAL)
                .smallFormatPaperbackRule(SmallFormatPaperbackRule.ACCEPT)
                .build()
        allocation.addRelease(release)
        FirstAvailableRule rule = new FirstAvailableRule()
        List<ProductInfo> products = Arrays.asList(hbProduct)

        when:
        List<PendingOrder> result = rule.createPOList(POCreateInfo.builder()
                .title(title)
                .products(products)
                .release(release)
                .allocation(allocation)
                .pref(null)
                .build())

        then:
        result != null
        result.size() == 1
        result.get(0).quantity == release.getQuantity()
    }

    def "if first available, one pending order for two products"() {
        given:
        Release release = Release.builder()
                .releaseId(ReleaseId.of(UUID.randomUUID()))
                .actionType(ActionType.ORDER)
                .fund(null)
                .hardbackfund(null)
                .paperbackfund(null)
                .initialAssignmentRule(AssignmentRule.SPLIT)
                .quantity(5)
                .hardbackQuantity(2)
                .paperbackQuantity(3)
                .preference(null)
                .releaseType(ReleaseType.INITIAL)
                .smallFormatPaperbackRule(SmallFormatPaperbackRule.ACCEPT)
                .build()
        allocation.addRelease(release)
        FirstAvailableRule rule = new FirstAvailableRule()
        List<ProductInfo> products = Arrays.asList(hbProduct, pbProduct)

        when:
        List<PendingOrder> result = rule.createPOList(POCreateInfo.builder()
                .title(title)
                .products(products)
                .release(release)
                .allocation(allocation)
                .pref(null)
                .build())

        then:
        result != null
        result.size() == 1
        result.get(0).getQuantity() == release.getQuantity()
    }
}

package au.com.peterpal.selecting.standingorders.titles.control

import au.com.peterpal.common.audit.EventPublisher
import au.com.peterpal.common.rest.validation.BusinessException
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationService
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus
import au.com.peterpal.selecting.standingorders.allocation.model.Release
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.Format
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderBL
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderService
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderBL
import au.com.peterpal.selecting.standingorders.standingorder.dto.ProductInfo
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus
import au.com.peterpal.selecting.standingorders.titles.dto.BulkUpdateTitleRequest
import au.com.peterpal.selecting.standingorders.titles.dto.UpdateMatchedProductFormatRequest
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProduct
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProductId
import au.com.peterpal.selecting.standingorders.titles.entity.ProductAggregated
import au.com.peterpal.selecting.standingorders.titles.entity.ProductAggregatedId
import au.com.peterpal.selecting.standingorders.titles.entity.Title
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus
import org.assertj.core.util.Lists
import spock.lang.Specification

import java.time.LocalDate

class TitleBLTest extends Specification {
    TitleBL titleBL

    TitleRepository titleRepository = Mock(TitleRepository.class)
    MatchedProductRepository matchedProductRepository = Mock(MatchedProductRepository.class)
    ProductAggregatedRepository productAggregatedRepository = Mock(ProductAggregatedRepository.class)

    def "map format by formCode"() {
        given:
        def title = Title.builder()
                .titleId(new TitleId())
                .title("a title")
                .personName("a person name")
                .build()

        def matchedProduct = MatchedProduct.builder()
                .matchedProductId(new MatchedProductId())
                .title(title)
                .build()

        titleBL = new TitleBL(titleRepository, matchedProductRepository, productAggregatedRepository, Mock(StandingOrderBL.class),
                Mock(PendingOrderBL.class), Mock(PendingOrderService.class), Mock(TitleService.class),
                Mock(EventPublisher.class), Mock(AllocationService.class), Mock(RelatedTitleBL.class))

        when:
        def request = UpdateMatchedProductFormatRequest.builder()
                .matchedProductId(matchedProduct.getMatchedProductId())
                .format(Format.PAPERBACK)
                .build()
        def response = titleBL.updateMatchedProductFormat(title.getTitleId(), request)

        then:
        response.matchedProductId == matchedProduct.getMatchedProductId()
        response.format == Format.PAPERBACK
        1 * matchedProductRepository.saveAndFlush(_) >> matchedProduct
        1 * matchedProductRepository.findByTitleTitleIdAndMatchedProductId(title.getTitleId(), matchedProduct.getMatchedProductId()) >> Optional.of(matchedProduct)
    }

    def "format validation valid product selection"() {
        given:
        titleBL = new TitleBL(titleRepository, matchedProductRepository, productAggregatedRepository, Mock(StandingOrderBL.class),
                Mock(PendingOrderBL.class), Mock(PendingOrderService.class), Mock(TitleService.class),
                Mock(EventPublisher.class), Mock(AllocationService.class), Mock(RelatedTitleBL.class))

        when:
        List<ProductAggregated> products = Lists.newArrayList(ProductAggregated.builder()
                .productAggregatedId(new ProductAggregatedId())
                .matchedProduct(MatchedProduct.builder()
                        .matchedProductId(new MatchedProductId())
                        .format(Format.HARDBACK)
                        .isbn("87836582638")
                        .publicationDate(LocalDate.now())
                        .build())
                .build())
        titleBL.validateProductSelection(products)

        then:
        noExceptionThrown()
    }

    def "format validation invalid product selection"() {
        given:
        titleBL = new TitleBL(titleRepository, matchedProductRepository, productAggregatedRepository, Mock(StandingOrderBL.class),
                Mock(PendingOrderBL.class), Mock(PendingOrderService.class), Mock(TitleService.class),
                Mock(EventPublisher.class), Mock(AllocationService.class), Mock(RelatedTitleBL.class))

        when:
        List<ProductInfo> products = Lists.newArrayList(
                ProductAggregated.builder()
                        .productAggregatedId(new ProductAggregatedId())
                        .matchedProduct(MatchedProduct.builder()
                                .matchedProductId(new MatchedProductId())
                                .format(Format.HARDBACK)
                                .isbn("87836582638")
                                .publicationDate(LocalDate.now())
                                .build())
                        .build(),
                ProductAggregated.builder()
                        .productAggregatedId(new ProductAggregatedId())
                        .matchedProduct(MatchedProduct.builder()
                                .matchedProductId(new MatchedProductId())
                                .format(Format.HARDBACK)
                                .isbn("***********")
                                .publicationDate(LocalDate.now())
                                .build())
                        .build())
        titleBL.validateProductSelection(products)

        then:
        def e = thrown(BusinessException)
        e.message == "Only one product can be selected for each format. [2 product(s) selected for HARDBACK.]"
    }

    def "allocation validation contains zero release qty"() {
        given:
        titleBL = new TitleBL(titleRepository, matchedProductRepository, productAggregatedRepository, Mock(StandingOrderBL.class),
                Mock(PendingOrderBL.class), Mock(PendingOrderService.class), Mock(TitleService.class),
                Mock(EventPublisher.class), Mock(AllocationService.class), Mock(RelatedTitleBL.class))

        def category = Category.builder().categoryId(new CategoryId()).code("AN").status(CategoryStatus.ACTIVE).build()

        when:
        List<Allocation> allocations = Lists.newArrayList(
                Allocation.builder()
                        .allocationId(new AllocationId())
                        .categories(List.of(category))
                        .customerStandingOrder(CustomerStandingOrder.builder()
                                .customerStandingOrderId(new CustomerStandingOrderId())
                                .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                                .customer(Customer.builder()
                                        .name("CUST-TEST01")
                                        .customerId(CustomerId.of(UUID.randomUUID()))
                                        .code("CUST-CODE01")
                                        .build())
                                .standingOrder(StandingOrder.builder()
                                        .standingOrderId(new StandingOrderId())
                                        .standingOrderNumber("SO-001")
                                        .standingOrderStatus(StandingOrderStatus.ACTIVE)
                                        .build())
                                .build())
                        .status(AllocationStatus.ACTIVE)
                        .releases(Lists.newArrayList(Release.builder()
                                .releaseId(new ReleaseId())
                                .releaseType(ReleaseType.INITIAL)
                                .quantity(0)
                                .build()))
                        .build())
        titleBL.validateAllocation(allocations, category)

        then:
        def e = thrown(BusinessException)
        e.message == "Standing Order selection contain zero quantity allocation: [SO-001 CUST-CODE01 ([AN])]"
    }

    def "allocation validation no allocation found"() {
        given:
        titleBL = new TitleBL(titleRepository, matchedProductRepository, productAggregatedRepository, Mock(StandingOrderBL.class),
                Mock(PendingOrderBL.class), Mock(PendingOrderService.class), Mock(TitleService.class),
                Mock(EventPublisher.class), Mock(AllocationService.class), Mock(RelatedTitleBL.class))

        when:
        List<Allocation> allocations = Lists.newArrayList()
        titleBL.validateAllocation(allocations, Category.builder().categoryId(new CategoryId()).code("AN").status(CategoryStatus.ACTIVE).build())

        then:
        def e = thrown(BusinessException)
        e.message == "No active allocations found for title with category AN"
    }

    def "undoTitle"() {
        def titleService = Mock(TitleService.class)
        def pendingOrderBL = Mock(PendingOrderBL.class)
        def pendingOrderService = Mock(PendingOrderService.class)
        given:
        titleBL = new TitleBL(titleRepository, matchedProductRepository, productAggregatedRepository, Mock(StandingOrderBL.class),
                pendingOrderBL, pendingOrderService, titleService,
                Mock(EventPublisher.class), Mock(AllocationService.class), Mock(RelatedTitleBL.class))

        def title = Title.builder()
                .titleId(new TitleId())
                .title("a title")
                .personName("a person name")
                .titleStatus(initialStatus)
                .build()

        when:
        def response = titleBL.undoTitle(title.titleId, "test")
        def matchedSOStatus = title.standingOrderAggregatedList.status.stream().distinct()
        def matchedProductStatus = title.productAggregatedList.status.stream().distinct()

        newStatus == title.titleStatus
        rejectionReasonType == title.rejectionReasonType
        rejectionOtherReason == title.rejectionOtherReason
        selectedSO == matchedSOStatus
        selectedProduct == matchedProductStatus

        then:
        titleService.findTitleById(_ as TitleId) >> title
        pendingOrderService.findByTitleTitleIdAndOrderStatus(_ as TitleId, _ as PendingOrderStatus) >> [Mock(PendingOrder.class)]
        timesDeletePendingOrde * pendingOrderBL.deletePendingOrders(_)

        where:
        initialStatus        | newStatus       | rejectionReasonType | rejectionOtherReason | selectedSO | selectedProduct | timesDeletePendingOrde
        TitleStatus.PENDING  | TitleStatus.NEW | null                | null                 | null       | null            | 1
        TitleStatus.REJECTED | TitleStatus.NEW | null                | null                 | null       | null            | 0
    }

    def "throw Exception when bulk title with invalid status"() {
        def titleService = Mock(TitleService.class)
        def pendingOrderBL = Mock(PendingOrderBL.class)
        def pendingOrderService = Mock(PendingOrderService.class)
        given:
        titleBL = new TitleBL(titleRepository, matchedProductRepository, productAggregatedRepository, Mock(StandingOrderBL.class),
                pendingOrderBL, pendingOrderService, titleService,
                Mock(EventPublisher.class), Mock(AllocationService.class), Mock(RelatedTitleBL.class))


        def titleId = new TitleId()
        def title = Title.builder()
                .titleId(titleId)
                .title("a title")
                .personName("a person name")
                .titleStatus(initialStatus)
                .build()
        BulkUpdateTitleRequest bulkUpdateTitleRequest = BulkUpdateTitleRequest.builder()
                .includedTitleIds([titleId])
                .updatedStatus(updatedStatus)
                .updatedCategory("AF")
                .build()

        when:
        titleBL.bulkEdit(bulkUpdateTitleRequest, "test")


        then:
        titleRepository.findAllById(_ as List) >> [title]
        pendingOrderService.findAllValidPendingOrdersByTitle(_ as TitleId) >> [Mock(PendingOrder.class)]
        def e = thrown(BusinessException)
        e.message == message

        where:
        initialStatus         | updatedStatus        | message
        TitleStatus.PROCESSED | TitleStatus.NEW      | "Cannot change the status of included PROCESSED titles"
        TitleStatus.PAUSED    | TitleStatus.NEW      | "Cannot change the status of included PAUSED titles"
        TitleStatus.PROCESSED | TitleStatus.REJECTED | "Cannot change the status of included PROCESSED titles"
        TitleStatus.NEW       | TitleStatus.REJECTED | "Cannot change the category with updated status REJECTED"
        TitleStatus.REJECTED  | null                 | "Assigning category only applied to a NEW title or PENDING title that has no pending orders."
        TitleStatus.PENDING   | null                 | "Assigning category only applied to a NEW title or PENDING title that has no pending orders."
    }
}

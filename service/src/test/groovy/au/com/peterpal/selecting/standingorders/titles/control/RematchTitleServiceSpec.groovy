package au.com.peterpal.selecting.standingorders.titles.control

import au.com.peterpal.common.audit.EventPublisher
import au.com.peterpal.selecting.standingorders.catalog.model.ContributorRoleCode
import au.com.peterpal.selecting.standingorders.catalog.model.LanguageCode
import au.com.peterpal.selecting.standingorders.catalog.model.ProductFormCode
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus
import au.com.peterpal.selecting.standingorders.gateways.clientweb.ClientWebApiGateway
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderBL
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderService
import au.com.peterpal.selecting.standingorders.standingorder.control.MatchingService
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderRepository
import au.com.peterpal.selecting.standingorders.standingorder.control.TermRepository
import au.com.peterpal.selecting.standingorders.standingorder.events.MatchFound
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductMatchInfo
import au.com.peterpal.selecting.standingorders.standingorder.model.CombinedTerm
import au.com.peterpal.selecting.standingorders.standingorder.model.CombinedTermId
import au.com.peterpal.selecting.standingorders.standingorder.model.OperationType
import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonStatus
import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonType
import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonTypeId
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus
import au.com.peterpal.selecting.standingorders.standingorder.model.Term
import au.com.peterpal.selecting.standingorders.standingorder.model.TermId
import au.com.peterpal.selecting.standingorders.standingorder.model.TermType
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProduct
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProductId
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedStandingOrder
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedStandingOrderId
import au.com.peterpal.selecting.standingorders.titles.entity.ProductAggregated
import au.com.peterpal.selecting.standingorders.titles.entity.ProductAggregatedId
import au.com.peterpal.selecting.standingorders.titles.entity.ProductAggregatedStatus
import au.com.peterpal.selecting.standingorders.titles.entity.StandingOrderAggregated
import au.com.peterpal.selecting.standingorders.titles.entity.StandingOrderAggregatedId
import au.com.peterpal.selecting.standingorders.titles.entity.StandingOrderAggregatedStatus
import au.com.peterpal.selecting.standingorders.titles.entity.Title
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus
import com.google.common.collect.Lists
import spock.lang.Specification

import java.time.LocalDate

class RematchTitleServiceSpec extends Specification {
    MatchAndMergeTitleService matchAndMergeTitleService = Mock(MatchAndMergeTitleService.class)
    TitleService titleService = Mock(TitleService.class)
    PendingOrderService pendingOrderService = Mock(PendingOrderService.class)
    PendingOrderBL pendingOrderBL = Mock(PendingOrderBL.class)
    MatchingService matchingService = Mock(MatchingService.class)
    StandingOrderRepository standingOrderRepository = Mock(StandingOrderRepository.class)
    TermRepository termRepository = Mock(TermRepository.class)
    ClientWebApiGateway clientWebApiGateway = Mock(ClientWebApiGateway.class)
    EventPublisher eventPublisher = Mock(EventPublisher.class)

    RematchTitleService rematchTitleService
    Title baseTitle
    MatchedProduct matchedProduct1
    StandingOrder so1
    StandingOrder so2
    MatchedStandingOrder matchedSO1
    Category AF
    Term term1
    Term term2
    ProductMatchInfo productInfo
    RejectionReasonType rejectionReasonType

    def setup() {
        rematchTitleService = new RematchTitleService(matchAndMergeTitleService,
                titleService,
                pendingOrderService,
                pendingOrderBL,
                matchingService,
                standingOrderRepository,
                termRepository,
                clientWebApiGateway,
                eventPublisher)

        so1 = StandingOrder.builder()
                .description("standingOrderDescription1")
                .notes("notes1")
                .standingOrderId(new StandingOrderId())
                .standingOrderNumber("SON01")
                .standingOrderStatus(StandingOrderStatus.ACTIVE)
                .build()
        so2 = StandingOrder.builder()
                .description("standingOrderDescription2")
                .notes("notes2")
                .standingOrderId(new StandingOrderId())
                .standingOrderNumber("SON02")
                .standingOrderStatus(StandingOrderStatus.ACTIVE)
                .build()
        matchedSO1 = MatchedStandingOrder.builder()
                .matchedStandingOrderId(new MatchedStandingOrderId())
                .standingOrder(so1)
                .termId(Term.DEFAULT.getTermId())
                .build()
        AF = Category.builder()
                .categoryId(new CategoryId())
                .code("AF")
                .status(CategoryStatus.ACTIVE)
                .build()
        matchedProduct1 = MatchedProduct.builder()
                .matchedProductId(new MatchedProductId())
                .catalogueId(1)
                .isbn("9783956794605")
                .publicationDate(LocalDate.of(2023, 12, 10))
                .category(AF)
                .matchedStandingOrders(Lists.newArrayList(matchedSO1))
                .build()

        baseTitle = Title.builder()
                .titleStatus(TitleStatus.NEW)
                .titleId(new TitleId())
                .title("a title")
                .personName("a person name")
                .build()

        term1 = Term.builder()
                .standingOrder(so1)
                .operation(OperationType.CONTAINS)
                .type(TermType.TITLE)
                .termId(TermId.of(UUID.randomUUID()))
                .value("Title")
                .combinedTerm(
                        CombinedTerm.builder()
                                .combinedTermId(CombinedTermId.of(UUID.randomUUID()))
                                .build())
                .build()

        term2 = Term.builder()
                .standingOrder(so2)
                .operation(OperationType.CONTAINS)
                .type(TermType.TITLE)
                .termId(TermId.of(UUID.randomUUID()))
                .value("Title")
                .combinedTerm(
                        CombinedTerm.builder()
                                .combinedTermId(CombinedTermId.of(UUID.randomUUID()))
                                .build())
                .build()

        productInfo = ProductMatchInfo.builder()
                .languageCode(LanguageCode.ENG)
                .formCode(ProductFormCode.BB)
                .productReference(matchedProduct1.isbn)
                .contributors(Arrays.asList(ProductMatchInfo.ContributorInfo.builder()
                        .personNameInverted("Eko, Firdaus")
                        .role(ContributorRoleCode.A01)
                        .build()))
                .build()

        rejectionReasonType = RejectionReasonType.builder()
                .rejectionReasonTypeId(RejectionReasonTypeId.of("PRODUCT_UPDATED"))
                .name("Rematch").status(RejectionReasonStatus.ACTIVE).build()
    }

    def "Rematch Title with Product no longer result match"() {
        def productAggregated = ProductAggregated.builder()
                .productAggregatedId(new ProductAggregatedId())
                .matchedProduct(matchedProduct1).build()

        def standingOrderAggregated = StandingOrderAggregated.builder()
                .standingOrderAggregatedId(new StandingOrderAggregatedId())
                .standingOrder(so1)
                .build()
        given:
        def title = baseTitle
                .withMatchedProducts([matchedProduct1])
                .withProductAggregatedList([productAggregated])
                .withStandingOrderAggregatedList([standingOrderAggregated])
        def productInfo = productInfo.withProductReference(matchedProduct1.isbn)

        when:
        def result = rematchTitleService.rematchTitleForProductUpdate(title.titleId, productInfo, "system")

        then:
        titleService.findTitleById(_ as TitleId) >> title
        1 * matchingService.match(productInfo) >> []
        titleService.findTitleById(_ as TitleId) >> { title }
        3 * titleService.update(_ as Title, "system") >> { title }
        1 * titleService.rejectTitle(_, _, _, _, _) >> { title.withTitleStatus(TitleStatus.REJECTED).withRejectionReasonType(rejectionReasonType) }

        result.title.titleId == title.titleId
        result.title.getActiveMatchProducts() == []
        result.title.productAggregatedList == [productAggregated.withStatus(ProductAggregatedStatus.REJECTED)]
        result.title.standingOrderAggregatedList == [standingOrderAggregated.withStatus(StandingOrderAggregatedStatus.REJECTED)]
        result.title.titleStatus == TitleStatus.REJECTED
        result.title.rejectionReasonType == rejectionReasonType
        result.productsNoLongerMatched == [matchedProduct1]
        result.newMatchedStandingOrders == []
        result.standingOrdersNoLongerMatched == [matchedSO1]
        result.invalidatedPendingOrders == null
    }


    def "Rematch Title with Product result match"() {
        def standingOrderAggregated = StandingOrderAggregated.builder()
                .standingOrderAggregatedId(new StandingOrderAggregatedId())
                .standingOrder(so1)
                .build()
        given:
        def title = baseTitle
                .withMatchedProducts([matchedProduct1])
                .withProductAggregatedList([ProductAggregated.builder()
                                                    .productAggregatedId(new ProductAggregatedId())
                                                    .matchedProduct(matchedProduct1).build()])
                .withStandingOrderAggregatedList([standingOrderAggregated])
        def productInfo = productInfo.withProductReference(matchedProduct1.isbn)
                .withPublicationDate(LocalDate.of(2023, 12, 15))

        when:
        def result = rematchTitleService.rematchTitleForProductUpdate(title.titleId, productInfo, "system")

        then:
        titleService.findTitleById(_ as TitleId) >> title
        1 * matchingService.match(productInfo) >> [MatchFound.builder().soId(so2.standingOrderId.id).matchedTermId(term1.getTermId()).build()]
        titleService.findTitleById(_ as TitleId) >> { title }
        3 * titleService.update(_ as Title, "system") >> { title }
        1 * matchAndMergeTitleService.merge(_ as Title) >> { title }
        1 * termRepository.findById(_) >> { Optional.of(term1) }
        1 * standingOrderRepository.getOne(so2.getStandingOrderId()) >> { so2 }

        result.title.titleId == title.titleId
        result.title.getActiveMatchProducts() == [matchedProduct1]
        result.title.productAggregatedList.matchedProduct == [matchedProduct1]
        result.title.standingOrderAggregatedList == [standingOrderAggregated.withStatus(StandingOrderAggregatedStatus.REJECTED)]
        result.title.titleStatus == TitleStatus.NEW
        result.productsNoLongerMatched == []
        result.newMatchedStandingOrders.standingOrder == [so2]
        result.standingOrdersNoLongerMatched.standingOrder == [so1]
        result.invalidatedPendingOrders == null
    }
}

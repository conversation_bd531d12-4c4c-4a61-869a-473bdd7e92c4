package au.com.peterpal.selecting.standingorders.standingorder.match

import au.com.peterpal.selecting.standingorders.catalog.model.LanguageCode
import au.com.peterpal.selecting.standingorders.standingorder.model.CombinedTermId
import au.com.peterpal.selecting.standingorders.standingorder.model.OperationType
import au.com.peterpal.selecting.standingorders.standingorder.model.SOSummary
import au.com.peterpal.selecting.standingorders.standingorder.model.Term
import au.com.peterpal.selecting.standingorders.standingorder.model.TermType
import spock.lang.Specification
import spock.lang.Unroll

class LegacyMatcherSpecs extends Specification {
    @Unroll
    def "match by Title: #title #subtitle Terms: #operation - #termValue"() {
        given:
        LegacyMatcher cut = LegacyMatcher.of(Mock(SOSummary.class), null, null, null, "[()’']", "[.-]+");
        Term term = Term.from(termValue, TermType.TITLE, operation, CombinedTermId.of(UUID.randomUUID()))
        ProductMatchInfo product = ProductMatchInfo.builder()
                .id(1)
                .productReference("97867565327")
                .title(ProductMatchInfo.TitleInfo.builder()
                        .withoutPrefix(title)
                        .subtitle(subtitle)
                        .build())
                .languageCode(LanguageCode.ENG)
                .build()
        when:
        def response = cut.matchTitle(term, product)

        then:
        response == isMatch

        where:
        termValue                                  | operation                 | title       | subtitle                        | isMatch
        "Star Wars"                                | OperationType.CONTAINS    | "Star Wars" | "Cataclysm (The High Republic)" | true
        "Star Wars"                                | OperationType.STARTS_WITH | "Star Wars" | "Cataclysm (The High Republic)" | true
        "Star Wars"                                | OperationType.ENDS_WITH   | "Star Wars" | "Cataclysm (The High Republic)" | false
        "Star Wars"                                | OperationType.EXACT_MATCH | "Star Wars" | "Cataclysm (The High Republic)" | false
        "Star Wars"                                | OperationType.CONTAINS    | "Star Wars" | null                            | true
        "Star Wars"                                | OperationType.STARTS_WITH | "Star Wars" | null                            | true
        "Star Wars"                                | OperationType.ENDS_WITH   | "Star Wars" | null                            | true
        "Star Wars"                                | OperationType.EXACT_MATCH | "Star Wars" | null                            | true
        "Star Wars Cataclysm "                     | OperationType.CONTAINS    | "Star Wars" | "Cataclysm (The High Republic)" | true
        "Star Wars Cataclysm "                     | OperationType.CONTAINS    | "Star Wars" | "The High Republic"             | false
        "Star Wars Cataclysm "                     | OperationType.CONTAINS    | "Star Wars" | null                            | false
        "Star Wars Cataclysm (The High Republic) " | OperationType.EXACT_MATCH | "Star Wars" | "Cataclysm (The High Republic)" | true
        "Star Wars Cataclysm "                     | OperationType.EXACT_MATCH | "Star Wars" | "Cataclysm (The High Republic)" | false

    }
}

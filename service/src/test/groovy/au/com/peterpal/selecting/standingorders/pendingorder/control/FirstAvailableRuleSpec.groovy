package au.com.peterpal.selecting.standingorders.pendingorder.control

import au.com.peterpal.selecting.standingorders.allocation.model.*
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId
import au.com.peterpal.selecting.standingorders.ext.branch.entity.Branch
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchStatus
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.pendingorder.dto.CopyPendingOrderDto
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus
import au.com.peterpal.selecting.standingorders.titles.entity.Title
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus
import au.com.peterpal.selecting.standingorders.titles.entity.TitleType
import org.assertj.core.util.Lists
import spock.lang.Shared
import spock.lang.Specification

import java.util.stream.Collectors

class FirstAvailableRuleSpec extends Specification {
    FirstAvailableRule cut

    def "copy"() {
        given:
        def allocation = baseAllocation
                .withReleases(Lists.newArrayList(baseRelease.withQuantity(allocationQty)))
                .withAllocationId(new AllocationId())


        poDetail = CopyPendingOrderDto.PendingOrderDetail.builder()
                .isbn("isbn")
                .format(ReleaseFormat.HB)
                .quantityTaken(qtyTaken)
                .brantchQtyTakenMap(branchQtyTaken)
                .build()

        request = CopyPendingOrderDto.builder()
                .title(baseTitle)
                .allocation(allocation)
                .branchAllocations(branchAllocations)
                .pendingOrderDetailMap([(ReleaseFormat.HB): poDetail])
                .build()

        when:
        def response = cut.copy(request)

        then:
        response[0].quantity == newQty
        response[0].branchDistributions
                .stream()
                .map { b -> b.quantity }
                .sorted()
                .collect(Collectors.toList()) == branchDistributions

        where:
        allocationQty | branchAllocations                                                                | qtyTaken | branchQtyTaken                             | newQty | branchDistributions
        5             | [baseBranchAllocation
                                 .withReleases([baseRelease.withQuantity(2)]).withAllocationId(branch1),
                         baseBranchAllocation
                                 .withReleases([baseRelease.withQuantity(3)]).withAllocationId(branch2)] | 3        | [(branch1): 1, (branch2): 2]               | 2      | [1, 1]
        4             | [baseBranchAllocation
                                 .withReleases([baseRelease.withQuantity(2)]).withAllocationId(branch1),
                         baseBranchAllocation
                                 .withReleases([baseRelease.withQuantity(2)]).withAllocationId(branch2)] | 3        | [(branch1): 1, (branch2): 2]               | 1      | [1]
        6             | [baseBranchAllocation
                                 .withReleases([baseRelease.withQuantity(2)]).withAllocationId(branch1),
                         baseBranchAllocation
                                 .withReleases([baseRelease.withQuantity(2)]).withAllocationId(branch2),
                         baseBranchAllocation
                                 .withReleases([baseRelease.withQuantity(2)]).withAllocationId(branch3)] | 4        | [(branch1): 2, (branch2): 2]               | 2      | [2]
        6             | [baseBranchAllocation
                                 .withReleases([baseRelease.withQuantity(2)]).withAllocationId(branch1),
                         baseBranchAllocation
                                 .withReleases([baseRelease.withQuantity(2)]).withAllocationId(branch2),
                         baseBranchAllocation
                                 .withReleases([baseRelease.withQuantity(2)]).withAllocationId(branch3)] | 4        | [(branch1): 1, (branch2): 2, (branch2): 1] | 2      | [1, 1]

    }

    @Shared
    Allocation baseAllocation = Allocation.builder()
            .allocationId(new AllocationId())
            .categories(List.of(Category.builder().categoryId(new CategoryId()).code("AN").status(CategoryStatus.ACTIVE).build()))
            .customerStandingOrder(CustomerStandingOrder.builder()
                    .customerStandingOrderId(new CustomerStandingOrderId())
                    .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                    .customer(Customer.builder()
                            .name("CUST-TEST01")
                            .customerId(CustomerId.of(UUID.randomUUID()))
                            .code("CUST-CODE01")
                            .build())
                    .standingOrder(StandingOrder.builder()
                            .standingOrderId(new StandingOrderId())
                            .standingOrderNumber("SO-001")
                            .standingOrderStatus(StandingOrderStatus.ACTIVE)
                            .build())
                    .build())
            .status(AllocationStatus.ACTIVE)
            .build()

    @Shared
    Allocation baseBranchAllocation = Allocation.builder()
            .allocationId(new AllocationId())
            .categories(List.of(Category.builder().categoryId(new CategoryId()).code("AN").status(CategoryStatus.ACTIVE).build()))
            .customerStandingOrder(CustomerStandingOrder.builder()
                    .customerStandingOrderId(new CustomerStandingOrderId())
                    .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                    .customer(Customer.builder()
                            .name("CUST-TEST01")
                            .customerId(CustomerId.of(UUID.randomUUID()))
                            .code("CUST-CODE01")
                            .build())
                    .standingOrder(StandingOrder.builder()
                            .standingOrderId(new StandingOrderId())
                            .standingOrderNumber("SO-001")
                            .standingOrderStatus(StandingOrderStatus.ACTIVE)
                            .build())
                    .build())
            .branch(Branch.builder()
                    .branchId(new BranchId())
                    .code("code")
                    .name("name")
                    .status(BranchStatus.ACTIVE)
                    .build())
            .status(AllocationStatus.ACTIVE)
            .build()

    @Shared
    Release baseRelease = Release.builder()
            .releaseId(new ReleaseId())
            .quantity(5)
            .releaseType(ReleaseType.INITIAL)
            .build()

    @Shared
    Title baseTitle = Title.builder()
            .titleId(new TitleId())
            .titleNumber("T001")
            .type(TitleType.ORIGINAL)
            .title("a title")
            .personName("a person name")
            .titleStatus(TitleStatus.PROCESSED)
            .build()

    CopyPendingOrderDto request
    def poDetail
    @Shared
    def branch1 = new AllocationId()
    @Shared
    def branch2 = new AllocationId()
    @Shared
    def branch3 = new AllocationId()

    def setup() {
        cut = new FirstAvailableRule()
    }
}

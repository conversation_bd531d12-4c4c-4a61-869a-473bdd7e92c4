package au.com.peterpal.selecting.standingorders.allocation.control

import au.com.peterpal.selecting.standingorders.TestFixture
import au.com.peterpal.selecting.standingorders.allocation.dto.SearchAllocationRequest
import au.com.peterpal.selecting.standingorders.allocation.dto.SearchAllocationResponse
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus
import au.com.peterpal.selecting.standingorders.allocationpreference.control.AllocationPreferenceRepository
import au.com.peterpal.selecting.standingorders.allocationpreference.control.ReleasePreferenceRepository
import au.com.peterpal.selecting.standingorders.ext.category.control.CategoryRepository
import au.com.peterpal.selecting.standingorders.customerstandingorder.control.CustomerStandingOrderRepository
import au.com.peterpal.selecting.standingorders.customerstandingorder.control.ReleaseRepository
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerRepository
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundRepository
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderRepository
import au.com.peterpal.selecting.standingorders.titles.control.TitleRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import spock.lang.Specification
import spock.lang.Unroll

@DataJpaTest
class AllocationRepositorySpec extends Specification {

    @Autowired
    AllocationRepository allocationRepository
    @Autowired
    CustomerStandingOrderRepository customerStandingOrderRepository
    @Autowired
    StandingOrderRepository standingOrderRepository
    @Autowired
    AllocationPreferenceRepository allocationPreferenceRepository
    @Autowired
    ReleasePreferenceRepository releasePreferenceRepository
    @Autowired
    ReleaseRepository releaseRepository
    @Autowired
    CustomerRepository customerRepository
    @Autowired
    FundRepository fundRepository
    @Autowired
    TitleRepository titleRepository
    @Autowired
    CategoryRepository categoryRepository
    TestFixture testFixture

    def setup() {
        testFixture =
                new TestFixture(
                        releasePreferenceRepository,
                        allocationPreferenceRepository,
                        customerRepository,
                        standingOrderRepository,
                        customerStandingOrderRepository,
                        releaseRepository,
                        allocationRepository,
                        fundRepository,
                        titleRepository,
                        categoryRepository)

        testFixture.initData()
    }

    @Unroll
    def "Search allocations #searchBy"() {
        given:
        Pageable pageRequest = PageRequest.of(0, 10)

        when:
        def result = allocationRepository.search(request, pageRequest)

        then:
        result.totalElements == totalElements

        where:
        searchBy                             | request                                                                                                     | totalElements
        ''                                   | new SearchAllocationRequest(null, null, null, null, null, null, null, null, null, null, null, null, null, null)                           | 1
        'by customer'                        | new SearchAllocationRequest(AllocationStatus.ACTIVE, 'CUST01', null, null, null, null, null, null, null, null, null, null, null, null)    | 1
        'by category'                        | new SearchAllocationRequest(AllocationStatus.ACTIVE, null, ['Z'], null, null, null, null, null, null, null, null, null, null, null)         | 0
        'by fund'                            | new SearchAllocationRequest(AllocationStatus.ACTIVE, null, null, 'FUND01', null, null, null, null, null, null, null, null, null, null)    | 1
        'by customer reference'              | new SearchAllocationRequest(AllocationStatus.ACTIVE, null, null, null, 'CUSTREF02', null, null, null, null, null, null, null, null, null) | 0
        'by category and customer reference' | new SearchAllocationRequest(AllocationStatus.ACTIVE, null, ['Y'], null, 'CUSTREF02', null, null, null, null, null, null, null, null, null)  | 0
    }

    def "Find customers with allocation"() {
        when:
        def result = allocationRepository.findCustomersWithAllocation()

        then:
        result.size() == 3
        result[0] == new SearchAllocationResponse.CustomerResponse(customerId: CustomerId.of('a723df17-a4f7-4198-842f-418d017817ac'), code: 'CUST01')
    }
}

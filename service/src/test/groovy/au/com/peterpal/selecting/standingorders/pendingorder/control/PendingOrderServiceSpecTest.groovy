package au.com.peterpal.selecting.standingorders.pendingorder.control

import au.com.peterpal.common.audit.EventPublisher
import au.com.peterpal.selecting.standingorders.allocation.model.*
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.ext.order.control.OrderService
import au.com.peterpal.selecting.standingorders.ext.order.dto.DuplicateOrder
import au.com.peterpal.selecting.standingorders.pendingorder.dto.PendingOrderResponse
import au.com.peterpal.selecting.standingorders.pendingorder.dto.PendingOrderStandingOrder
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId
import au.com.peterpal.selecting.standingorders.titles.entity.Title
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import spock.lang.Specification

class PendingOrderServiceSpecTest extends Specification {
    PendingOrderRepository pendingOrderRepository = Mock(PendingOrderRepository.class)
    OrderService orderService = Mock(OrderService.class)
    EventPublisher eventPublisher = Mock(EventPublisher.class)
    PendingOrderService pendingOrderService

    def setup() {
        pendingOrderService = new PendingOrderService(pendingOrderRepository, orderService, eventPublisher)
    }


    def "Search pending orders by title"() {
        given:
        Pageable pageRequest = PageRequest.of(0, 10)
        TitleId titleId = new TitleId()
        PendingOrder pendingOrder = PendingOrder.builder()
                .pendingOrderId(new PendingOrderId())
                .quantity(1)
                .format(ReleaseFormat.HB)
                .orderStatus(PendingOrderStatus.NEW)
                .customer(Customer.of(new CustomerId(), "BCC"))
                .deliveryInstructions("instruction")
                .notes("notes")
                .title(Title.builder()
                        .titleId(titleId)
                        .title("The lord protector")
                        .personName("Robins, Tony")
                        .build())
                .release(Release.builder()
                        .releaseId(new ReleaseId())
                        .allocation(Allocation.builder()
                                .allocationId(new AllocationId())
                                .deliveryInstructions("instruction")
                                .notes("notes")
                                .build())
                        .build())
                .build()


        def duplicateOrder = DuplicateOrder.builder().build()
        StandingOrderId standingOrderId = StandingOrderId.of(UUID.randomUUID())

        when:
        orderService.findDuplicateOrder(_, _, _, _, _, _) >> duplicateOrder
        def result = pendingOrderService.searchAllByTitle(titleId, null, null)


        then:
        1 * pendingOrderRepository.searchAllByTitle(titleId, null, null) >> List.of(PendingOrderStandingOrder
                .builder()
                .pendingOrder(pendingOrder)
                .standingDescription("standing order desc")
                .standingOrderId(standingOrderId)
                .standingOrderNumber("SO-00001")
                .build())
        result == [PendingOrderResponse.builder()
                           .pendingOrderId(pendingOrder.pendingOrderId)
                           .deliveryInstructions("instruction")
                           .notes("notes")
                           .fundId(null)
                           .fundCode(null)
                           .customerId(pendingOrder.customer.customerId.toString())
                           .customerCode(pendingOrder.customer.code)
                           .quantity(1)
                           .format("HB")
                           .status("NEW")
                           .customerReference(null)
                           .category(null)
                           .orderCount(0)
                           .duplicateOrder(null)
                           .standingOrders(Set.of(PendingOrderResponse.StandingOrder
                                   .builder().standingOrderId(standingOrderId)
                                   .standingOrderNumber("SO-00001")
                                   .standingDescription("standing order desc")
                                   .build()))
                           .build()]
    }

    def "Find by title"() {
        given:
        TitleId titleId = new TitleId()
        PendingOrder pendingOrder = PendingOrder.builder().pendingOrderId(new PendingOrderId()).customer(Customer.of(new CustomerId(), "BCC")).release(Release.builder().releaseId(new ReleaseId()).build()).build()

        when:
        def result = pendingOrderService.findByTitleTitleIdAndOrderStatus(titleId, PendingOrderStatus.NEW)

        then:
        1 * pendingOrderRepository.findByTitleTitleIdAndOrderStatus(titleId, PendingOrderStatus.NEW) >> [pendingOrder]
        result == [pendingOrder]
    }
}

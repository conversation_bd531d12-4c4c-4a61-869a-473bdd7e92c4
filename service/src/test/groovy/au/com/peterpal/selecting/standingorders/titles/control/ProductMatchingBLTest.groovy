package au.com.peterpal.selecting.standingorders.titles.control

import au.com.peterpal.common.audit.EventPublisher
import au.com.peterpal.selecting.standingorders.gateways.clientweb.ClientWebApiGateway
import au.com.peterpal.selecting.standingorders.products.control.ProductRepository
import au.com.peterpal.selecting.standingorders.standingorder.control.MatchingService
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductMatchInfo
import spock.lang.Specification

import java.time.LocalDate
import java.time.format.DateTimeFormatter

class ProductMatchingBLTest extends Specification {

    def "SkipProductUpdateProcessing"() {
        given:
        MatchingService matchingService = Mock(MatchingService.class)
        RematchTitleService rematchTitleService = Mock(RematchTitleService.class)
        TitleService titleService = Mock(TitleService.class)
        ProductRepository productRepository = Mock(ProductRepository.class)
        ClientWebApiGateway clientWebApiGateway = Mock(ClientWebApiGateway.class)
        MatchAndMergeTitleService matchAndMergeTitleService = Mock(MatchAndMergeTitleService.class)
        ProductMatchingBL productMatchingBL = new ProductMatchingBL(matchingService, rematchTitleService, titleService, productRepository, clientWebApiGateway, matchAndMergeTitleService)
        productMatchingBL.processProductUpdateLimitDateValue = dateLimitValue
        productMatchingBL.processProductUpdateLimitDateFormat = "dd/MM/yyyy"

        LocalDate dateLimit = Objects.nonNull(initialImportDate) ?
                LocalDate.parse(initialImportDate, DateTimeFormatter.ofPattern(productMatchingBL.processProductUpdateLimitDateFormat)) : null


        when:
        def response = productMatchingBL.skipProductUpdateProcessing(ProductMatchInfo.builder()
                .productReference("978654122371")
                .initialImportDate(dateLimit)
                .build())

        then:
        productRepository.existsByProductReference(_) >> productExist
        response == isSkipped

        where:
        initialImportDate | dateLimitValue | productExist | isSkipped
        "05/05/2024"      | "01/02/2024"   | true         | false
        "01/01/2024"      | "01/02/2024"   | true         | false
        "01/01/2024"      | null           | true         | false
        "01/01/2024"      | null           | true         | false
        "01/01/2024"      | "01/02/2024"   | false        | true
        null              | "01/02/2024"   | false        | true
    }
}

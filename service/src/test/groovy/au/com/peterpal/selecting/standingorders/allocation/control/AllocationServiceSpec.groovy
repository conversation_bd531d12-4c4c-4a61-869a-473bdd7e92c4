package au.com.peterpal.selecting.standingorders.allocation.control

import au.com.peterpal.common.audit.EventPublisher
import au.com.peterpal.selecting.standingorders.allocation.commands.AllocationSearchResponse
import au.com.peterpal.selecting.standingorders.allocation.dto.AllocationInfo
import au.com.peterpal.selecting.standingorders.allocation.dto.SearchAllocationRequest
import au.com.peterpal.selecting.standingorders.allocation.dto.SearchAllocationResponse
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus
import au.com.peterpal.selecting.standingorders.ext.category.control.CategoryRepository
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.ext.fund.boundary.dto.FundStatus
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundRepository
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.messaging.MessageChannel
import spock.lang.Specification
import spock.lang.Unroll

class AllocationServiceSpec extends Specification {
  AllocationRepository allocationRepository = Mock()
  FundRepository fundRepository = Mock()
  CategoryRepository categoryRepository = Mock()
  EventPublisher publisher = Mock()
  MessageChannel rematchAllocationOutboundChannel = Mock()
  AllocationService allocationService

  def setup() {
    allocationService = new AllocationService(allocationRepository, fundRepository, categoryRepository, publisher, rematchAllocationOutboundChannel)
  }

  @Unroll
  def "Search allocations"() {
    given:
    SearchAllocationRequest searchRequest = new SearchAllocationRequest(AllocationStatus.ACTIVE, 'customerCode', ['categories'], 'fund', 'customerReference', null, null, null, null, null, null, null, null, null)
    Pageable pageRequest = PageRequest.of(0, 10)

    when:
    def result = allocationService.search(searchRequest, pageRequest)

    then:
    1 * allocationRepository.search(searchRequest, pageRequest) >> response
    result == response.map({ allocation -> AllocationInfo.from(allocation as AllocationSearchResponse) })
    result.totalElements == size

    where:
    response                       | size
    new PageImpl<>([AllocationSearchResponse.builder()
                        .allocationId(AllocationId.of(UUID.randomUUID()))
                        .status(AllocationStatus.ACTIVE)
                        .customerStandingOrder(CustomerStandingOrder.builder()
                            .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
                            .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                            .customer(Customer.of(CustomerId.of(UUID.randomUUID()), 'BCC'))
                            .standingOrder(StandingOrder.builder()
                              .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
                              .standingOrderNumber("SO-00025061")
                              .standingOrderStatus(StandingOrderStatus.ACTIVE)
                              .description("Rowling J.K")
                              .build())
                            .build())
                        .build()]) | 1
    new PageImpl<>([])             | 0
  }

  def "Find customers with allocation"() {
    given:
    def response = [new SearchAllocationResponse.CustomerResponse(customerId: CustomerId.of(UUID.randomUUID()), code: 'BCC')]

    when:
    def result = allocationService.findCustomersWithAllocation()

    then:
    1 * allocationRepository.findCustomersWithAllocation() >> response
    result == response
  }

  def "Get customer funds"() {
    given:
    def customerCode = 'BCC'
    def fundId = FundId.of(UUID.randomUUID())
    def customer = Customer.builder().customerId(CustomerId.of(UUID.randomUUID())).code(customerCode).name(customerCode).build()
    def response = [Fund.builder().fundId(fundId).code('AF').name('AF').status(FundStatus.ACTIVE).customer(customer).build()]

    when:
    def result = allocationService.getCustomerFunds(customerCode)

    then:
    1 * fundRepository.findByCustomerCode(customerCode) >> response
    result == [new SearchAllocationResponse.FundResponse(fundId: fundId, code: 'AF')]
  }
}

package au.com.peterpal.selecting.standingorders.utils

import spock.lang.Specification

class StringHelperTest extends Specification{

  def "trim"() {
    given:
      String str = "Not trimmed "

    when:
      def result = StringHelper.of(str).trim()

    then:
      result == "Not trimmed"
  }

  def "trim null string"() {
    given:
      String str = null

    when:
      def result = StringHelper.of(str).trim()

    then:
      result == null
  }
}

package au.com.peterpal.selecting.standingorders.pendingorder.control

import au.com.peterpal.common.audit.EventPublisher
import au.com.peterpal.common.rest.validation.BusinessException
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationRepository
import au.com.peterpal.selecting.standingorders.allocation.model.Release
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId
import au.com.peterpal.selecting.standingorders.allocationpreference.control.AllocationPreferenceRepository
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType
import au.com.peterpal.selecting.standingorders.catalog.model.LanguageCode
import au.com.peterpal.selecting.standingorders.catalog.model.TitleTypeCode
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.ext.fund.boundary.dto.FundStatus
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundService
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId
import au.com.peterpal.selecting.standingorders.ext.supplier.control.SupplierRepository
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.Supplier
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.SupplierId
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.CwProductInfo

import au.com.peterpal.selecting.standingorders.pendingorder.dto.*
import au.com.peterpal.selecting.standingorders.pendingorder.events.PendingOrderUpdated
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus
import au.com.peterpal.selecting.standingorders.standingorder.control.CategoryService
import au.com.peterpal.selecting.standingorders.standingorder.control.ProductSearchService
import au.com.peterpal.selecting.standingorders.titles.control.TitleService
import au.com.peterpal.selecting.standingorders.titles.entity.Title
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId
import au.com.peterpal.selecting.standingorders.utils.CurrencyCode
import org.assertj.core.util.Lists
import org.springframework.messaging.MessageChannel
import spock.lang.Specification

class PendingOrderBLSpecTest extends Specification {

    PendingOrderService pendingOrderService = Mock(PendingOrderService.class)
    TitleService titleService = Mock(TitleService.class)
    SupplierRepository supplierService = Mock(SupplierRepository.class)
    EventPublisher eventPublisher = Mock(EventPublisher.class)
    MessageChannel pendingOrderSubmittedIntegrationChannel = Mock(MessageChannel.class)
    FundService fundService = Mock(FundService.class)
    POCreatorFactory poCreatorFactory = Mock(POCreatorFactory.class)
    AllocationPreferenceRepository allocPrefRepo = Mock(AllocationPreferenceRepository.class)
    ProductSearchService productSearchService = Mock(ProductSearchService.class)
    CategoryService categoryService = Mock(CategoryService.class)
    AllocationRepository allocationRepository = Mock(AllocationRepository.class)

    PendingOrderBL pendingOrderBL
    String username
    SubmitPendingOrderRequest submitPendingOrderRequest
    Supplier supplier
    SubmitPendingOrderService submitPendingOrderService

    def setup() {
        pendingOrderBL = new PendingOrderBL(pendingOrderService, submitPendingOrderService,
                titleService, supplierService, eventPublisher,
                pendingOrderSubmittedIntegrationChannel,
                fundService, allocPrefRepo, productSearchService, poCreatorFactory, categoryService, allocationRepository)
        username = "junm"
        SupplierId supplierId = new SupplierId()
        def titleId = new TitleId()
        submitPendingOrderRequest = SubmitPendingOrderRequest.of(titleId, Lists.newArrayList(), Lists.newArrayList())
        supplier = Supplier.builder().supplierId(new SupplierId()).name("100 MAGIC MILES").code("100MAGMIL").build()
    }


    def "Get products by title"() {
        given:
        TitleId titleId = new TitleId()
        String isbn = "9780997298741"

        when:
        def response = pendingOrderBL.findProductsByTitle(titleId)

        then:
        1 * pendingOrderService.findByTitleGroupByFormat(titleId) >> [
                new ProcessFormatResponse(
                        "HB",
                        isbn,
                        new ProcessFormatResponse.Supplier("1", "100MAGIC", "100 MAGIC MILES"),
                        new BigDecimal("38.99"),
                        CurrencyCode.AUD,
                        null)
        ]
        1 * productSearchService.searchProductByIsbn(isbn) >> Optional.of(
                CwProductInfo.builder()
                        .id(1)
                        .title(
                                CwProductInfo.TitleInfo.builder()
                                        .type(TitleTypeCode.TITLE)
                                        .text("The Observer Effect")
                                        .prefix("The")
                                        .withoutPrefix("Observer Effect")
                                        .subtitle("Poems")
                                        .build())
                        .productReference(isbn)
                        .languageCode(LanguageCode.ENG)
                        .build()
        )

        response == [new ProductDetailDto(
                isbn,
                "HB",
                null,
                null,
                null,
                new BigDecimal("38.99"),
                CurrencyCode.AUD,
                new ProductDetailDto.Supplier("100 MAGIC MILES", "1")
        )]
    }

    def "Add order number reference to pending order"() {
        given:
        def pendingOrderId = new PendingOrderId()

        def title = Title.builder().titleId(new TitleId()).title("a title").personName("a person name").build()
        def pendingOrder = PendingOrder.builder()
                .pendingOrderId(pendingOrderId)
                .orderStatus(PendingOrderStatus.SUBMITTED)
                .quantity(1)
                .format(ReleaseFormat.HB)
                .customer(Customer.of(new CustomerId(), "BCC"))
                .release(Release.builder().releaseId(new ReleaseId()).build())
                .supplier(supplier)
                .orderedProductReference(null)
                .title(title)
                .build()
        when:
        pendingOrderBL.addOrderNumber(pendingOrderId, "TO00001")

        then:
        1 * pendingOrderService.findById(pendingOrderId) >> pendingOrder
        1 * pendingOrderService.save({
            it.pendingOrderId == pendingOrderId &&
                    it.orderStatus == PendingOrderStatus.PROCESSED &&
                    it.orderNumber == "TO00001"
        }) >> { args -> args[0] }
        1 * titleService.markTitleAsProcessed(title.getTitleId())
    }


    static Fund fund = Fund.builder()
            .fundId(new FundId())
            .customer(Customer.builder()
                    .customerId(CustomerId.of(UUID.randomUUID()))
                    .code("BCC")
                    .name("Brisbane City Libraries")
                    .build())
            .code("code")
            .name("name")
            .status(FundStatus.ACTIVE)
            .build()

    static Category category = Category.builder().categoryId(new CategoryId()).code("upd-category").description("desc").status(
            CategoryStatus.ACTIVE).build();

    def "Update pending order"() {
        given:
        PendingOrder pendingOrder = PendingOrder.builder()
                .pendingOrderId(PendingOrderId.of(UUID.randomUUID()))
                .customerReference("cust ref")
                .orderStatus(PendingOrderStatus.NEW)
                .customer(Customer.of(new CustomerId(), "BCC"))
                .fund(fund)
                .category(category)
                .orderedProductReference("9780747599876")
                .format(ReleaseFormat.HB)
                .release(Release.builder().releaseId(new ReleaseId()).releaseType(ReleaseType.INITIAL).build())
                .quantity(1)
                .title(Title.builder().titleId(new TitleId()).title("a title").personName("a person name").build())
                .orderStatus(PendingOrderStatus.NEW)
                .build()

        when:
        pendingOrderBL.handle(UpdatePendingOrderRequest
                .builder()
                .pendingOrderId(pendingOrder.getPendingOrderId())
                .customerReference("upd-cust-ref")
                .category("upd-category")
                .deliveryInstructions("upd-instruction")
                .notes("upd-notes")
                .quantity(20)
                .build(), username)

        then:
        1 * categoryService.getCategoryByCode("upd-category") >> category
        1 * pendingOrderService.findById(pendingOrder.pendingOrderId) >> pendingOrder
        1 * eventPublisher.publishEvent({
            it.pendingOrderId == pendingOrder.pendingOrderId &&
                    it.customerReference == "upd-cust-ref" &&
                    it.category == "upd-category" &&
                    it.deliveryInstructions == "upd-instruction" &&
                    it.notes == "upd-notes" &&
                    it.quantity == 20
        } as PendingOrderUpdated) >> {}
    }

    def "Update pending order fund"() {
        given:
        PendingOrder pendingOrder = PendingOrder.builder()
                .pendingOrderId(PendingOrderId.of(UUID.randomUUID()))
                .customerReference("cust ref")
                .orderStatus(PendingOrderStatus.NEW)
                .customer(Customer.of(new CustomerId(), "BCC"))
                .fund(fund)
                .orderedProductReference("9780747599876")
                .format(ReleaseFormat.HB)
                .release(Release.builder().releaseId(new ReleaseId()).releaseType(ReleaseType.INITIAL).build())
                .quantity(1)
                .orderStatus(PendingOrderStatus.NEW)
                .build()

        FundId updatedFundId = new FundId();

        when:
        pendingOrderBL.handle(UpdatePendingOrderRequest
                .builder()
                .pendingOrderId(pendingOrder.getPendingOrderId())
                .fundId(updatedFundId)
                .build(), username)

        then:
        1 * pendingOrderService.findById(pendingOrder.pendingOrderId) >> pendingOrder
        1 * fundService.getFund(_ as UUID, true) >> fund.withFundId(updatedFundId)
        1 * fundService.getFund(_ as UUID) >> fund.withFundId(updatedFundId)
        1 * eventPublisher.publishEvent({
            it.pendingOrderId == pendingOrder.pendingOrderId &&
                    it.fundId == updatedFundId &&
                    it.customerReference == "cust ref" &&
                    it.category == null &&
                    it.deliveryInstructions == null &&
                    it.notes == null &&
                    it.quantity == 1
        } as PendingOrderUpdated) >> {}
    }

    def "Update submitted pending order should throw exception"() {
        given:
        PendingOrder pendingOrder = PendingOrder.builder()
                .pendingOrderId(PendingOrderId.of(UUID.randomUUID()))
                .orderStatus(PendingOrderStatus.SUBMITTED)
                .customer(Customer.of(new CustomerId(), "BCC"))
                .release(Release.builder().releaseId(new ReleaseId()).releaseType(ReleaseType.INITIAL).build())
                .build()

        FundId updatedFundId = new FundId();

        when:
        pendingOrderBL.handle(UpdatePendingOrderRequest
                .builder()
                .pendingOrderId(pendingOrder.getPendingOrderId())
                .fundId(updatedFundId)
                .build(), username)

        then:
        1 * pendingOrderService.findById(pendingOrder.pendingOrderId) >> pendingOrder

        def e = thrown(BusinessException)
        e.message == "Can not update Pending order ${pendingOrder.pendingOrderId} already been submitted or processed."
    }
}

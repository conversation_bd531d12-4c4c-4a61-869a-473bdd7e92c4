package au.com.peterpal.selecting.standingorders.titles.control

import au.com.peterpal.common.audit.EventPublisher
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationRepository
import au.com.peterpal.selecting.standingorders.allocation.model.*
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.ext.order.control.OrderRepository
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderBL
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus
import au.com.peterpal.selecting.standingorders.titles.dto.CreateRelatedTitleByStandingOrder
import au.com.peterpal.selecting.standingorders.titles.entity.RelatedTitle
import au.com.peterpal.selecting.standingorders.titles.entity.RelatedTitleId
import au.com.peterpal.selecting.standingorders.titles.entity.Title
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus
import au.com.peterpal.selecting.standingorders.titles.entity.TitleType
import org.assertj.core.util.Lists
import spock.lang.Shared
import spock.lang.Specification

class RematchAllocationServiceSpec extends Specification {

    AllocationRepository allocationRepo = Mock(AllocationRepository.class)
    TitleService titleService = Mock(TitleService.class)
    RelatedTitleBL relatedTitleBL = Mock(RelatedTitleBL.class)
    RelatedTitleRepository relatedTitleRepository = Mock(RelatedTitleRepository.class)
    PendingOrderBL pendingOrderBL = Mock(PendingOrderBL.class)
    EventPublisher eventPublisher = Mock(EventPublisher.class)
    OrderRepository orderRepository = Mock(OrderRepository.class)

    RematchAllocationService cut
    def category
    Allocation baseAllocation
    Release baseRelease
    Title baseTitle

    @Shared
    Title relatedTitle = Title.builder()
            .titleId(new TitleId())
            .titleNumber("T002")
            .type(TitleType.RELATED)
            .title("a title")
            .personName("a person name")
            .titleStatus(TitleStatus.PENDING)
            .build()

    @Shared
    RelatedTitle relatedTitleHolder = RelatedTitle.builder()
            .relatedTitleId(new RelatedTitleId())
            .relatedTitle(relatedTitle).build()

    def "rematch allocation"() {
        given:
        def title = baseTitle.withTitleId(new TitleId()).withTitleStatus(titlestatus)
        def release = baseRelease.withReleaseId(new ReleaseId())
        def allocation = baseAllocation.withReleases(Lists.newArrayList(release)).withAllocationId(new AllocationId())

        when:
        def response = cut.rematch(title, allocation, "test")

        then:
        relatedTitleRepository.findAllByOriginalTitleTitleId(_ as TitleId) >> existingRelatedTitles
        relatedTitleBL.handle(_ as CreateRelatedTitleByStandingOrder, _ as String) >> relatedTitle
        pendingOrderBL.findAllNewPendingOrderByTitleIdAllocationId(_ as TitleId, _ as AllocationId) >> [Mock(PendingOrder.class)]

        response.title.titleNumber == rTitleNumber
        response.title.type == rType
        response.title.titleStatus == rStatus

        where:
        titlestatus           | existingRelatedTitles                                      | rTitleNumber | rType              | rStatus
        TitleStatus.PENDING   | []                                                         | "T001"       | TitleType.ORIGINAL | TitleStatus.PENDING
        TitleStatus.PROCESSED | []                                                         | "T002"       | TitleType.RELATED  | TitleStatus.PENDING
        TitleStatus.PROCESSED | [relatedTitleHolder
                                         .withRelatedTitle(relatedTitle
                                                 .withTitleNumber("T003"))]                | "T003"       | TitleType.RELATED  | TitleStatus.PENDING
        TitleStatus.PROCESSED | [relatedTitleHolder
                                         .withRelatedTitle(relatedTitle
                                                 .withTitleNumber("T003")
                                                 .withTitleStatus(TitleStatus.PROCESSED))] | "T002"       | TitleType.RELATED  | TitleStatus.PENDING

    }


    def setup() {
        cut = new RematchAllocationService(
                allocationRepo,
                titleService,
                relatedTitleBL,
                relatedTitleRepository,
                pendingOrderBL,
                eventPublisher,
                orderRepository
        )
        category = Category.builder().categoryId(new CategoryId()).code("AN").status(CategoryStatus.ACTIVE).build()
        baseTitle = Title.builder()
                .titleId(new TitleId())
                .titleNumber("T001")
                .type(TitleType.ORIGINAL)
                .title("a title")
                .personName("a person name")
                .titleStatus(TitleStatus.PROCESSED)
                .build()

        baseRelease = Release.builder()
                .releaseId(new ReleaseId())
                .quantity(5)
                .releaseType(ReleaseType.INITIAL)
                .build()
        baseAllocation = Allocation.builder()
                .allocationId(new AllocationId())
                .categories(List.of(category))
                .customerStandingOrder(CustomerStandingOrder.builder()
                        .customerStandingOrderId(new CustomerStandingOrderId())
                        .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                        .customer(Customer.builder()
                                .name("CUST-TEST01")
                                .customerId(CustomerId.of(UUID.randomUUID()))
                                .code("CUST-CODE01")
                                .build())
                        .standingOrder(StandingOrder.builder()
                                .standingOrderId(new StandingOrderId())
                                .standingOrderNumber("SO-001")
                                .standingOrderStatus(StandingOrderStatus.ACTIVE)
                                .build())
                        .build())
                .status(AllocationStatus.ACTIVE)
                .build()
    }
}

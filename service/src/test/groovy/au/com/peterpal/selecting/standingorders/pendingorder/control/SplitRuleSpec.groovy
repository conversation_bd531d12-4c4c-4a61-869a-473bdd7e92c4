package au.com.peterpal.selecting.standingorders.pendingorder.control

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus
import au.com.peterpal.selecting.standingorders.allocation.model.Release
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.Format
import au.com.peterpal.selecting.standingorders.ext.branch.entity.Branch
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchStatus
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.pendingorder.dto.CopyPendingOrderDto
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProduct
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProductId
import au.com.peterpal.selecting.standingorders.titles.entity.ProductAggregated
import au.com.peterpal.selecting.standingorders.titles.entity.ProductAggregatedId
import au.com.peterpal.selecting.standingorders.titles.entity.ProductAggregatedStatus
import au.com.peterpal.selecting.standingorders.titles.entity.Title
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus
import au.com.peterpal.selecting.standingorders.titles.entity.TitleType
import org.assertj.core.util.Lists
import spock.lang.Shared
import spock.lang.Specification

import java.time.LocalDate

class SplitRuleSpec extends Specification {

    SplitRule cut

    def "copy"() {
        given:
        def allocation = baseAllocation
                .withReleases(Lists.newArrayList(baseRelease
                        .withQuantity(allocationQty)
                        .withHardbackQuantity(hbQty)
                        .withPaperbackQuantity(pbQty)
                ))
                .withAllocationId(new AllocationId())

        poHbDetail = CopyPendingOrderDto.PendingOrderDetail.builder()
                .isbn("isbn")
                .format(ReleaseFormat.HB)
                .quantityTaken(hbQtyTaken)
                .brantchQtyTakenMap(hbBranchQtyTaken)
                .build()

        poPbDetail = CopyPendingOrderDto.PendingOrderDetail.builder()
                .isbn("isbn")
                .format(ReleaseFormat.PB)
                .quantityTaken(pbQtyTaken)
                .brantchQtyTakenMap(pbBranchQtyTaken)
                .build()

        request = CopyPendingOrderDto.builder()
                .title(baseTitle)
                .allocation(allocation)
                .branchAllocations(branchAllocations)
                .pendingOrderDetailMap([(ReleaseFormat.HB): poHbDetail, (ReleaseFormat.PB): poPbDetail])
                .build()

        when:
        def response = cut.copy(request)

        then:
        def poHB = response.stream().filter { it.format == ReleaseFormat.HB }.findAny()
        def poPB = response.stream().filter { it.format == ReleaseFormat.PB }.findAny()

        poHB.map { it.getQuantity() }.orElse(null) == newHBQty
        poPB.map { it.getQuantity() }.orElse(null) == newPBQty

        poHB.map { it ->
            it.branchDistributions.sort().collectEntries { [it.allocationId, it.quantity] }
        }.orElse(null) == hBbranchDist
        poPB.map { it ->
            it.branchDistributions.sort().collectEntries { [it.allocationId, it.quantity] }
        }.orElse(null) == pBbranchDist

        where:
        allocationQty | hbQty | pbQty | branchAllocations                                     | hbQtyTaken | hbBranchQtyTaken             | pbQtyTaken | pbBranchQtyTaken             | newHBQty | hBbranchDist                 | newPBQty | pBbranchDist
        8             | 5     | 3     | [baseBranchAllocation
                                                 .withReleases([baseRelease.withQuantity(4)])
                                                 .withAllocationId(branch1),
                                         baseBranchAllocation
                                                 .withReleases([baseRelease.withQuantity(4)])
                                                 .withAllocationId(branch2)]                  | 3          | [(branch1): 1, (branch2): 2] | 2          | [(branch1): 1, (branch2): 1] | 2        | [(branch1): 2]               | 1        | [(branch2): 1]
        5             | 2     | 3     | [baseBranchAllocation
                                                 .withReleases([baseRelease.withQuantity(3)])
                                                 .withAllocationId(branch1),
                                         baseBranchAllocation
                                                 .withReleases([baseRelease.withQuantity(2)])
                                                 .withAllocationId(branch2)]                  | 2          | [(branch1): 1, (branch2): 1] | 1          | [(branch1): 1]               | null     | null                         | 2        | [(branch1): 1, (branch2): 1]
        7             | 4     | 3     | [baseBranchAllocation
                                                 .withReleases([baseRelease.withQuantity(3)])
                                                 .withAllocationId(branch1),
                                         baseBranchAllocation
                                                 .withReleases([baseRelease.withQuantity(4)])
                                                 .withAllocationId(branch2)]                  | 1          | [(branch1): 1]               | 1          | [(branch1): 1]               | 3        | [(branch1): 1, (branch2): 2] | 2        | [(branch2): 2]
    }

    @Shared
    Allocation baseAllocation = Allocation.builder()
            .allocationId(new AllocationId())
            .categories(List.of(Category.builder().categoryId(new CategoryId()).code("AN").status(CategoryStatus.ACTIVE).build()))
            .customerStandingOrder(CustomerStandingOrder.builder()
                    .customerStandingOrderId(new CustomerStandingOrderId())
                    .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                    .customer(Customer.builder()
                            .name("CUST-TEST01")
                            .customerId(CustomerId.of(UUID.randomUUID()))
                            .code("CUST-CODE01")
                            .build())
                    .standingOrder(StandingOrder.builder()
                            .standingOrderId(new StandingOrderId())
                            .standingOrderNumber("SO-001")
                            .standingOrderStatus(StandingOrderStatus.ACTIVE)
                            .build())
                    .build())
            .status(AllocationStatus.ACTIVE)
            .build()

    @Shared
    Allocation baseBranchAllocation = Allocation.builder()
            .allocationId(new AllocationId())
            .categories(List.of(Category.builder().categoryId(new CategoryId()).code("AN").status(CategoryStatus.ACTIVE).build()))
            .customerStandingOrder(CustomerStandingOrder.builder()
                    .customerStandingOrderId(new CustomerStandingOrderId())
                    .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                    .customer(Customer.builder()
                            .name("CUST-TEST01")
                            .customerId(CustomerId.of(UUID.randomUUID()))
                            .code("CUST-CODE01")
                            .build())
                    .standingOrder(StandingOrder.builder()
                            .standingOrderId(new StandingOrderId())
                            .standingOrderNumber("SO-001")
                            .standingOrderStatus(StandingOrderStatus.ACTIVE)
                            .build())
                    .build())
            .branch(Branch.builder()
                    .branchId(new BranchId())
                    .code("code")
                    .name("name")
                    .status(BranchStatus.ACTIVE)
                    .build())
            .status(AllocationStatus.ACTIVE)
            .build()

    @Shared
    Release baseRelease = Release.builder()
            .releaseId(new ReleaseId())
            .quantity(5)
            .releaseType(ReleaseType.INITIAL)
            .build()

    @Shared
    Title baseTitle = Title.builder()
            .titleId(new TitleId())
            .titleNumber("T001")
            .type(TitleType.ORIGINAL)
            .title("a title")
            .personName("a person name")
            .titleStatus(TitleStatus.PROCESSED)
            .productAggregatedList([
                    ProductAggregated.builder()
                            .productAggregatedId(new ProductAggregatedId())
                            .matchedProduct(MatchedProduct.builder()
                                    .matchedProductId(new MatchedProductId())
                                    .isbn("978978HB")
                                    .publicationDate(LocalDate.now())
                                    .format(Format.HARDBACK)
                                    .build())
                            .status(ProductAggregatedStatus.ACCEPTED)
                            .build(),
                    ProductAggregated.builder()
                            .productAggregatedId(new ProductAggregatedId())
                            .matchedProduct(MatchedProduct.builder()
                                    .matchedProductId(new MatchedProductId())
                                    .isbn("978978PB")
                                    .publicationDate(LocalDate.now())
                                    .format(Format.PAPERBACK)
                                    .build())
                            .status(ProductAggregatedStatus.ACCEPTED)
                            .build()
            ])
            .build()

    CopyPendingOrderDto request
    def poHbDetail
    def poPbDetail
    @Shared
    def branch1 = new AllocationId()
    @Shared
    def branch2 = new AllocationId()
    @Shared
    def branch3 = new AllocationId()

    def setup() {
        cut = new SplitRule()
    }
}

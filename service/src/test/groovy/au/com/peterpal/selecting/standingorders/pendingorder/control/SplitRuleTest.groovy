package au.com.peterpal.selecting.standingorders.pendingorder.control

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus
import au.com.peterpal.selecting.standingorders.allocation.model.Release
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId
import au.com.peterpal.selecting.standingorders.ext.branch.entity.Branch
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchStatus
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.pendingorder.dto.POCreateInfo
import au.com.peterpal.selecting.standingorders.standingorder.dto.ProductInfo
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus
import au.com.peterpal.selecting.standingorders.titles.entity.Title
import com.google.common.collect.Lists
import spock.lang.Shared
import spock.lang.Specification

import java.time.LocalDate
import java.util.stream.Collectors

import static au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat.HB
import static au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat.PB

class SplitRuleTest extends Specification {
    @Shared
    def baseProduct = ProductInfo.builder()
            .isbn("978635472354723")
            .format(HB)
            .pubDate(LocalDate.now())
            .build()

    def "SplitRule qty test"() {
        given:
        SplitRule cut = new SplitRule()
        def allocation = Allocation.builder()
                .allocationId(new AllocationId())
                .status(AllocationStatus.ACTIVE)
                .customerStandingOrder(CustomerStandingOrder.builder()
                        .customerStandingOrderId(new CustomerStandingOrderId())
                        .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                        .customer(Customer.builder()
                                .customerId(new CustomerId())
                                .code("code")
                                .name("name")
                                .build())
                        .build())
                .build()

        def release = Release.builder()
                .releaseId(new ReleaseId())
                .allocation(allocation)
                .quantity(releaseQty)
                .paperbackQuantity(releasePBqty)
                .hardbackQuantity(releaseHBqty)
                .build()

        def request = POCreateInfo.builder()
                .title(Mock(Title.class))
                .products(selectedProducts)
                .release(release)
                .allocation(allocation)
                .build()

        when:
        def response = cut.createPOList(request)

        then:
        response
                .stream()
                .map { po -> "${po.format}:${po.quantity}" }
                .sorted()
                .collect(Collectors.joining(', ')) == poCreatedResult

        where:
        selectedProducts                                         | releaseQty | releasePBqty | releaseHBqty | poCreatedResult
        [baseProduct.withFormat(HB)]                             | 10         | 5            | 5            | "HB:10"
        [baseProduct.withFormat(HB)]                             | 10         | 5            | 0            | "HB:10"
        [baseProduct.withFormat(HB)]                             | 10         | 0            | 5            | "HB:10"
        [baseProduct.withFormat(HB)]                             | 10         | 0            | 0            | "HB:10"
        [baseProduct.withFormat(HB)]                             | 0          | 5            | 5            | ""
        [baseProduct.withFormat(HB)]                             | 0          | 5            | 0            | ""
        [baseProduct.withFormat(HB)]                             | 0          | 0            | 5            | ""
        [baseProduct.withFormat(HB)]                             | 0          | 0            | 0            | ""
        [baseProduct.withFormat(PB)]                             | 10         | 5            | 5            | "PB:10"
        [baseProduct.withFormat(PB)]                             | 10         | 5            | 0            | "PB:10"
        [baseProduct.withFormat(PB)]                             | 10         | 0            | 5            | "PB:10"
        [baseProduct.withFormat(PB)]                             | 10         | 0            | 0            | "PB:10"
        [baseProduct.withFormat(PB)]                             | 0          | 5            | 5            | ""
        [baseProduct.withFormat(PB)]                             | 0          | 5            | 0            | ""
        [baseProduct.withFormat(PB)]                             | 0          | 0            | 5            | ""
        [baseProduct.withFormat(PB)]                             | 0          | 0            | 0            | ""
        [baseProduct.withFormat(HB), baseProduct.withFormat(PB)] | 10         | 5            | 5            | "HB:5, PB:5"
        [baseProduct.withFormat(HB), baseProduct.withFormat(PB)] | 10         | 5            | 0            | "PB:5"
        [baseProduct.withFormat(HB), baseProduct.withFormat(PB)] | 10         | 0            | 5            | "HB:5"
        [baseProduct.withFormat(HB), baseProduct.withFormat(PB)] | 10         | 0            | 0            | ""
        [baseProduct.withFormat(HB), baseProduct.withFormat(PB)] | 0          | 5            | 5            | "HB:5, PB:5"
        [baseProduct.withFormat(HB), baseProduct.withFormat(PB)] | 0          | 5            | 0            | "PB:5"
        [baseProduct.withFormat(HB), baseProduct.withFormat(PB)] | 0          | 0            | 5            | "HB:5"
        [baseProduct.withFormat(HB), baseProduct.withFormat(PB)] | 0          | 0            | 0            | ""

    }

    @Shared
    def baseBranch = Branch.builder()
            .branchId(new BranchId())
            .status(BranchStatus.ACTIVE)
            .code("code")
            .name("name")
            .build()
    @Shared
    def baseRelease = Release.builder()
            .releaseId(new ReleaseId())
            .releaseType(ReleaseType.INITIAL)
            .actionType(ActionType.ORDER)
            .initialAssignmentRule(AssignmentRule.SPLIT)
            .build()
    @Shared
    def baseBranchAllocation = Allocation.builder()
            .allocationId(new AllocationId())
            .status(AllocationStatus.ACTIVE)
            .releases(Lists.newArrayList())
            .build()

    def "SplitRule qty test with branch"() {
        given:
        SplitRule cut = new SplitRule()
        def allocation = Allocation.builder()
                .allocationId(new AllocationId())
                .status(AllocationStatus.ACTIVE)
                .customerStandingOrder(CustomerStandingOrder.builder()
                        .customerStandingOrderId(new CustomerStandingOrderId())
                        .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                        .customer(Customer.builder()
                                .customerId(new CustomerId())
                                .code("code")
                                .name("name")
                                .build())
                        .build())
                .build()

        def release = Release.builder()
                .releaseId(new ReleaseId())
                .allocation(allocation)
                .quantity(parenQty)
                .paperbackQuantity(parentPBqty)
                .hardbackQuantity(parentHBqty)
                .build()

        def request = POCreateInfo.builder()
                .title(Mock(Title.class))
                .products([baseProduct.withFormat(HB), baseProduct.withFormat(PB)])
                .release(release)
                .allocation(allocation)
                .branchAllocations(branchAllocations)
                .build()

        when:
        def response = cut.createPOList(request)

        then:
        response
                .stream()
                .map { po -> "${po.format}:${po.quantity}==>${po.branchDistributions.stream().map { b -> b.branchCode + ':' + b.quantity }.sorted().collect(Collectors.toList())}" }
                .sorted()
                .collect(Collectors.joining(', ')) == poCreatedResult

        where:
        branchAllocations                                      | parenQty | parentPBqty | parentHBqty | poCreatedResult
        [baseBranchAllocation
                 .withAllocationId(new AllocationId())
                 .withBranch(baseBranch.withCode("CAS"))
                 .withReleases([baseRelease.withQuantity(2)]),
         baseBranchAllocation
                 .withAllocationId(new AllocationId())
                 .withBranch(baseBranch.withCode("DAR"))
                 .withReleases([baseRelease.withQuantity(3)])] | 5        | 4           | 1           | "HB:1==>[CAS:1], PB:4==>[CAS:1, DAR:3]"
        [baseBranchAllocation
                 .withAllocationId(new AllocationId())
                 .withBranch(baseBranch.withCode("CAS"))
                 .withReleases([baseRelease.withQuantity(4)]),
         baseBranchAllocation
                 .withAllocationId(new AllocationId())
                 .withBranch(baseBranch.withCode("DAR"))
                 .withReleases([baseRelease.withQuantity(1)])] | 5        | 4           | 1           | "HB:1==>[CAS:1], PB:4==>[CAS:3, DAR:1]"
        [baseBranchAllocation
                 .withAllocationId(new AllocationId())
                 .withBranch(baseBranch.withCode("CAS"))
                 .withReleases([baseRelease.withQuantity(5)])] | 5        | 4           | 1           | "HB:1==>[CAS:1], PB:4==>[CAS:4]"

    }
}

package au.com.peterpal.selecting.standingorders.customerstandingorder.control

import au.com.peterpal.common.audit.EventPublisher
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationService
import au.com.peterpal.selecting.standingorders.allocation.dto.AllocationInfo
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus
import au.com.peterpal.selecting.standingorders.allocation.model.Release
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId
import au.com.peterpal.selecting.standingorders.allocationpreference.control.AllocationPreferenceRepository
import au.com.peterpal.selecting.standingorders.allocationpreference.control.ReleasePreferenceRepository
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType
import au.com.peterpal.selecting.standingorders.ext.category.control.CategoryRepository
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus
import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.BranchAllocationInfo
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId
import au.com.peterpal.selecting.standingorders.ext.branch.entity.Branch
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchStatus
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerService
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.ext.fund.boundary.dto.FundStatus
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundService
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderBL
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderRepository
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus
import org.springframework.messaging.MessageChannel
import spock.lang.Specification

class CustomerStandingOrderBLSpec extends Specification {
    AllocationService allocationService = Mock(AllocationService.class)
    FundService fundService = Mock(FundService.class)
    CustomerService customerService = Mock(CustomerService.class)
    CustomerStandingOrderRepository customerStandingOrderRepository = Mock(CustomerStandingOrderRepository.class)
    StandingOrderRepository standingOrderRepository = Mock(StandingOrderRepository.class)
    StandingOrderBL standingOrderBL = Mock(StandingOrderBL.class)
    AllocationPreferenceRepository allocationPreferenceRepository = Mock(AllocationPreferenceRepository.class)
    ReleaseService releaseService = Mock(ReleaseService.class)
    ReleasePreferenceRepository releasePreferenceRepository = Mock(ReleasePreferenceRepository.class)
    EventPublisher eventPublisher = Mock(EventPublisher.class)
    CategoryRepository categoryRepository = Mock(CategoryRepository.class)
    MessageChannel rematchAllocationOutboundChannel = Mock()

    List<BranchAllocationInfo> branches = new ArrayList<>()
    CustomerStandingOrderBL customerStandingOrderBL

    Customer customer = Customer.builder()
            .customerId(CustomerId.of(UUID.randomUUID()))
            .code("BCC")
            .name("Customer name")
            .build()

    Branch branch = Branch.builder()
            .branchId(BranchId.of(UUID.randomUUID()))
            .code("ANN")
            .name("Branch name")
            .customer(customer)
            .status(BranchStatus.ACTIVE)
            .build()

    StandingOrder standingOrder = StandingOrder.builder()
            .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
            .description("desc")
            .notes("some notes")
            .standingOrderNumber("SO-00025026")
            .standingOrderStatus(StandingOrderStatus.ACTIVE)
            .build()

    def setup() {
        customerStandingOrderBL = new CustomerStandingOrderBL(
                customerStandingOrderRepository,
                customerService,
                standingOrderRepository,
                standingOrderBL,
                allocationService,
                fundService,
                allocationPreferenceRepository,
                releaseService,
                releasePreferenceRepository,
                eventPublisher,
                categoryRepository,
                rematchAllocationOutboundChannel
        )
    }

    def "branch allocations will not update if category is blank"() {
        given:
        List<AllocationInfo> saved
        branches.add(BranchAllocationInfo.builder()
                .allocationId(null)
                .branchId(UUID.randomUUID())
                .customerStandingOrderId(null)
                .standingOrderId(UUID.randomUUID())
                .customerId(UUID.randomUUID())
                .status(AllocationStatus.ACTIVE)
                .categories(null)
                .build());

        when:
        saved = customerStandingOrderBL.updateBranchAllocations(branches, false, "test")
        then:
        saved.isEmpty()
    }

    def "new branch allocation is created when allocation id is null"() {
        given:
        List<AllocationInfo> saved
        def cso = CustomerStandingOrder.builder()
                .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
                .customer(customer)
                .standingOrder(standingOrder)
                .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                .allocations(new ArrayList<Allocation>())
                .build()

        def release = Release.builder()
                .releaseId(new ReleaseId(UUID.randomUUID()))
                .releaseType(ReleaseType.INITIAL)
                .quantity(0)
                .hardbackQuantity(0)
                .paperbackQuantity(0)
                .build()

        Category categoryCAT = Category.builder().categoryId(new CategoryId()).code("CAT").description("desc").status(
                CategoryStatus.ACTIVE).build();
        categoryRepository.save(categoryCAT)

        def codeIn = categoryRepository.findByCodeIn(List.of("CAT"));

        def allocation = Allocation.builder()
                .allocationId(AllocationId.of(UUID.randomUUID()))
                .status(AllocationStatus.ACTIVE)
                .categories(List.of(categoryCAT))
                .customerStandingOrder(cso)
                .build()

        release.setAllocation(allocation)
        allocation.getReleases().add(release)
        cso.getAllocations().add(allocation)
        customerService.getBranch(_) >> branch
        customerService.findById(_) >> customer
        standingOrderBL.findById(_) >> standingOrder
        fundService.getFund(_) >> null
        customerStandingOrderRepository.findByCustomerAndStandingOrder(_, _) >> Optional.of(cso)
        customerStandingOrderRepository.getOne(_) >> cso
        allocationService.save(_ as Allocation) >> { Allocation alloc -> alloc }
        allocationService.findById(_) >> allocation
        customerStandingOrderBL.updateAllocationQtys(_) >> {}

        branches.add(BranchAllocationInfo.builder()
                .allocationId(null)
                .branchId(UUID.randomUUID())
                .customerStandingOrderId(null)
                .standingOrderId(UUID.randomUUID())
                .customerId(UUID.randomUUID())
                .status(AllocationStatus.ACTIVE)
                .categories(List.of("CAT"))
//          .categories(List.of(categoryCAT))
                .baParentId(allocation.getAllocationId().getId())
                .build());

        int x = 1;

        when:
        saved = customerStandingOrderBL.updateBranchAllocations(branches, false, "test")

        then:
        saved.size() == 1
        1 * customerService.getBranch(_)
        1 * allocationService.updateAllocationQtys(_) >> allocation
    }

    def "branch allocation is updated when it exists"() {
        given:
        List<AllocationInfo> saved
        def updatedNotes = "Updated notes"
        def fund = Fund.builder()
                .fundId(new FundId())
                .customer(customer)
                .code("code")
                .name("name")
                .status(FundStatus.ACTIVE)
                .build()

        def cso = CustomerStandingOrder.builder()
                .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
                .customer(customer)
                .standingOrder(standingOrder)
                .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                .allocations(new ArrayList<Allocation>())
                .build()

        def release = Release.builder()
                .releaseId(new ReleaseId(UUID.randomUUID()))
                .releaseType(ReleaseType.INITIAL)
                .quantity(0)
                .hardbackQuantity(0)
                .paperbackQuantity(0)
                .build()

        Category categoryCAT = Category.builder().categoryId(new CategoryId()).code("CAT").description("desc").status(
                CategoryStatus.ACTIVE).build();
        categoryRepository.save(categoryCAT)

        def ballocation = Allocation.builder()
                .allocationId(AllocationId.of(UUID.randomUUID()))
                .status(AllocationStatus.ACTIVE)
                .branch(branch)
                .categories(List.of(categoryCAT))
                .customerStandingOrder(cso)
                .notes("Some notes")
                .build()
        release.setAllocation(ballocation)
        ballocation.getReleases().add(release)
        cso.getAllocations().add(ballocation)

        def pallocation = Allocation.builder()
                .allocationId(AllocationId.of(UUID.randomUUID()))
                .status(AllocationStatus.ACTIVE)
                .categories(List.of(categoryCAT))
                .customerStandingOrder(cso)
                .build()

        release = Release.builder()
                .releaseId(new ReleaseId(UUID.randomUUID()))
                .releaseType(ReleaseType.INITIAL)
                .quantity(0)
                .hardbackQuantity(0)
                .paperbackQuantity(0)
                .build()
        release.setAllocation(pallocation)
        pallocation.getReleases().add(release)
        cso.getAllocations().add(pallocation)
        ballocation.setBaParent(pallocation)

        fundService.getFund(_) >> fund
        AllocationService allocSrv = Stub()
        allocSrv.findById(_) >> ballocation
        allocSrv.save(_ as Allocation) >> { args -> args[0] }
        customerStandingOrderRepository.getOne(_) >> cso
        def csoBL = new CustomerStandingOrderBL(
                customerStandingOrderRepository,
                customerService,
                standingOrderRepository,
                standingOrderBL,
                allocSrv,
                fundService,
                allocationPreferenceRepository,
                releaseService,
                releasePreferenceRepository,
                eventPublisher,
                categoryRepository,
                rematchAllocationOutboundChannel

        )
        Category categoryAFD = Category.builder().categoryId(new CategoryId()).code("AFD").description("desc").status(
                CategoryStatus.ACTIVE).build();
        categoryRepository.save(categoryAFD)

        branches.add(BranchAllocationInfo.builder()
                .allocationId(ballocation.getAllocationId().getId())
                .branchId(branch.getBranchId().getId())
                .customerStandingOrderId(null)
                .standingOrderId(standingOrder.getStandingOrderId().getId())
                .customerId(customer.getCustomerId().getId())
                .status(AllocationStatus.ACTIVE)
                .categories(List.of(categoryAFD))
                .fundId(fund.getFundId().getId())
                .notes(updatedNotes)
                .baParentId(pallocation.getAllocationId().getId())
                .build());

        when:
        saved = csoBL.updateBranchAllocations(branches, false, "test")

        then:
        saved.size() == 1
        saved.get(0).getNotes() == updatedNotes
        saved.get(0).getFund() != null
        saved.get(0).getFund().getFundId() == fund.getFundId()
    }
}

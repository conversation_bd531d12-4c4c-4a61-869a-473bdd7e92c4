package au.com.peterpal.selecting.standingorders.titles.control

import au.com.peterpal.selecting.standingorders.catalog.model.ProductFormCode
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.Format
import au.com.peterpal.selecting.standingorders.titles.entity.ProductFormatMapping
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import spock.lang.Specification

@DataJpaTest
class ProductFormatServiceTest extends Specification {
    ProductFormatService productFormatService

    @Autowired
    ProductFormatMappingRepository productFormatMappingRepository

    def "map format by formCode"() {
        productFormatService = new ProductFormatService(productFormatMappingRepository)
        given:
        productFormatMappingRepository.save(ProductFormatMapping.of(ProductFormCode.BB, Format.HARDBACK))
        productFormatMappingRepository.save(ProductFormatMapping.of(ProductFormCode.BC, Format.PAPERBACK))


        when:
        def response = productFormatService.map(formCode)

        then:
        response == format

        where:
        format           | formCode
        Format.HARDBACK  | ProductFormCode.BB
        Format.PAPERBACK | ProductFormCode.BC
        null             | ProductFormCode.WW
    }
}

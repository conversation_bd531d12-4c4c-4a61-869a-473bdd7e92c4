package au.com.peterpal.selecting.standingorders.ext.supplier.control

import au.com.peterpal.selecting.standingorders.ext.supplier.boundary.dto.SupplierStatus
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.Supplier
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.SupplierId
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.data.domain.PageRequest
import spock.lang.Specification

@DataJpaTest
class SupplierRepositorySpec extends Specification {

  @Autowired
  SupplierRepository repo

  def "find by code and ACTIVE status"() {
    given:
      def suppliers = Arrays.asList(
        Supplier.builder()
          .supplierId(SupplierId.of("da13b8ee-10c0-4c63-954c-a9d05f561a87"))
          .name("PEARSON AUSTRALIA")
          .code("PEAAUS")
          .status(SupplierStatus.ACTIVE)
          .build(),
        Supplier.builder()
          .supplierId(SupplierId.of("5d41db81-1199-498d-89e0-34a1c222dfd5"))
          .name("Damien Abbott")
          .code("ABBOTTD")
          .status(SupplierStatus.INACTIVE)
          .build()
      )
      repo.saveAll(suppliers)

    when:
      def supplier = repo.findByCode("PEAAUS")

    then:
      supplier.isEmpty() == false
      supplier.get().getCode() == "PEAAUS"
  }

  def "find all ACTIVE by code"() {
    given:
      def suppliers = Arrays.asList(
          Supplier.builder()
              .supplierId(SupplierId.of("da13b8ee-10c0-4c63-954c-a9d05f561a87"))
              .name("PEARSON AUSTRALIA")
              .code("PEAAUS")
              .status(SupplierStatus.ACTIVE)
              .build(),
          Supplier.builder()
              .supplierId(SupplierId.of("5d41db81-1199-498d-89e0-34a1c222dfd5"))
              .name("Damien Abbott")
              .code("ABBOTTD")
              .status(SupplierStatus.INACTIVE)
              .build()
      )
      repo.saveAll(suppliers)

    when:
      def saved = repo.findAllByCode("PEAAUS")

    then:
      saved.isEmpty() == false
      saved.size() == 1
      saved.get(0).getCode() == "PEAAUS"
  }

  def "find all active by name ignore case"() {
    given:
      def suppliers = Arrays.asList(
          Supplier.builder()
              .supplierId(SupplierId.of("da13b8ee-10c0-4c63-954c-a9d05f561a87"))
              .name("PEARSON AUSTRALIA")
              .code("PEAAUS")
              .status(SupplierStatus.ACTIVE)
              .build(),
          Supplier.builder()
              .supplierId(SupplierId.of("5d41db81-1199-498d-89e0-34a1c222dfd5"))
              .name("Damien Abbott")
              .code("ABBOTTD")
              .status(SupplierStatus.ACTIVE)
              .build()
      )
      repo.saveAll(suppliers)

    when:
      def found = repo.findAllByNameStartsWithIgnoreCaseAndStatus("pe", SupplierStatus.ACTIVE, PageRequest.of(0, 2))

    then:
      found.isEmpty() == false
      found.size() == 1
      found.get(0).getName() == "PEARSON AUSTRALIA"
  }
}

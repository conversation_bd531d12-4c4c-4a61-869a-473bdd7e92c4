package au.com.peterpal.selecting.standingorders.allocation.model

import au.com.peterpal.selecting.standingorders.ext.category.entity.Category
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.ext.fund.boundary.dto.FundStatus
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import spock.lang.Specification;

public class AllocationCategorySpec extends Specification {

  Allocation allocation

  Category categoryY
  Category categoryQ
  Category categoryAF

  def setup() {
     categoryY = Category.builder().categoryId(new CategoryId()).code("Y")
            .description("desc").status(CategoryStatus.ACTIVE).build();
     categoryQ = Category.builder().categoryId(new CategoryId()).code("Q")
            .description("desc").status(CategoryStatus.ACTIVE).build();
     categoryAF = Category.builder().categoryId(new CategoryId()).code("AF")
            .description("desc").status(CategoryStatus.ACTIVE).build();

    allocation = Allocation.builder()
        .allocationId(AllocationId.of(UUID.randomUUID()))
        .allocationPreference(null)
        .categories(List.of(categoryY, categoryQ, categoryAF))
        .customerReference("CUSTREF01")
        .deliveryInstructions("deliveryInstructions")
        .fund(Fund.builder()
            .fundId(FundId.of(UUID.randomUUID()))
            .code("ABC")
            .name("name")
            .status(FundStatus.ACTIVE)
            .customer(Customer.of(CustomerId.of(UUID.randomUUID()), "BCC"))
            .build())
        .status(AllocationStatus.ACTIVE)
        .notes("notes")
        .build()
  }

  def "contains category returns true for contained categories"() {
    when:
      boolean result = allocation.containsCategory(categoryQ)

    then:
      result == true
  }

  def "contains category for null category is false"() {
    when:
      boolean result = allocation.containsCategory(category)

    then:
      result == resultValue

    where:
      containsCategory  | category  | resultValue
      'null'            | null      | false
  }

//  def "contains category for null allocation category is false"() {
//    given:
//      allocation.setCategories(Arrays.asList("null"))
////      allocation.setCategories(Arrays.asList(category))
//
//    when:
//      boolean result = allocation.containsCategory(categoryQ)
//
//    then:
//      result == resultValue
//
//    where:
//      containsCategory  | category  | resultValue
//      'null'            | null      | false
//  }
}

package au.com.peterpal.selecting.standingorders.gateways.clientweb

import au.com.peterpal.common.rest.validation.ResourceNotFoundException
import au.com.peterpal.selecting.standingorders.catalog.model.LanguageCode
import au.com.peterpal.selecting.standingorders.catalog.model.TitleTypeCode
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.CwProductInfo
import spock.lang.Specification

class ClientWebApiGatewaySpecTest extends Specification {

  ClientWebApiClient clientWebApiClient = Mock(ClientWebApiClient.class)
  ClientWebApiGateway clientWebApiGateway

  def setup() {
    clientWebApiGateway = new ClientWebApiGateway(clientWebApiClient)
  }

  def "Find product by isbn"() {
    given:
    String isbn = "9783956794605"
    CwProductInfo productInfo =
        CwProductInfo.builder()
            .id(1)
            .title(
                CwProductInfo.TitleInfo.builder()
                    .type(TitleTypeCode.TITLE)
                    .text("The Observer Effect")
                    .prefix("The")
                    .withoutPrefix("Observer Effect")
                    .subtitle("Poems")
                    .build())
            .productReference(isbn)
            .languageCode(LanguageCode.ENG)
            .build()

    when:
    def result = clientWebApiGateway.searchProductByIsbn(isbn)

    then:
    1 * clientWebApiClient.getProductByIsbn(isbn) >> productInfo
    result == productInfo
  }

  def "Find product by isbn without result should throw exception"() {
    when:
    clientWebApiGateway.searchProductByIsbn("isbn")

    then:
    1 * clientWebApiClient.getProductByIsbn(_ as String) >> null
    def e = thrown(ResourceNotFoundException)
    e.message == "CwProductInfo not found: isbn"
  }
}

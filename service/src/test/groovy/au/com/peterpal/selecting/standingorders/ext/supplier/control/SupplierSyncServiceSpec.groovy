package au.com.peterpal.selecting.standingorders.ext.supplier.control

import au.com.peterpal.selecting.standingorders.ext.supplier.boundary.dto.SupplierMessage
import au.com.peterpal.selecting.standingorders.ext.supplier.boundary.dto.SupplierStatus
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.Supplier
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.SupplierId
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import spock.lang.Specification

@DataJpaTest
class SupplierSyncServiceSpec extends Specification {

  @Autowired
  SupplierRepository repo

  SupplierSyncService service

  def setup() {
    service = new SupplierSyncService(repo)
  }

  def "update existing active to inactive"() {
    given:
      def existing = repo.save(Supplier.builder()
          .supplierId(SupplierId.of("da13b8ee-10c0-4c63-954c-a9d05f561a87"))
          .code("PEAAUS")
          .name("PEARSON AUSTRALIA")
          .status(SupplierStatus.ACTIVE)
          .build())
      def msg = SupplierMessage.builder()
          .supplierId(SupplierId.of("da13b8ee-10c0-4c63-954c-a9d05f561a87"))
          .code("PEAAUS")
          .name("PEARSON AUSTRALIA")
          .status(SupplierStatus.INACTIVE)
          .build()

    when:
      service.handle(msg)
      def changed = repo.findById(SupplierId.of("da13b8ee-10c0-4c63-954c-a9d05f561a87"))

    then:
      changed.isPresent() == true
      changed.get().getStatus() == SupplierStatus.INACTIVE
  }

  def "create active supplier"() {
    given:
      def msg = SupplierMessage.builder()
          .supplierId(SupplierId.of("da13b8ee-10c0-4c63-954c-a9d05f561a87"))
          .code("PEAAUS")
          .name("PEARSON AUSTRALIA")
          .status(SupplierStatus.ACTIVE)
          .build()

    when:
      service.handle(msg)
      def created = repo.findById(SupplierId.of("da13b8ee-10c0-4c63-954c-a9d05f561a87"))

    then:
      created.isPresent() == true
      created.get().getCode() == "PEAAUS"
      created.get().getName() == "PEARSON AUSTRALIA"
      created.get().getStatus().equals(SupplierStatus.ACTIVE)
  }

  def "prevent creating INACTIVE suppliers"() {
    given:
      def msg = SupplierMessage.builder()
          .supplierId(SupplierId.of("da13b8ee-10c0-4c63-954c-a9d05f561a87"))
          .code("PEAAUS")
          .name("PEARSON AUSTRALIA")
          .status(SupplierStatus.INACTIVE)
          .build()

    when:
      service.handle(msg)
      def created = repo.findById(SupplierId.of("da13b8ee-10c0-4c63-954c-a9d05f561a87"))

    then:
      created.isPresent() == false

  }

  def "prevent creating more than one supplier with the same code"() {
    given:
      def syncService = Spy(service)
      def existing = repo.save(Supplier.builder()
          .supplierId(SupplierId.of("da13b8ee-10c0-4c63-954c-a9d05f561a87"))
          .code("PEAAUS")
          .name("PEARSON AUSTRALIA")
          .status(SupplierStatus.ACTIVE)
          .build())
      def msg = SupplierMessage.builder()
          .supplierId(SupplierId.of("da13b8ee-10c0-4c63-954c-a9d05f561a88"))
          .code("PEAAUS")
          .name("PEARSON AUSTRALIA")
          .status(SupplierStatus.INACTIVE)
          .build()

    when:
      syncService.handle(msg)
      def created = repo.findById(SupplierId.of("da13b8ee-10c0-4c63-954c-a9d05f561a88"))

    then:
      1 * syncService.create(msg)
      created.isPresent() == false
  }
}

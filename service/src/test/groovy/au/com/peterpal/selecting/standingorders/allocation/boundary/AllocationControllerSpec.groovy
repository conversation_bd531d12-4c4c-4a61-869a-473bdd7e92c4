package au.com.peterpal.selecting.standingorders.allocation.boundary

import au.com.peterpal.common.security.config.CustomKeycloakSpringBootConfigResolver
import au.com.peterpal.selecting.standingorders.SpringSecurityTestConfig
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationService
import au.com.peterpal.selecting.standingorders.allocation.dto.SearchAllocationRequest
import au.com.peterpal.selecting.standingorders.allocation.dto.SearchAllocationResponse
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus
import au.com.peterpal.selecting.standingorders.config.SecurityConfig
import au.com.peterpal.selecting.standingorders.customerstandingorder.control.CustomerStandingOrderBL
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId
import au.com.peterpal.selecting.standingorders.titles.control.RematchAllocationService
import com.fasterxml.jackson.databind.ObjectMapper
import org.keycloak.adapters.springboot.KeycloakSpringBootProperties
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.web.config.EnableSpringDataWebSupport
import org.springframework.security.test.context.support.WithUserDetails
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import static org.hamcrest.Matchers.containsString
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content

@WebMvcTest(controllers = [AllocationController])
@ContextConfiguration(classes = AllocationController.class)
@WithUserDetails("dev")
@EnableSpringDataWebSupport
@Import([SecurityConfig.class, SpringSecurityTestConfig.class, CustomKeycloakSpringBootConfigResolver.class, KeycloakSpringBootProperties.class])
class AllocationControllerSpec extends Specification {
  @Autowired
  private MockMvc mockMvc
  @Autowired
  private ObjectMapper mapper
  @SpringBean
  AllocationService allocationService = Mock()
  @SpringBean
  CustomerStandingOrderBL customerStandingOrderBL = Mock()
  @SpringBean
  RematchAllocationService rematchTitleByAllocationService = Mock()

  def "Search allocations"() {
    given:
    def searchAllocationResponse = new SearchAllocationResponse(
        allocationId: AllocationId.of(UUID.randomUUID()),
        standingOrderId: StandingOrderId.of(UUID.randomUUID()),
        allocationPreferenceId: null,
        customerStandingOrderId: CustomerStandingOrderId.of(UUID.randomUUID()),
        customer: new SearchAllocationResponse.CustomerResponse(customerId: CustomerId.of(UUID.randomUUID()), code: 'BCC'),
        fund: new SearchAllocationResponse.FundResponse(fundId: FundId.of(UUID.randomUUID()), code: 'AFJ'),
        category: 'CGR',
        status: AllocationStatus.ACTIVE,
        customerReference: 'customerReference',
        deliveryInstructions: 'deliveryInstructions',
        notes: 'notes'
    )
    when:
    def results = mockMvc.perform(get('/api/allocations/search'))

    then:
    1 * allocationService.search(SearchAllocationRequest.builder().build(), PageRequest.of(0, 10, Sort.Direction.DESC, "customerStandingOrder.standingOrder.description")) >> new PageImpl<>([searchAllocationResponse])
    results
        .andExpect(status().isOk())
        .andExpect(content().string(containsString(mapper.writeValueAsString(searchAllocationResponse))))
  }

  def "Find customers with allocation"() {
    given:
    def customerId = CustomerId.of(UUID.randomUUID())
    def customerResponse = new SearchAllocationResponse.CustomerResponse(customerId: customerId, code: 'BCC')

    when:
    def results = mockMvc.perform(get('/api/allocations/customers'))

    then:
    1 * allocationService.findCustomersWithAllocation() >> [customerResponse]
    results
        .andExpect(status().isOk())
        .andExpect(content().string(mapper.writeValueAsString([customerResponse])))
  }

  def "Get customer funds"() {
    given:
    def customerCode = 'BCC'
    def fundId = FundId.of(UUID.randomUUID())
    def fundResponse = new SearchAllocationResponse.FundResponse(fundId: fundId, code: 'AFJ')

    when:
    def results = mockMvc.perform(get('/api/allocations/customers/' + customerCode + '/funds'))

    then:
    1 * allocationService.getCustomerFunds(customerCode) >> [fundResponse]
    results
        .andExpect(status().isOk())
        .andExpect(content().string(mapper.writeValueAsString([fundResponse])))
  }
}

package au.com.peterpal.selecting.standingorders.ext.order.control

import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerRepository
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.ext.order.entity.Order
import au.com.peterpal.selecting.standingorders.ext.order.entity.OrderId
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import spock.lang.Specification

import java.time.LocalDate

@DataJpaTest
class OrderRepositorySpec extends Specification {

  @Autowired
  OrderRepository orderRepo

  @Autowired
  CustomerRepository customerRepo

  def "find by ordered product reference in and customer"() {
    given:
      List<String> refs = Arrays.asList("123456789", "4638462846", "8098608080")
      Customer customer = Customer.builder()
          .customerId(CustomerId.of(UUID.randomUUID()))
          .code("BCC")
          .name("Brisbane City Council Library")
          .build()

      Order order = Order.builder()
          .orderId(OrderId.of(UUID.randomUUID()))
          .orderNumber("TO-0001")
          .orderDate(LocalDate.now())
          .customer(customer)
          .orderedProductReference("123456789")
          .build()

      customerRepo.save(customer)
      orderRepo.save(order)

    when:
      List<Order> orders = orderRepo.findByOrderedProductReferenceInAndCustomer(refs, customer)

    then:
      orders != null
      orders.size() == 1
  }
}

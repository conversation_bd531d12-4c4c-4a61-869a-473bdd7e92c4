package au.com.peterpal.selecting.standingorders.pendingorder.control


import au.com.peterpal.common.rest.validation.BusinessException
import au.com.peterpal.selecting.standingorders.allocation.model.Release
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.Supplier
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.SupplierId
import au.com.peterpal.selecting.standingorders.lock.control.PendingOrderSubmittedRepository
import au.com.peterpal.selecting.standingorders.pendingorder.dto.SubmitPendingOrderRequest
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId
import org.assertj.core.util.Lists
import spock.lang.Specification

class SubmitPendingOrderSpecTest extends Specification {

    PendingOrderService pendingOrderService = Mock(PendingOrderService.class)

    String username
    SubmitPendingOrderRequest submitPendingOrderRequest
    Supplier supplier
    SubmitPendingOrderService submitPendingOrderService
    PendingOrderSubmittedRepository pendingOrderSubmittedRepository = Mock(PendingOrderSubmittedRepository.class)

    def setup() {
        submitPendingOrderService = new SubmitPendingOrderService(pendingOrderService, pendingOrderSubmittedRepository)
        username = "junm"
        def titleId = new TitleId()
        submitPendingOrderRequest = SubmitPendingOrderRequest.of(titleId, Lists.newArrayList(), Lists.newArrayList())
        supplier = Supplier.builder().supplierId(new SupplierId()).name("100 MAGIC MILES").code("100MAGMIL").build()
    }


    def "Submit pending order"() {
        given:
        def pendingOrder = PendingOrder.builder()
                .pendingOrderId(new PendingOrderId())
                .orderStatus(PendingOrderStatus.NEW)
                .quantity(1)
                .format(ReleaseFormat.HB)
                .customer(Customer.of(new CustomerId(), "BCC"))
                .release(Release.builder().releaseId(new ReleaseId()).build())
                .supplier(supplier)
                .build()

        when:
        submitPendingOrderService.submitPendingOrders(submitPendingOrderRequest.withAcceptedPendingOrderIds([pendingOrder.pendingOrderId]), username)


        then:
        1 * pendingOrderService.findAllValidPendingOrdersByTitle(_ as TitleId) >> [pendingOrder]
        1 * pendingOrderService.findByIds(_) >> [pendingOrder]
        1 * pendingOrderService.saveAll([pendingOrder]) >> [pendingOrder]

    }


    def "Submit pending order that has 0 qty throw exception"() {
        given:
        def pendingOrder = PendingOrder.builder()
                .pendingOrderId(new PendingOrderId())
                .orderStatus(PendingOrderStatus.NEW)
                .quantity(0)
                .format(ReleaseFormat.HB)
                .customer(Customer.of(new CustomerId(), "BCC"))
                .release(Release.builder().releaseId(new ReleaseId()).build())
                .supplier(supplier)
                .build()
        when:
        submitPendingOrderService.submitPendingOrders(submitPendingOrderRequest.withAcceptedPendingOrderIds([pendingOrder.pendingOrderId]), username)

        then:
        1 * pendingOrderService.findAllValidPendingOrdersByTitle(_ as TitleId) >> [pendingOrder]
        1 * pendingOrderService.findByIds(_) >> [pendingOrder]

        def e = thrown(BusinessException)
        e.message == "Pending order BCC-HB has quantity less than zero."
    }

    def "Submit pending order without supplier should throw exception"() {
        given:
        def pendingOrder = PendingOrder.builder()
                .pendingOrderId(new PendingOrderId())
                .orderStatus(PendingOrderStatus.NEW)
                .quantity(0)
                .format(ReleaseFormat.HB)
                .customer(Customer.of(new CustomerId(), "BCC"))
                .release(Release.builder().releaseId(new ReleaseId()).build())
                .build()
        when:
        submitPendingOrderService.submitPendingOrders(submitPendingOrderRequest.withAcceptedPendingOrderIds([pendingOrder.pendingOrderId]), username)

        then:
        1 * pendingOrderService.findAllValidPendingOrdersByTitle(_ as TitleId) >> [pendingOrder]
        1 * pendingOrderService.findByIds(_) >> [pendingOrder]

        def e = thrown(BusinessException)
        e.message == "No Supplier specified for format HB, Please specify a supplier."
    }

}

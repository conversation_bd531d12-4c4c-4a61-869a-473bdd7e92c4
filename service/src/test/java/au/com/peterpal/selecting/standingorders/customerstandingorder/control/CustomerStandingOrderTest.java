package au.com.peterpal.selecting.standingorders.customerstandingorder.control;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.Branch;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchStatus;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerRepository;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderRepository;
import au.com.peterpal.selecting.standingorders.standingorder.dto.StandingOrderRequest;
import au.com.peterpal.selecting.standingorders.standingorder.dto.StandingOrderResponse;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import org.hamcrest.CoreMatchers;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@DataJpaTest
public class CustomerStandingOrderTest {

  @Autowired
  private CustomerRepository customerRepo;

  @Autowired
  private StandingOrderRepository standOrdRepo;

  @Autowired
  private CustomerStandingOrderRepository custStandOrdRepo;

  @Test
  public void whenFindByCustomerAndStandingOrder_thenReturnCustomerStandingOrder() {
    Customer customer = Customer.builder()
        .customerId(CustomerId.of(UUID.randomUUID()))
        .code("ABC")
        .name("ABC customer")
        .build();
    customer = customerRepo.save(customer);

    StandingOrder standOrder = StandingOrder.builder()
        .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
        .standingOrderNumber("SO-000012345")
        .standingOrderStatus(StandingOrderStatus.ACTIVE)
        .build();
    standOrder = standOrdRepo.save(standOrder);

    CustomerStandingOrder custStandOrder =
        CustomerStandingOrder.builder()
            .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
            .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
            .standingOrder(standOrder)
            .customer(customer)
            .build();
    custStandOrder = custStandOrdRepo.save(custStandOrder);

    Optional<CustomerStandingOrder> csoOptional = custStandOrdRepo.findByCustomerAndStandingOrder(customer, standOrder);

    assertThat(csoOptional.isPresent(), is(true));
  }

  @Test
  public void testSearchStandingOrdersPaginatedByNumber() {
    Customer customer = Customer.builder()
        .customerId(CustomerId.of(UUID.randomUUID()))
        .code("ABC")
        .name("ABC customer")
        .build();
    customer = customerRepo.save(customer);

    StandingOrder standOrder = StandingOrder.builder()
        .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
        .standingOrderNumber("SO-000002345")
        .standingOrderStatus(StandingOrderStatus.ACTIVE)
        .build();
    standOrder = standOrdRepo.save(standOrder);

    CustomerStandingOrder custStandOrder =
        CustomerStandingOrder.builder()
            .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
            .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
            .standingOrder(standOrder)
            .customer(customer)
            .build();
    custStandOrder = custStandOrdRepo.save(custStandOrder);

    standOrder = StandingOrder.builder()
        .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
        .standingOrderNumber("SO-000002445")
        .standingOrderStatus(StandingOrderStatus.ACTIVE)
        .build();
    standOrdRepo.save(standOrder);
    custStandOrder =
        CustomerStandingOrder.builder()
            .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
            .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
            .standingOrder(standOrder)
            .customer(customer)
            .build();
    custStandOrdRepo.save(custStandOrder);

    standOrder = StandingOrder.builder()
        .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
        .standingOrderNumber("SO-000003445")
        .standingOrderStatus(StandingOrderStatus.ACTIVE)
        .build();
    standOrdRepo.save(standOrder);
    custStandOrder =
        CustomerStandingOrder.builder()
            .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
            .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
            .standingOrder(standOrder)
            .customer(customer)
            .build();
    custStandOrdRepo.save(custStandOrder);

    StandingOrderRequest request = StandingOrderRequest.builder()
        .standingOrderNumber("SO-000002")
        .build();
    Page<StandingOrderResponse> page = standOrdRepo.search(request, PageRequest.of(0, 3));

    assertThat(page, CoreMatchers.is(notNullValue()));
    assertThat(page.getTotalElements(), is(2L));
  }

  @Test
  public void testSearchStandingOrdersPaginatedByCustomer() {
    CustomerId ci = CustomerId.of(UUID.randomUUID());
    Customer customer = Customer.builder()
        .customerId(ci)
        .code("ABC")
        .name("ABC customer")
        .build();
    customer = customerRepo.save(customer);

    StandingOrder standOrder = StandingOrder.builder()
        .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
        .standingOrderNumber("SO-000002345")
        .standingOrderStatus(StandingOrderStatus.ACTIVE)
        .build();
    standOrder = standOrdRepo.save(standOrder);

    CustomerStandingOrder custStandOrder =
        CustomerStandingOrder.builder()
            .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
            .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
            .standingOrder(standOrder)
            .customer(customer)
            .build();
    custStandOrder = custStandOrdRepo.save(custStandOrder);

    standOrder = StandingOrder.builder()
        .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
        .standingOrderNumber("SO-000002445")
        .standingOrderStatus(StandingOrderStatus.ACTIVE)
        .build();
    standOrdRepo.save(standOrder);
    custStandOrder =
        CustomerStandingOrder.builder()
            .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
            .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
            .standingOrder(standOrder)
            .customer(customer)
            .build();
    custStandOrdRepo.save(custStandOrder);

    standOrder = StandingOrder.builder()
        .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
        .standingOrderNumber("SO-000003445")
        .standingOrderStatus(StandingOrderStatus.ACTIVE)
        .build();
    standOrdRepo.save(standOrder);
    custStandOrder =
        CustomerStandingOrder.builder()
            .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
            .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
            .standingOrder(standOrder)
            .customer(customer)
            .build();
    custStandOrdRepo.save(custStandOrder);

    StandingOrderRequest request = StandingOrderRequest.builder()
        .customerId(ci)
        .build();
    Page<StandingOrderResponse> page = standOrdRepo.search(request, PageRequest.of(0, 3));

    assertThat(page, CoreMatchers.is(notNullValue()));
    assertThat(page.getTotalElements(), is(3L));
  }

  @Test
  public void testBatchRead() {
    int[] arr = {1,2,3,4,5,6,7,8,9};
    List<Integer> list = Arrays.stream(arr).boxed().collect(Collectors.toList());
    int batchSize = 3;
    int count = 0;
    for (int i = 0; i < list.size(); i = i + batchSize) {
      int toIndex = i + batchSize > list.size() ? list.size() : i + batchSize;
      List<Integer> slice = list.subList(i, toIndex);
      for (int j = 0; j < slice.size(); j++) {
        assertThat(slice.get(j) == list.get(i + j), is(true));
      }
      count++;
    }
    assertThat(count, is(3));

    int[] arr2 = {1,2,3,4,5,6,7,8,9,10};
    count = 0;
    list = Arrays.stream(arr2).boxed().collect(Collectors.toList());
    for (int i = 0; i < list.size(); i = i + batchSize) {
      int toIndex = i + batchSize > list.size() ? list.size() : i + batchSize;
      List<Integer> slice = list.subList(i, toIndex);
      for (int j = 0; j < slice.size(); j++) {
        assertThat(slice.get(j) == list.get(i + j), is(true));
      }
      count++;
    }
    assertThat(count, is(4));
  }

  @Test
  public void testAllocationSelection() {

    CustomerStandingOrder custStandOrder =
      CustomerStandingOrder.builder()
        .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
        .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
        .build();

    Category categoryAF = Category.builder().categoryId(new CategoryId()).code("AF")
        .description("desc").status(
            CategoryStatus.ACTIVE).build();
    Category categoryYY = Category.builder().categoryId(new CategoryId()).code("YY")
        .description("desc").status(CategoryStatus.ACTIVE).build();

    Allocation allocation = Allocation.builder()
        .allocationId(AllocationId.of(UUID.randomUUID()))
        .status(AllocationStatus.ACTIVE)
        .categories(List.of(categoryAF))
        .branch(Branch.builder()
            .branchId(BranchId.of(UUID.randomUUID()))
            .name("Annerley")
            .code("ANN")
            .status(BranchStatus.ACTIVE)
            .build()
        )
        .build();
    custStandOrder.addAllocation(allocation);
    allocation = Allocation.builder()
        .allocationId(AllocationId.of(UUID.randomUUID()))
        .status(AllocationStatus.ACTIVE)
        .categories(List.of(categoryAF))
        .branch(Branch.builder()
            .branchId(BranchId.of(UUID.randomUUID()))
            .name("Brisbane Square")
            .code("BSQ")
            .status(BranchStatus.ACTIVE)
            .build()
        )
        .build();
    custStandOrder.addAllocation(allocation);
    allocation = Allocation.builder()
        .allocationId(AllocationId.of(UUID.randomUUID()))
        .status(AllocationStatus.ACTIVE)
        .categories(List.of(categoryAF))
        .branch(null)
        .build();
    custStandOrder.addAllocation(allocation);

    allocation = Allocation.builder()
        .allocationId(AllocationId.of(UUID.randomUUID()))
        .status(AllocationStatus.ACTIVE)
        .categories(List.of(categoryYY))
        .branch(null)
        .build();
    custStandOrder.addAllocation(allocation);


    List<Allocation> al = custStandOrder.getAllocations(new ArrayList<>(), false);
    assertThat(al, notNullValue());
    assertThat(al.isEmpty(), is(false));
    assertThat(al.size(), is(2));

    al = custStandOrder.getAllocations(new ArrayList<>(), true);
    assertThat(al, notNullValue());
    assertThat(al.isEmpty(), is(false));
    assertThat(al.size(), is(2));

    al = custStandOrder.getAllocations(List.of(categoryAF), true);
    assertThat(al, notNullValue());
    assertThat(al.isEmpty(), is(false));
    assertThat(al.size(), is(2));

    al = custStandOrder.getAllocations(List.of(categoryYY), true);
    assertThat(al, notNullValue());
    assertThat(al.isEmpty(), is(true));

    al = custStandOrder.getAllocations(List.of(categoryAF), false);
    assertThat(al, notNullValue());
    assertThat(al.isEmpty(), is(false));
    assertThat(al.size(), is(1));
  }
}

package au.com.peterpal.selecting.standingorders.standingorder;

import au.com.peterpal.common.audit.EventPublisher;
import org.springframework.beans.factory.annotation.Autowired;

//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration(classes = { SynchronousSpringEventsConfig.class }, loader = AnnotationConfigContextLoader.class)
public class CustomSpringEventPublisherTest {

  @Autowired
  private EventPublisher publisher;
//  @Autowired
//  private AnnotationDrivenEventListener listener;

//  @Test
  public void publishEvent() {
    //    isTrue(!listener.isHitCustomEventHandler(), "The value should be false");

//    StandingOrderCreated standingOrderCreated =
//        StandingOrderCreated.builder()
//            .id(new U())
//            .description("==================== event one")
//            .notes("notes")
//            .build();

//    publisher.publishEvent(standingOrderCreated, "ianr");
//    System.out.println(" ");
//    System.out.println("Done publishing synchronous custom event. ");
//    isTrue(listener.isHitCustomEventHandler(), "Now the value should be changed to true");
  }

//  @Test
  public void publishEventStandingOrderChanged() {
//    isTrue(!listener.isHitCustomEventHandler(), "The value should be false");

//    StandingOrderChanged standingOrderChanged =
//        StandingOrderChanged.builder()
//            .id(new StandingOrderId())
//            .description("================= event two")
//            .notes("notes")
//            .build();
//
//    publisher.publishEvent(standingOrderChanged, "ianr");
    System.out.println(" ");
    System.out.println("Done publishing synchronous custom event. ");
//    isTrue(listener.isHitCustomEventHandler(), "Now the value should be changed to true");
  }
}
package au.com.peterpal.selecting.standingorders.allocationpreference.boundary;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.common.security.config.CustomKeycloakSpringBootConfigResolver;
import au.com.peterpal.selecting.standingorders.SpringSecurityTestConfig;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.AllocationPreferenceBL;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.ReleasePreferenceBL;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AllocationPreferenceRequest;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AllocationPreferenceResponse;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.config.SecurityConfig;
import au.com.peterpal.selecting.standingorders.ext.customer.dto.CustomerInfo;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.keycloak.adapters.springboot.KeycloakSpringBootProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.config.EnableSpringDataWebSupport;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(AllocationPreferenceController.class)
@ContextConfiguration(classes = AllocationPreferenceController.class)
@EnableSpringDataWebSupport
@WithUserDetails("dev")
@Import({
  SecurityConfig.class,
  SpringSecurityTestConfig.class,
  CustomKeycloakSpringBootConfigResolver.class,
  KeycloakSpringBootProperties.class
})
public class AllocationPreferenceControllerSearchTest {

  private static final String BASE_URL = "/api/allocation-preferences";

  @Rule public ExpectedException rule = ExpectedException.none();
  @Autowired private MockMvc mvc;
  @MockBean AllocationPreferenceBL allocationPreferenceBL;
  @MockBean ReleasePreferenceBL releasePreferenceBL;

  private List<AllocationPreferenceResponse> searchResult;

  @Before
  public void init() {
    CustomerInfo customerInfo =
        CustomerInfo.builder()
            .id(CustomerId.of("a723df17-a4f7-4198-842f-418d017817ac"))
            .code("CUST01")
            .name("Customer Test 01")
            .build();

    searchResult =
        Arrays.asList(
            AllocationPreferenceResponse.builder()
                .allocationPreferenceId(
                    AllocationPreferenceId.of("c7793c2e-f5f8-4981-a79e-b8d481b109ca"))
                .customerReference("CUST-REF01")
                .categories(List.of("YFP"))
                .deliveryInstructions("Deliver instruction test")
                .notes("test")
                .build(),
            AllocationPreferenceResponse.builder()
                .allocationPreferenceId(
                    AllocationPreferenceId.of("4896f855-2bf6-405f-9340-4095efcdaccd"))
                .customerReference("CUST-REF02")
                .categories(List.of("AN"))
                .deliveryInstructions("Deliver instruction test 2")
                .notes("test note 2")
                .build());
  }

  @Test
  public void findAllocationPreferenceByCustomerCode() throws Exception {
    String code = "CUST01";

    when(allocationPreferenceBL.handle(any(AllocationPreferenceRequest.class), any(Pageable.class)))
        .thenReturn(new PageImpl<>(searchResult));

    String url = BASE_URL + "/find-by-customer?customerCode=%s";

    mvc.perform(MockMvcRequestBuilders.get(String.format(url, code)))
        .andDo(print())
        .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  public void findAllocationPreferenceByCustomerCodeNotExist() throws Exception {
    String customerCode = "CODE_NOT_EXIST";
    String url = BASE_URL + "/find-by-customer?customerCode=%s";

    when(allocationPreferenceBL.handle(
            eq(AllocationPreferenceRequest.of(customerCode)), any(Pageable.class)))
        .thenThrow(new ResourceNotFoundException(Customer.class, customerCode));

    mvc.perform(MockMvcRequestBuilders.get(String.format(url, customerCode)))
        .andExpect(status().isNotFound());
  }

  @Test
  public void findAllocationPreferenceByCustomerIdNotProvided() throws Exception {
    String url = BASE_URL + "/find-by-customer";

    mvc.perform(MockMvcRequestBuilders.get(url)).andDo(print()).andExpect(status().isBadRequest());
  }
}

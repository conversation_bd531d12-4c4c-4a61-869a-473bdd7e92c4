package au.com.peterpal.selecting.standingorders;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;

import java.util.Arrays;

@TestConfiguration
public class SpringSecurityTestConfig {

  private final String ROLE_ADMIN = "ROLE_Admin";
  private final String ROLE_USER = "ROLE_User";
  private final String ROLE_OTHER = "ROLE_Other";

  @Bean
  public UserDetailsService userDetailsService() {
    User admin =
        new User(
            "dev",
            "dev",
            Arrays.asList(
                new SimpleGrantedAuthority(ROLE_ADMIN), new SimpleGrantedAuthority(ROLE_USER)));
    User user = new User("user", "user", Arrays.asList(new SimpleGrantedAuthority(ROLE_USER)));
    User other = new User("other", "other", Arrays.asList(
            new SimpleGrantedAuthority(ROLE_OTHER), new SimpleGrantedAuthority(ROLE_OTHER)));

    return new InMemoryUserDetailsManager(Arrays.asList(admin, user, other));
  }
}

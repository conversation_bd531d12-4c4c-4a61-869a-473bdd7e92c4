package au.com.peterpal.selecting.standingorders.allocationpreference.boundary;

import au.com.peterpal.common.security.config.CustomKeycloakSpringBootConfigResolver;
import au.com.peterpal.selecting.standingorders.SpringSecurityTestConfig;
import au.com.peterpal.selecting.standingorders.allocationpreference.commands.RemoveReleasePreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.AllocationPreferenceBL;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.ReleasePreferenceBL;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.ReleasePreferenceInfo;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.UpdateAllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.*;
import au.com.peterpal.selecting.standingorders.config.SecurityConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.keycloak.adapters.springboot.KeycloakSpringBootProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(AllocationPreferenceController.class)
@ContextConfiguration(classes = AllocationPreferenceController.class)
@WithUserDetails("dev")
@Import({
  SecurityConfig.class,
  SpringSecurityTestConfig.class,
  CustomKeycloakSpringBootConfigResolver.class,
  KeycloakSpringBootProperties.class
})
public class AllocationPreferenceControllerTest {

  private static final String BASE_URL = "/api/allocation-preferences";
  private static final String USERNAME = "userdev";
  private static final String HEADER_USERNAME = "username";

  @Autowired private MockMvc mvc;
  @MockBean AllocationPreferenceBL allocationPreferenceBL;
  @MockBean ReleasePreferenceBL releasePreferenceBL;

  private ObjectMapper objectMapper;

  @Before
  public void init() {
    objectMapper = new ObjectMapper();
  }

  @Test
  public void removeAllocationPreference() throws Exception {

    UUID allocationPreferenceId = UUID.randomUUID();
    mvc.perform(
            delete(String.format(BASE_URL + "/%s", allocationPreferenceId))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  public void updateAllocationPreference() throws Exception {
    AllocationPreferenceId allocationPreferenceId = AllocationPreferenceId.of(UUID.randomUUID());

    UpdateAllocationPreference updateAllocationPreference =
        UpdateAllocationPreference.builder()
            .category(List.of("Y"))
            .customerReference("customer refe")
            .deliveryInstructions("delivery")
            .notes("Notes")
            .fundCode("BCC")
            .build();

    String url = BASE_URL + "/%s";

    mvc.perform(
            MockMvcRequestBuilders.put(String.format(url, allocationPreferenceId))
                .header(HEADER_USERNAME, USERNAME)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateAllocationPreference)))
        .andExpect(status().isOk());
  }

  @Test
  public void updateAllocationPreferenceNoCategory() throws Exception {
    AllocationPreferenceId allocationPreferenceId = AllocationPreferenceId.of(UUID.randomUUID());

    UpdateAllocationPreference updateAllocationPreference =
        UpdateAllocationPreference.builder()
            .customerReference("customer refe")
            .deliveryInstructions("delivery")
            .notes("Notes")
            .fundCode("BCC")
            .build();

    String url = BASE_URL + "/%s";

    mvc.perform(
            MockMvcRequestBuilders.put(String.format(url, allocationPreferenceId))
                .header(HEADER_USERNAME, USERNAME)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateAllocationPreference)))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void AddReleasePreference() throws Exception {
    UUID allocationPreferenceId = UUID.randomUUID();

    ReleasePreferenceInfo createReleasePreference =
        ReleasePreferenceInfo.builder()
            .releaseType(ReleaseType.INITIAL)
            .actionType(ActionType.ORDER.name())
            .fundCode("FCODE01")
            .hbFundCode("HBFUNDCODE01")
            .initialAssignmentRule(AssignmentRule.FIRST_AVAILABLE.name())
            .pbFundCode("PBFUNDCODE02")
            .smallFormatPaperbackRule(SmallFormatPaperbackRule.FIRST.name())
            .build();

    mvc.perform(
            MockMvcRequestBuilders.post(
                    String.format(BASE_URL + "/%s/release-preference", allocationPreferenceId))
                .header(HEADER_USERNAME, USERNAME)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createReleasePreference)))
        .andExpect(status().isCreated());
  }

  @Test
  public void removeReleasePreference() throws Exception {
    UUID releasePreferenceUuid = UUID.randomUUID();
    ReleasePreferenceId releasePreferenceId = ReleasePreferenceId.of(releasePreferenceUuid);

    String url = BASE_URL + "/release-preferences/%s";

    when(releasePreferenceBL.handle(
            eq(RemoveReleasePreference.of(releasePreferenceId)), eq(USERNAME)))
        .thenReturn(releasePreferenceId);

    mvc.perform(
            MockMvcRequestBuilders.delete(String.format(url, releasePreferenceUuid))
                .header(HEADER_USERNAME, USERNAME))
        .andDo(print())
        .andExpect(content().json(objectMapper.writeValueAsString(releasePreferenceUuid)))
        .andExpect(status().isOk());
  }
}

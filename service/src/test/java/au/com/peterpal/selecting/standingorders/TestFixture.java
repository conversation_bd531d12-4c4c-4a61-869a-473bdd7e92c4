package au.com.peterpal.selecting.standingorders;

import au.com.peterpal.selecting.standingorders.allocation.control.AllocationRepository;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.AllocationPreferenceRepository;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.ReleasePreferenceRepository;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.SmallFormatPaperbackRule;
import au.com.peterpal.selecting.standingorders.ext.category.control.CategoryRepository;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus;
import au.com.peterpal.selecting.standingorders.customerstandingorder.control.CustomerStandingOrderRepository;
import au.com.peterpal.selecting.standingorders.customerstandingorder.control.ReleaseRepository;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerRepository;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.boundary.dto.FundStatus;
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundRepository;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderRepository;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import au.com.peterpal.selecting.standingorders.titles.control.TitleRepository;
import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Profile;

// TODO SELSW-351 fix test class after removing so association from title
@Log4j2
@Data
@Profile("development")
public class TestFixture {

  private AllocationRepository allocationRepository;
  private ReleaseRepository releaseRepository;
  private FundRepository fundRepository;
  private CustomerStandingOrderRepository customerStandingOrderRepository;
  private ReleasePreferenceRepository releasePreferenceRepository;
  private AllocationPreferenceRepository allocationPreferenceRepository;
  private CustomerRepository customerRepository;
  private StandingOrderRepository standingOrderRepository;
  private TitleRepository titleRepository;
  private CategoryRepository categoryRepository;

  private Customer customer;

  private List<Customer> customers;

  private List<AllocationPreference> allocationPreferences;
  private List<ReleasePreference> releasePreferences;
  private List<CustomerStandingOrder> customerStandingOrderList;
  private List<Release> releases;
  private List<Allocation> allocationList;
  private List<StandingOrder> standingOrderList;
  private Fund fund;
  private Fund pbFund;
  private Fund hbFund;
  private Category categoryYFP;
  private Category categoryAN;
  private Category categoryY;
  private Category categoryZ;

  public TestFixture(
      ReleasePreferenceRepository releasePreferenceRepository,
      AllocationPreferenceRepository allocationPreferenceRepository,
      CustomerRepository customerRepository,
      StandingOrderRepository standingOrderRepository,
      CustomerStandingOrderRepository customerStandingOrderRepository,
      ReleaseRepository releaseRepository,
      AllocationRepository allocationRepository,
      FundRepository fundRepository,
      TitleRepository titleRepository,
      CategoryRepository categoryRepository) {

    this.releasePreferenceRepository = releasePreferenceRepository;
    this.allocationPreferenceRepository = allocationPreferenceRepository;
    this.customerRepository = customerRepository;
    this.standingOrderRepository = standingOrderRepository;
    this.customerStandingOrderRepository = customerStandingOrderRepository;
    this.releaseRepository = releaseRepository;
    this.allocationRepository = allocationRepository;
    this.fundRepository = fundRepository;
    this.titleRepository = titleRepository;
    this.categoryRepository = categoryRepository;
  }

  public void initData() {
    initCategoryData();
    initCustomerData();
    initFundData();
    initAllocationPreferenceData();
    initReleasePreferenceData();

    initStandingOrderData();
    initCustomerStandingOrderData();
    initTitleData();
    initReleaseData();
  }

  private void initFundData() {
    fund =
        fundRepository.save(
            Fund.builder()
                .code("FUND01")
                .customer(customer)
                .fundId(FundId.of("c6aeeb5d-c093-49d2-9c7d-b27099b868b3"))
                .name("FUND01")
                .status(FundStatus.ACTIVE)
                .build());

    pbFund =
        fundRepository.save(
            Fund.builder()
                .code("PBFUND02")
                .customer(customer)
                .fundId(FundId.of("ffd0f7f6-8dcd-418d-a111-d95dfda82aec"))
                .name("PBFUND02")
                .status(FundStatus.ACTIVE)
                .build());

    hbFund =
        fundRepository.save(
            Fund.builder()
                .code("HBFUND03")
                .customer(customer)
                .fundId(FundId.of("6c6d508e-b41a-48f5-a47e-4a7fe4970b78"))
                .name("HBFUND03")
                .status(FundStatus.ACTIVE)
                .build());
  }

  private void initTitleData() {
    List<Title> titles =
        Arrays.asList(
            Title.builder()
                .titleId(TitleId.of(UUID.fromString("c6aeeb5d-c093-49d2-9c7d-b27099b868a1")))
                .title("The Old Man And The Dog")
                .personName("eko, firdaus")
                .build(),
            Title.builder()
                .titleId(TitleId.of(UUID.fromString("c6aeeb5d-c093-49d2-9c7d-b27099b868a2")))
                .title("The Old Man And The Dog")
                .personName("eko, firdaus")
                .build(),
            Title.builder()
                .titleId(TitleId.of(UUID.randomUUID()))
                .titleStatus(TitleStatus.NEW)
                .title("The Old Man And The Dog")
                .personName("eko, firdaus")
                .build(),
            Title.builder()
                .titleId(TitleId.of(UUID.randomUUID()))
                .titleStatus(TitleStatus.NEW)
                .title("The Mouse and The Motorcycle")
                .personName("eko, firdaus")
                .build(),
            Title.builder()
                .titleId(TitleId.of(UUID.randomUUID()))
                .titleStatus(TitleStatus.NEW)
                .title("Some title")
                .personName("eko, firdaus")
                .build());
    titleRepository.saveAll(titles);
  }

  private void initAllocationData() {
    List<Allocation> allocationsToSave =
        Arrays.asList(
            Allocation.builder()
                .allocationId(AllocationId.of("56921280-ff76-467e-b8be-4e5fe9ab1fa8"))
                .allocationPreference(allocationPreferences.get(0))
                .categories(List.of(categoryY))
                .customerReference("CUSTREF01")
                .customerStandingOrder(customerStandingOrderList.get(0))
                .deliveryInstructions("deliveryInstructions")
                .fund(fund)
                .status(AllocationStatus.ACTIVE)
                .notes("note")
                .build(),
            Allocation.builder()
                .allocationId(AllocationId.of(UUID.randomUUID()))
                .allocationPreference(allocationPreferences.get(0))
                .categories(List.of(categoryZ))
                .customerReference("CUSTREF02")
                .customerStandingOrder(customerStandingOrderList.get(0))
                .deliveryInstructions("deliveryInstructions")
                .fund(fund)
                .status(AllocationStatus.ACTIVE)
                .notes("note2")
                .build(),
            Allocation.builder()
                .allocationId(AllocationId.of(UUID.randomUUID()))
                .allocationPreference(allocationPreferences.get(0))
                .categories(List.of(categoryY))
                .customerReference("CUSTREF03")
                .customerStandingOrder(customerStandingOrderList.get(1))
                .deliveryInstructions("deliveryInstructions")
                .fund(fund)
                .status(AllocationStatus.ACTIVE)
                .notes("note3")
                .build(),
            Allocation.builder()
                .allocationId(AllocationId.of(UUID.randomUUID()))
                .allocationPreference(allocationPreferences.get(0))
                .categories(List.of(categoryY))
                .customerReference("CUSTREF04")
                .customerStandingOrder(customerStandingOrderList.get(2))
                .deliveryInstructions("deliveryInstructions")
                .fund(fund)
                .status(AllocationStatus.PAUSED)
                .notes("note4")
                .build(),
            Allocation.builder()
                .allocationId(AllocationId.of(UUID.randomUUID()))
                .allocationPreference(allocationPreferences.get(0))
                .categories(List.of(categoryY))
                .customerReference("CUSTREF05")
                .customerStandingOrder(customerStandingOrderList.get(0))
                .deliveryInstructions("deliveryInstructions")
                .fund(fund)
                .status(AllocationStatus.INACTIVE)
                .notes("note5")
                .build());

    this.allocationList = allocationRepository.saveAll(allocationsToSave);
  }

  private void initCustomerStandingOrderData() {
    List<CustomerStandingOrder> customerStandingOrderList =
        Arrays.asList(
            CustomerStandingOrder.builder()
                .customer(customer)
                .customerStandingOrderId(
                    CustomerStandingOrderId.of("b01cb8d1-0ed4-48e1-9a99-fb44fde76469"))
                .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                .standingOrder(standingOrderList.get(0))
                .build(),
            CustomerStandingOrder.builder()
                .customer(customers.get(0))
                .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
                .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                .standingOrder(standingOrderList.get(1))
                .build(),
            CustomerStandingOrder.builder()
                .customer(customers.get(1))
                .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
                .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
                .standingOrder(standingOrderList.get(1))
                .build());

    this.customerStandingOrderList =
        customerStandingOrderRepository.saveAll(customerStandingOrderList);
    initAllocationData();

    customerStandingOrderList.get(0).addAllocation(allocationList.get(0));
    customerStandingOrderList.get(0).addAllocation(allocationList.get(1));
    customerStandingOrderList.get(0).addAllocation(allocationList.get(4));
    customerStandingOrderList.get(1).addAllocation(allocationList.get(2));
    customerStandingOrderList.get(2).addAllocation(allocationList.get(3));

    this.customerStandingOrderList =
        customerStandingOrderRepository.saveAll(customerStandingOrderList);
  }

  private void initAllocationPreferenceData() {
    List<AllocationPreference> allocationPreferenceListToSave =
        Arrays.asList(
            AllocationPreference.builder()
                .allocationPreferenceId(
                    AllocationPreferenceId.of("c7793c2e-f5f8-4981-a79e-b8d481b109ca"))
                .customerReference("CUST-REF01")
                .categories(List.of(categoryYFP))
                .customer(customer)
                .deliveryInstructions("Deliver instruction test")
                .notes("test")
                .build(),
            AllocationPreference.builder()
                .allocationPreferenceId(
                    AllocationPreferenceId.of("4896f855-2bf6-405f-9340-4095efcdaccd"))
                .customerReference("CUST-REF02")
                .categories(List.of(categoryAN))
                .customer(customer)
                .deliveryInstructions("Deliver instruction test 2")
                .notes("test note 2")
                .build());

    allocationPreferences = allocationPreferenceRepository.saveAll(allocationPreferenceListToSave);
  }

  private void initCustomerData() {
    customer =
        customerRepository.save(
            Customer.builder()
                .customerId(CustomerId.of("a723df17-a4f7-4198-842f-418d017817ac"))
                .code("CUST01")
                .name("Customer Test 01")
                .build());
    List<Customer> customersToSave =
        Arrays.asList(
            Customer.builder()
                .customerId(CustomerId.of(UUID.randomUUID()))
                .code("CUST02")
                .name("Customer Test 02")
                .build(),
            Customer.builder()
                .customerId(CustomerId.of(UUID.randomUUID()))
                .code("CUST03")
                .name("Customer Test 03")
                .build());
    this.customers = customerRepository.saveAll(customersToSave);
  }

  private void initReleasePreferenceData() {
    List<ReleasePreference> releasePreferencesToSave =
        Collections.singletonList(
            ReleasePreference.builder()
                .releasePreferenceId(ReleasePreferenceId.of(UUID.randomUUID()))
                .allocationPreference(allocationPreferences.get(0))
                .releaseType(ReleaseType.INITIAL)
                .actionType(ActionType.ORDER)
                .initialAssignmentRule(AssignmentRule.FIRST_AVAILABLE)
                .smallFormatPaperbackRule(SmallFormatPaperbackRule.ACCEPT)
                .fund(fund)
                .hardbackfund(hbFund)
                .paperbackfund(pbFund)
                .build());

    releasePreferences = releasePreferenceRepository.saveAll(releasePreferencesToSave);
  }

  private void initReleaseData() {
    List<Release> releasesToSave =
        Collections.singletonList(
            Release.builder()
                .releaseId(ReleaseId.of(UUID.randomUUID()))
                .actionType(ActionType.ORDER)
                .fund(fund)
                .hardbackfund(hbFund)
                .paperbackfund(pbFund)
                .initialAssignmentRule(AssignmentRule.FIRST_AVAILABLE)
                .quantity(3)
                .hardbackQuantity(2)
                .paperbackQuantity(1)
                .preference(releasePreferences.get(0))
                .releaseType(ReleaseType.INITIAL)
                .smallFormatPaperbackRule(SmallFormatPaperbackRule.ACCEPT)
                .allocation(allocationList.get(0))
                .build());

    releases = releaseRepository.saveAll(releasesToSave);
  }
  private void initCategoryData() {
     categoryYFP = Category.builder().categoryId(new CategoryId()).code("YFP")
        .description("desc").status(
            CategoryStatus.ACTIVE).build();
    categoryYFP =categoryRepository.saveAndFlush(categoryYFP);

     categoryAN = Category.builder().categoryId(new CategoryId()).code("AN")
        .description("desc").status(
            CategoryStatus.ACTIVE).build();
    categoryAN =categoryRepository.saveAndFlush(categoryAN);

    categoryY = Category.builder().categoryId(new CategoryId()).code("Y")
        .description("desc").status(CategoryStatus.ACTIVE).build();
    categoryY = categoryRepository.saveAndFlush(categoryY);

    categoryZ = Category.builder().categoryId(new CategoryId()).code("Z")
        .description("desc").status(CategoryStatus.ACTIVE).build();
    categoryZ = categoryRepository.saveAndFlush(categoryZ);
  }

  private void initStandingOrderData() {
    List<StandingOrder> standingOrderList =
        Arrays.asList(
            StandingOrder.builder()
                .description("standingOrderDescription1")
                .notes("notes1")
                .standingOrderId(StandingOrderId.of("b764c64f-ecd0-4afe-b6a5-4b8d183e9a59"))
                .standingOrderNumber("SON01")
                .standingOrderStatus(StandingOrderStatus.ACTIVE)
                .build(),
            StandingOrder.builder()
                .description("standingOrderDescription1")
                .notes("notes2")
                .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
                .standingOrderNumber("SON02")
                .standingOrderStatus(StandingOrderStatus.ACTIVE)
                .build());

    this.standingOrderList = standingOrderRepository.saveAll(standingOrderList);
  }
}

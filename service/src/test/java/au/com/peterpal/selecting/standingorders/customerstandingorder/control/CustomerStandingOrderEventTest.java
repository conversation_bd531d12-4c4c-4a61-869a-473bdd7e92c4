package au.com.peterpal.selecting.standingorders.customerstandingorder.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationRepository;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.*;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus;
import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.CreateOrUpdateAllocation;
import au.com.peterpal.selecting.standingorders.customerstandingorder.events.*;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.boundary.dto.FundStatus;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderBL;
import au.com.peterpal.selecting.standingorders.standingorder.model.OperationType;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import au.com.peterpal.selecting.standingorders.standingorder.model.Term;
import au.com.peterpal.selecting.standingorders.standingorder.model.TermId;
import au.com.peterpal.selecting.standingorders.titles.control.MatchAndMergeTitleService;
import au.com.peterpal.selecting.standingorders.titles.control.ProductMatchingBL;
import au.com.peterpal.selecting.standingorders.titles.control.TitleBL;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.cloud.openfeign.ribbon.FeignRibbonClientAutoConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

@ImportAutoConfiguration({FeignAutoConfiguration.class, FeignRibbonClientAutoConfiguration.class})
@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(properties = {"match-service.restricted-publishers=", "title.un-defer.scheduled.cron=0 1 0 * * *"})
public class CustomerStandingOrderEventTest {

  @Rule
  public ExpectedException expectedException = ExpectedException.none();

  @MockBean
  AllocationRepository allocationRepository;

  @Autowired
  private EventPublisher publisher;

  @MockBean
  CustomerStandingOrderRepository customerStandingOrderRepository;

  @MockBean
  PendingOrderBL pendingOrderBL;
  @MockBean
  ProductMatchingBL productMatchingBL;
  @MockBean
  MatchAndMergeTitleService matchAndMergeTitleService;
  @MockBean
  TitleBL titleBL;

  private Customer customer;
  private String customerCode;
  private Fund fund;
  private AllocationPreference allocationPreference;
  private Allocation allocation;
  // private AllocationCreated allocationCreated;
  private CreateOrUpdateAllocation createAllocation;
  private CustomerStandingOrder customerStandingOrder;
  private AllocationUpdated allocationUpdated;
  private NoteAdded noteAdded;
  private List<CreateOrUpdateAllocation> createorUpdateAllocations = new ArrayList<>();
  private StandingOrder standingOrder;
  private CustomerStandingOrderUpdated customerStandingOrderUpdated;
  private List<CreateOrUpdateAllocation> createAllocations = new ArrayList<>();
  private List<Term> listTerms = new ArrayList<>();
  private CategoryAssigned categoryAssigned;

  @Before
  public void setUp() {
    customerCode = "BCC";
    customer = Customer.of(CustomerId.of(UUID.randomUUID()), customerCode);

    fund =
        Fund.builder()
            .fundId(FundId.of(UUID.randomUUID()))
            .code("BCC")
            .customer(customer)
            .name("name")
            .status(FundStatus.ACTIVE)
            .build();

    Category categoryY = Category.builder().categoryId(new CategoryId()).code("Y").status(
        CategoryStatus.ACTIVE).build();

    allocationPreference =
        AllocationPreference.builder()
            .allocationPreferenceId(AllocationPreferenceId.of(UUID.randomUUID()))
            .customer(customer)
            .categories(List.of(categoryY))
            .customerReference("test")
            .deliveryInstructions("delivery")
            .notes("notes")
            .fund(fund)
            .build();

    createAllocation =
        CreateOrUpdateAllocation.builder()
            .id(UUID.randomUUID())
            .allocationPreference(allocationPreference)
            .categories(allocationPreference.getCategories())
            .customerReference(allocationPreference.getCustomerReference())
            .deliveryInstructions(allocationPreference.getDeliveryInstructions())
            .fund(allocationPreference.getFund())
            .notes(allocationPreference.getNotes())
            .build();

    createorUpdateAllocations.add(createAllocation);

    customerStandingOrder =
        CustomerStandingOrder.builder()
            .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
            .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
            .customer(customer)
            .build();

    Term termData =
        Term.builder()
            .termId(TermId.of(UUID.randomUUID()))
            .operation(OperationType.EXACT_MATCH)
            .build();

    listTerms.add(termData);

    standingOrder =
        StandingOrder.builder()
            .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
            .notes("notes")
            .description("description")
            .standingOrderNumber("12345")
            .standingOrderStatus(StandingOrderStatus.ACTIVE)
            .terms(listTerms)
            .build();

    customerStandingOrderUpdated =
        CustomerStandingOrderUpdated.builder()
            .id(customerStandingOrder.getCustomerStandingOrderId().getId())
            .customerStandingOrderId(customerStandingOrder.getCustomerStandingOrderId())
            .customerId(customer.getCustomerId())
            .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
            .standingOrderId(standingOrder.getStandingOrderId())
            .username("developer")
            .build();

     Category categoryYFP = Category.builder().categoryId(new CategoryId()).code("YFP")
        .description("desc").status(
            CategoryStatus.ACTIVE).build();

    allocationUpdated =
        AllocationUpdated.builder()
            .id(UUID.randomUUID())
            .customerReference("Test customer reference")
            .deliveryInstructions("Delivery instruction")
            .notes("Notes")
            .customerStandingOrderId(customerStandingOrder.getCustomerStandingOrderId())
            .allocationPreferenceId(allocationPreference.getAllocationPreferenceId())
            .fundId(fund.getFundId())
            .categoryIds(List.of(categoryYFP.getCategoryId()))
            .username("developer")
            .build();

    allocation =
        Allocation.builder()
            .allocationId(AllocationId.of(UUID.randomUUID()))
            .allocationPreference(allocationPreference)
            .categories(allocationPreference.getCategories())
            .customerReference(allocationPreference.getCustomerReference())
            .deliveryInstructions(allocationPreference.getDeliveryInstructions())
            .fund(allocationPreference.getFund())
            .notes(allocationPreference.getNotes())
            .build();

    noteAdded =
        NoteAdded.builder()
            .id(UUID.randomUUID())
            .username("developer")
            .allocation(allocation)
            .build();

    categoryAssigned =
        CategoryAssigned.builder()
            .id(UUID.randomUUID())
            .username("developer")
            .allocation(allocation)
            .build();
  }

  @Test
  public void onCategoryAssigned() {
    Category categoryY = Category.builder().categoryId(new CategoryId()).code("Y").description("desc").status(CategoryStatus.ACTIVE).build();

    categoryAssigned.getAllocation().setCategories(List.of(categoryY));
    publisher.publishEvent(categoryAssigned);
    verify(allocationRepository).saveAndFlush((eq(categoryAssigned.getAllocation())));
  }

  @Test
  public void onNoteAdded() {
    noteAdded.getAllocation().setNotes("Test Notes");
    publisher.publishEvent(noteAdded);
    verify(allocationRepository).saveAndFlush((eq(noteAdded.getAllocation())));
  }

  @Test
  public void onFormatAssigned() {
    //    allocation.setFormat(Format.TRADE_PAPERBACK);
    FormatAssigned formatAssigned = FormatAssigned.from(allocation, "developer");

    publisher.publishEvent(formatAssigned);
    verify(allocationRepository).saveAndFlush(eq(formatAssigned.getAllocation()));
  }
}

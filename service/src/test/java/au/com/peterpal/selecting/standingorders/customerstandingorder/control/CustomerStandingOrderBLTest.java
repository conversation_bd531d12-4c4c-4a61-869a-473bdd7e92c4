package au.com.peterpal.selecting.standingorders.customerstandingorder.control;

import static org.assertj.core.api.Assertions.assertThat;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.audit.control.AuditDomainBL;
import au.com.peterpal.common.audit.control.AuditDomainEventListener;
import au.com.peterpal.common.audit.control.AuditDomainEventRepository;
import au.com.peterpal.common.sequencenumbergenerator.SequenceDefinitionRepository;
import au.com.peterpal.common.sequencenumbergenerator.SequenceNumberRepository;
import au.com.peterpal.common.sequencenumbergenerator.SequenceNumberService;
import au.com.peterpal.selecting.standingorders.allocation.dto.AllocationInfo;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.ext.category.control.CategoryRepository;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.branch.control.BranchRepository;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.Branch;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchStatus;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerRepository;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.ClientWebApiClient;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.ClientWebApiGateway;
import au.com.peterpal.selecting.standingorders.pendingorder.control.POCreator;
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderBL;
import au.com.peterpal.selecting.standingorders.products.control.ProductService;
import au.com.peterpal.selecting.standingorders.standingorder.control.MatchingService;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderRepository;
import au.com.peterpal.selecting.standingorders.standingorder.model.OperationType;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import au.com.peterpal.selecting.standingorders.standingorder.model.Term;
import au.com.peterpal.selecting.standingorders.standingorder.model.TermId;
import au.com.peterpal.selecting.standingorders.standingorder.model.TermType;
import au.com.peterpal.selecting.standingorders.titles.control.MatchAndMergeTitleService;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.UUID;

import au.com.peterpal.selecting.standingorders.titles.control.TitleBL;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.messaging.MessageChannel;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@DataJpaTest
@ComponentScan({
    "au.com.peterpal.selecting.standingorders.customerstandingorder.control",
    "au.com.peterpal.selecting.standingorders.ext.customer.control",
    "au.com.peterpal.selecting.standingorders.profile.control",
    "au.com.peterpal.selecting.standingorders.standingorder.control",
    "au.com.peterpal.selecting.standingorders.pendingorder.control",
    "au.com.peterpal.selecting.standingorders.ext.supplier.control",
    "au.com.peterpal.selecting.standingorders.allocation.control",
    "au.com.peterpal.selecting.standingorders.ext.fund.control",
    "au.com.peterpal.selecting.standingorders.ext.order.control"
})
public class CustomerStandingOrderBLTest {

  @Autowired
  CustomerStandingOrderBL csobl;

  @Autowired
  CategoryRepository categoryRepository;

  @MockBean
  SequenceDefinitionRepository sequenceDefinitionRepository;

  @MockBean
  TitleBL titleBL;

  @MockBean
  PendingOrderBL pendingOrderBL;

  @MockBean
  SequenceNumberRepository sequenceNumberRepository;

  @MockBean
  EventPublisher eventPublisher;

  @MockBean
  SequenceNumberService sequenceNumberService;

  @MockBean
  ClientWebApiClient clientWebApiClient;

  @MockBean
  ClientWebApiGateway clientWebApiGateway;

  @MockBean
  Map<AssignmentRule, POCreator> creatorMap;

  @MockBean
  MatchingService matchingService;

  @MockBean
  ProductService productService;

  @Autowired
  CustomerRepository customerRepo;

  @Autowired
  StandingOrderRepository standingOrderRepo;

  @Autowired
  CustomerStandingOrderRepository csoRepo;

  @Autowired
  BranchRepository branchRepo;

  @MockBean
  AuditDomainBL auditDomainBL;

  @MockBean
  AuditDomainEventRepository auditDomainEventRepository;

  @MockBean
  AuditDomainEventListener auditDomainEventListener;

  @MockBean
  MatchAndMergeTitleService matchAndMergeTitleService;

  @MockBean
  MessageChannel rematchAllocationOutboundChannel;

  private Customer customer;
  private Branch branch;
  private StandingOrder so;

  @Before
  public void setUp() {
    assertThat(csobl).isNotNull();
    assertThat(customerRepo).isNotNull();
    assertThat(standingOrderRepo).isNotNull();
    assertThat(csoRepo).isNotNull();
    assertThat(branchRepo).isNotNull();
    assertThat(categoryRepository).isNotNull();

    customer = Customer.builder()
        .customerId(CustomerId.of(UUID.randomUUID()))
        .code("BCC")
        .name("Brisbane City Libraries")
        .build();
    customer = customerRepo.save(customer);
    assertThat(customer).isNotNull();

    branch = Branch.builder()
        .branchId(BranchId.of(UUID.randomUUID()))
        .status(BranchStatus.ACTIVE)
        .code("ANN")
        .name("ANN branch")
        .customer(customer)
        .build();
    branch = branchRepo.save(branch);
    assertThat(branch).isNotNull();

    so = StandingOrder.builder()
        .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
        .standingOrderStatus(StandingOrderStatus.ACTIVE)
        .description("Test standing order")
        .notes("Some notes")
        .standingOrderNumber("SO-000001")
        .build();

    so.addTerm(Term.builder()
        .standingOrder(so)
        .operation(OperationType.CONTAINS)
        .type(TermType.TITLE)
        .termId(TermId.of(UUID.randomUUID()))
        .value("Title")
        .build());
    so = standingOrderRepo.save(so);
    assertThat(so).isNotNull();

    Category categoryAN = Category.builder().categoryId(new CategoryId()).code("AN")
        .description("desc").status(
            CategoryStatus.ACTIVE).build();
    categoryAN = categoryRepository.save(categoryAN);
    assertThat(categoryAN).isNotNull();
  }

  @Test(expected = NullPointerException.class)
  public void getBranchAllocationsNullCustomerTest() {
    csobl.getBranchAllocations(null, StandingOrderId.of(UUID.randomUUID()), "AF", "");
  }

  @Test(expected = NullPointerException.class)
  public void getBranchAllocationsNullSO() {
    csobl.getBranchAllocations(CustomerId.of(UUID.randomUUID()), null, "AF", "");
  }

  @Test(expected = NullPointerException.class)
  public void getBranchAllocationsEmptyCategory() {
    csobl.getBranchAllocations(CustomerId.of(UUID.randomUUID()),
        StandingOrderId.of(UUID.randomUUID()), null, "");
  }

  @Test(expected = NoSuchElementException.class)
  public void getBranchAllocationsNoCustomerStandingOrderTest() {
    List<AllocationInfo> al =
        csobl.getBranchAllocations(customer.getCustomerId(), so.getStandingOrderId(),
            "AF", null);
    assertThat(al).isNotNull();
  }

  @Test
  public void getBranchAllocationsNoAllocationsTest() {
    List<Category> categories = categoryRepository.findByCodeIn(List.of("AN"));
    Allocation allocation = Allocation.builder().allocationId(new AllocationId()).categories(
        categories).build();

    CustomerStandingOrder cso = CustomerStandingOrder.builder()
        .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
        .customer(customer)
        .standingOrder(so)
        .allocations(List.of(allocation))
        .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
        .build();
    cso = csoRepo.save(cso);
    assertThat(cso).isNotNull();

    List<AllocationInfo> al =
        csobl.getBranchAllocations(customer.getCustomerId(), so.getStandingOrderId(),
            categories.get(0).getCode(), null);
    assertThat(al).isNotNull();
  }

  @Test
  public void getBranchAllocationsTest() {
    CustomerStandingOrder cso = CustomerStandingOrder.builder()
        .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
        .customer(customer)
        .standingOrder(so)
        .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
        .build();

    // Customer allocation
    Allocation all = Allocation.builder()
        .allocationId(AllocationId.of(UUID.randomUUID()))
        .customerStandingOrder(cso)
        .status(AllocationStatus.ACTIVE)
        .build();
    cso.addAllocation(all);

    Category categoryAF = Category.builder().categoryId(new CategoryId()).code("AF")
        .description("desc").status(
            CategoryStatus.ACTIVE).build();
    categoryRepository.save(categoryAF);

    all = Allocation.builder()
        .allocationId(AllocationId.of(UUID.randomUUID()))
        .customerStandingOrder(cso)
        .categories(List.of(categoryAF))
        .status(AllocationStatus.ACTIVE)
        .branch(branch)
        .build();
    cso.addAllocation(all);

    cso = csoRepo.save(cso);
    assertThat(cso).isNotNull();

    List<AllocationInfo> al =
        csobl.getBranchAllocations(customer.getCustomerId(), so.getStandingOrderId(),
            categoryAF.getCode(), null);
    assertThat(al).isNotNull();
    assertThat(al.size()).isEqualTo(1);
  }
}

package au.com.peterpal.selecting.standingorders.releasepreference.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.ReleasePreferenceRepository;
import au.com.peterpal.selecting.standingorders.allocationpreference.events.ReleasePreferenceAdded;
import au.com.peterpal.selecting.standingorders.allocationpreference.events.ReleasePreferenceRemoved;
import au.com.peterpal.selecting.standingorders.allocationpreference.events.ReleasePreferenceUpdated;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.*;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.boundary.dto.FundStatus;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderBL;
import java.util.List;

import au.com.peterpal.selecting.standingorders.titles.control.MatchAndMergeTitleService;
import au.com.peterpal.selecting.standingorders.titles.control.ProductMatchingBL;
import au.com.peterpal.selecting.standingorders.titles.control.TitleBL;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(properties = {"match-service.restricted-publishers=", "title.un-defer.scheduled.cron=0 1 0 * * *"})
public class ReleasePreferenceEventTest {

  private static final String USERNAME = "userdev";

  @Rule public ExpectedException expectedException = ExpectedException.none();
  @Autowired private EventPublisher eventPublisher;
  @MockBean private ReleasePreferenceRepository releasePreferenceRepository;
  @MockBean
  PendingOrderBL pendingOrderBL;
  @MockBean
  ProductMatchingBL productMatchingBL;
  @MockBean
  MatchAndMergeTitleService matchAndMergeTitleService;
  @MockBean
  TitleBL titleBL;

  private ReleasePreference releasePreference;

  @Before
  public void setUp() {
    Customer customer = Customer.of(CustomerId.of(UUID.randomUUID()), "CUST01");

    Fund fund =
        Fund.builder()
            .fundId(FundId.of(UUID.randomUUID()))
            .code("F01")
            .customer(customer)
            .name("F01")
            .status(FundStatus.ACTIVE)
            .build();

    Fund hbFund =
        Fund.builder()
            .fundId(FundId.of(UUID.randomUUID()))
            .code("HBF02")
            .customer(customer)
            .name("HB02")
            .status(FundStatus.ACTIVE)
            .build();

    Fund pbFund =
        Fund.builder()
            .fundId(FundId.of(UUID.randomUUID()))
            .code("PBF03")
            .customer(customer)
            .name("PBF03")
            .status(FundStatus.ACTIVE)
            .build();

    Category categoryY = Category.builder().categoryId(new CategoryId()).code("Y").status(
        CategoryStatus.ACTIVE).build();

    AllocationPreference allocationPreference =
        AllocationPreference.builder()
            .allocationPreferenceId(AllocationPreferenceId.of(UUID.randomUUID()))
            .customer(customer)
            .categories(List.of(categoryY))
            .customerReference("test")
            .deliveryInstructions("delivery")
            .notes("notes")
            .fund(fund)
            .build();

    releasePreference =
        ReleasePreference.builder()
            .releasePreferenceId(ReleasePreferenceId.of(UUID.randomUUID()))
            .actionType(ActionType.ORDER)
            .initialAssignmentRule(AssignmentRule.FIRST_AVAILABLE)
            .paperbackfund(pbFund)
            .hardbackfund(hbFund)
            .releaseType(ReleaseType.INITIAL)
            .smallFormatPaperbackRule(SmallFormatPaperbackRule.FIRST)
            .allocationPreference(allocationPreference)
            .fund(fund)
            .build();
  }

  @Test
  public void onReleasePreferenceAdded() {
    ReleasePreferenceAdded releasePreferenceAdded =
        ReleasePreferenceAdded.builder()
            .id(UUID.randomUUID())
            .releasePreference(releasePreference)
            .username(USERNAME)
            .build();

    eventPublisher.publishEvent(releasePreferenceAdded);
    verify(releasePreferenceRepository).save(eq(releasePreferenceAdded.getReleasePreference()));
  }

  @Test
  public void onReleasePreferenceRemoved() {
    ReleasePreferenceRemoved releasePreferenceRemoved =
        ReleasePreferenceRemoved.builder().id(UUID.randomUUID()).username(USERNAME).build();

    eventPublisher.publishEvent(releasePreferenceRemoved);
    verify(releasePreferenceRepository)
        .deleteById(eq(ReleasePreferenceId.of(releasePreferenceRemoved.getId())));
  }

  @Test
  public void onReleasePreferenceUpdated() {
    ReleasePreferenceUpdated releasePreferenceUpdated =
        ReleasePreferenceUpdated.from(UUID.randomUUID(), releasePreference, USERNAME);

    eventPublisher.publishEvent(releasePreferenceUpdated);
    verify(releasePreferenceRepository).save(eq(releasePreferenceUpdated.getPreference()));
  }
}

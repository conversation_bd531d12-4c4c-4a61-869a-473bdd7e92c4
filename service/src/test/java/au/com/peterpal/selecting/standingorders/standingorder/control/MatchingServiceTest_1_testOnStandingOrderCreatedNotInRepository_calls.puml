@startuml

/' diagram meta data start
config=CallConfiguration;
{
  "rootMethod": "au.com.peterpal.selecting.standingorders.standingorder.control.MatchingServiceTest#testOnStandingOrderCreatedNotInRepository()",
  "projectClassification": {
    "searchMode": "OpenProject", // OpenProject, AllProjects
    "includedProjects": "",
    "pathEndKeywords": "*.impl",
    "isClientPath": "",
    "isClientName": "",
    "isTestPath": "",
    "isTestName": "",
    "isMappingPath": "",
    "isMappingName": "",
    "isDataAccessPath": "",
    "isDataAccessName": "",
    "isDataStructurePath": "",
    "isDataStructureName": "",
    "isInterfaceStructuresPath": "",
    "isInterfaceStructuresName": "",
    "isEntryPointPath": "",
    "isEntryPointName": ""
  },
  "graphRestriction": {
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "removeByInheritance": "", // inheritance/annotation based filtering is done in a second step
    "removeByAnnotation": "",
    "removeByClassPackage": "", // cleanup the graph after inheritance/annotation based filtering is done
    "removeByClassName": "",
    "cutMappings": false,
    "cutEnum": true,
    "cutTests": true,
    "cutClient": true,
    "cutDataAccess": true,
    "cutInterfaceStructures": true,
    "cutDataStructures": true,
    "cutGetterAndSetter": true,
    "cutConstructors": true
  },
  "graphTraversal": {
    "forwardDepth": 3,
    "backwardDepth": 3,
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "hideMappings": false,
    "hideDataStructures": false,
    "hidePrivateMethods": true,
    "hideInterfaceCalls": true, // indirection: implementation -> interface (is hidden) -> implementation
    "onlyShowApplicationEntryPoints": false // root node is included
  },
  "details": {
    "aggregation": "GroupByClass", // ByClass, GroupByClass, None
    "showMethodParametersTypes": false,
    "showMethodParametersNames": false,
    "showMethodReturnType": false,
    "showPackageLevels": 2,
    "showCallOrder": false,
    "edgeMode": "MethodsOnly", // TypesOnly, MethodsOnly, TypesAndMethods, MethodsAndDirectTypeUsage
    "showDetailedClassStructure": false
  },
  "rootClass": "au.com.peterpal.selecting.standingorders.standingorder.control.MatchingServiceTest"
}
diagram meta data end '/



digraph g {
    rankdir="LR"
    splines=polyline


'nodes
subgraph cluster_98689 {
   	label=com
	labeljust=l
	fillcolor="#ececec"
	style=filled

   subgraph cluster_1300071644 {
   	label=peterpal
	labeljust=l
	fillcolor="#d8d8d8"
	style=filled

   subgraph cluster_1225991170 {
   	label=MatchingService
	labeljust=l
	fillcolor=white
	style=filled

   MatchingService1335247380XXXcountObserverMap0[
	label="+ countObserverMap()"
	style=filled
	fillcolor=white
	tooltip="MatchingService

null"
	fontcolor=darkgreen
];

MatchingService1335247380XXXinit0[
	label="+ init()"
	style=filled
	fillcolor=white
	tooltip="MatchingService

null"
	fontcolor=darkgreen
];

MatchingService1335247380XXXon418050490[
	label="+ on()"
	style=filled
	fillcolor=white
	tooltip="MatchingService

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1867065251 {
   	label=Affirm
	labeljust=l
	fillcolor=white
	style=filled

   Affirm1046088404XXXnotNull1808118735[
	label="+ notNull()"
	style=filled
	fillcolor=white
	tooltip="Affirm

&#10;  Affirm that the object is not {@code null}&#10;  \<pre\>&#10;  Validator.of(value).notNull(\"The value must not be null\");&#10;  \</pre\>&#10;  @param message&#10; "
	fontcolor=darkgreen
];

Affirm1046088404XXXof1939501217[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="Affirm

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1909489870 {
   	label=StandingOrderCreated
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderCreated586215038XXXbuilder0[
	label="+ builder()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderCreated

null"
	fontcolor=darkgreen
];
}

subgraph cluster_2099930544 {
   	label=MatchingServiceTest
	labeljust=l
	fillcolor=white
	style=filled

   MatchingServiceTest1335247380XXXtestOnStandingOrderCreatedNotInRepository0[
	label="+ testOnStandingOrderCreatedNotInRepository()"
	style=filled
	fillcolor=white
	tooltip="MatchingServiceTest

null"
	penwidth=4
	fontcolor=darkgreen
];
}
}
}

'edges
MatchingService1335247380XXXon418050490 -> Affirm1046088404XXXnotNull1808118735;
MatchingService1335247380XXXon418050490 -> Affirm1046088404XXXof1939501217;
MatchingServiceTest1335247380XXXtestOnStandingOrderCreatedNotInRepository0 -> MatchingService1335247380XXXcountObserverMap0;
MatchingServiceTest1335247380XXXtestOnStandingOrderCreatedNotInRepository0 -> MatchingService1335247380XXXinit0;
MatchingServiceTest1335247380XXXtestOnStandingOrderCreatedNotInRepository0 -> MatchingService1335247380XXXon418050490;
MatchingServiceTest1335247380XXXtestOnStandingOrderCreatedNotInRepository0 -> StandingOrderCreated586215038XXXbuilder0;

}
@enduml

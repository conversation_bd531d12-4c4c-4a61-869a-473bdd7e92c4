package au.com.peterpal.selecting.standingorders.standingorder.boundary;

import au.com.peterpal.common.security.config.CustomKeycloakSpringBootConfigResolver;
import au.com.peterpal.selecting.standingorders.SpringSecurityTestConfig;
import au.com.peterpal.selecting.standingorders.config.SecurityConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.keycloak.adapters.springboot.KeycloakSpringBootProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.web.config.EnableSpringDataWebSupport;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@EnableSpringDataWebSupport
@WebMvcTest(StandingOrderController.class)
@ContextConfiguration(classes = StandingOrderController.class)
@Import({
  SecurityConfig.class,
  SpringSecurityTestConfig.class,
  CustomKeycloakSpringBootConfigResolver.class,
  KeycloakSpringBootProperties.class
})
public class AuthenticationTest {

  private static final String BASE_PATH = "/api/standing-orders/";

  @Autowired
  private MockMvc mvc;

  @MockBean private StandingOrderController standingOrderController;

  @Test
  public void unauthorizedAccess() throws Exception {
    setColumnConfiguration().andExpect(status().isUnauthorized());
  }

  @Test
  @WithUserDetails("other")
  public void forbiddenAccess() throws Exception {
    setColumnConfiguration().andExpect(status().isForbidden());
  }

  private ResultActions setColumnConfiguration() throws Exception {
    return mvc.perform(
        get(BASE_PATH + "search")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON));
  }
}

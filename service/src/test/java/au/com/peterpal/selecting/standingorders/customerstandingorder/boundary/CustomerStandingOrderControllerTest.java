package au.com.peterpal.selecting.standingorders.customerstandingorder.boundary;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.common.security.config.CustomKeycloakSpringBootConfigResolver;
import au.com.peterpal.selecting.standingorders.SpringSecurityTestConfig;
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationBL;
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationRepository;
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationService;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.ReleasePreferenceRepository;
import au.com.peterpal.selecting.standingorders.config.SecurityConfig;
import au.com.peterpal.selecting.standingorders.customerstandingorder.control.CustomerStandingOrderBL;
import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.*;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.Format;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerRepository;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.itemtype.control.ItemTypeRepository;
import au.com.peterpal.selecting.standingorders.ext.order.control.OrderRepository;
import au.com.peterpal.selecting.standingorders.ext.supplier.control.SupplierRepository;
import au.com.peterpal.selecting.standingorders.profile.control.CustomerProfileService;
//import au.com.peterpal.selecting.standingorders.standingorder.control.TitleRepository;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.keycloak.adapters.springboot.KeycloakSpringBootProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(CustomerStandingOrderController.class)
@ContextConfiguration(classes = CustomerStandingOrderController.class)
@WithUserDetails("dev")
@Import({
  SecurityConfig.class,
  SpringSecurityTestConfig.class,
  CustomKeycloakSpringBootConfigResolver.class,
  KeycloakSpringBootProperties.class
})
public class CustomerStandingOrderControllerTest {

  private static final String BASE_PATH = "/api/customer-standing-orders";
  private static final String HEADER_USERNAME = "username";
  private static final String USERNAME = "userdev";

  private ObjectMapper mapper;

  @Autowired private MockMvc mvc;

  @MockBean private EventPublisher publisher;

  @MockBean private AllocationRepository allocationRepository;

  @MockBean private CustomerStandingOrderBL customerStandingOrderBL;
  @MockBean private AllocationBL allocationBL;

  @MockBean private CustomerProfileService customerProfileService;
  @MockBean private AllocationService allocationService;
  @MockBean private OrderRepository orderRepository;
  @MockBean private ItemTypeRepository itemTypeRepository;
  @MockBean private SupplierRepository supplierRepository;
//  @MockBean private TitleRepository titleRepository;
  @MockBean private ReleasePreferenceRepository releasePreferenceRepository;
  @MockBean private CustomerRepository customerRepository;

  @Before
  public void setUp() {
    mapper = new ObjectMapper();
  }

  @Test
  public void createAllocation() throws Exception {
    AllocationRequest ar = AllocationRequest.builder()
        .customerId(CustomerId.of(UUID.randomUUID()))
        .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
        .build();

    mvc.perform(
      MockMvcRequestBuilders.post(BASE_PATH + "/allocations")
          .header(HEADER_USERNAME, USERNAME)
          .content(mapper.writeValueAsString(ar))
          .contentType(MediaType.APPLICATION_JSON)
          .accept(MediaType.APPLICATION_JSON))
    .andExpect(status().isOk());
  }

  @Test
  public void createCustomerStandingOrders() throws Exception {

    CreateCustomerStandingOrder request =
        CreateCustomerStandingOrder.builder()
            .customerId(UUID.fromString("8abe1236-a7c0-41df-a85e-1e1e90a69c1d"))
            .standingOrderId(UUID.fromString("8abe1236-a7c0-41df-a85e-1e1e90a69c1d"))
            .build();

    mvc.perform(
            MockMvcRequestBuilders.post(BASE_PATH)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isOk());
  }

  @Test
  public void createCustomerStandingOrdersNoCustomer() throws Exception {

    CreateCustomerStandingOrder request =
        CreateCustomerStandingOrder.builder()
            .standingOrderId(UUID.fromString("8abe1236-a7c0-41df-a85e-1e1e90a69c1d"))
            .build();

    mvc.perform(
            MockMvcRequestBuilders.post(BASE_PATH)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void createCustomerStandingOrdersNoStandingOrderId() throws Exception {

    CreateCustomerStandingOrder request =
        CreateCustomerStandingOrder.builder()
            .customerId(UUID.fromString("8abe1236-a7c0-41df-a85e-1e1e90a69c1d"))
            .build();

    mvc.perform(
            MockMvcRequestBuilders.post(BASE_PATH)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void createCustomerStandingOrdersNoParams() throws Exception {

    CreateCustomerStandingOrder request = CreateCustomerStandingOrder.builder().build();

    mvc.perform(
            MockMvcRequestBuilders.post(BASE_PATH)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void getCustomerStandingOrders() throws Exception {

    UUID customerStandingOrderId = UUID.randomUUID();

    mvc.perform(
            get(BASE_PATH + "/{customerStandingOrderId}", customerStandingOrderId)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  public void updateCustomerStandingOrders() throws Exception {
    CustomerStandingOrderId id = CustomerStandingOrderId.of(UUID.randomUUID());
    CustomerStandingOrderRequest request =
        CustomerStandingOrderRequest.builder()
            .customerId(UUID.fromString("8abe1236-a7c0-41df-a85e-1e1e90a69c1d"))
            .standingOrderId(UUID.fromString("8abe1236-a7c0-41df-a85e-1e1e90a69c1d"))
            .build();

    mvc.perform(
            MockMvcRequestBuilders.put(String.format(BASE_PATH + "/%s", id))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isOk());
  }

  @Test
  public void updateCustomerStandingOrdersCustomerStandingOrderNotExist() throws Exception {
    CustomerStandingOrderId id = CustomerStandingOrderId.of(UUID.randomUUID());
    CustomerStandingOrderRequest request =
        CustomerStandingOrderRequest.builder()
            .customerId(UUID.fromString("8abe1236-a7c0-41df-a85e-1e1e90a69c1d"))
            .standingOrderId(UUID.fromString("8abe1236-a7c0-41df-a85e-1e1e90a69c1d"))
            .build();

    when(customerStandingOrderBL.handle(eq(request), eq(id), eq(USERNAME)))
        .thenThrow(new ResourceNotFoundException(CustomerStandingOrder.class, String.valueOf(id)));

    mvc.perform(
            MockMvcRequestBuilders.put(String.format(BASE_PATH + "/%s", id))
                .header(HEADER_USERNAME, USERNAME)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isNotFound());
  }

  @Test
  public void updateCustomerStandingOrdersNoCustomer() throws Exception {
    CustomerStandingOrderId id = CustomerStandingOrderId.of(UUID.randomUUID());
    CustomerStandingOrderRequest request =
        CustomerStandingOrderRequest.builder()
            .standingOrderId(UUID.fromString("8abe1236-a7c0-41df-a85e-1e1e90a69c1d"))
            .build();

    mvc.perform(
            MockMvcRequestBuilders.put(String.format(BASE_PATH + "/%s", id))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void updateCustomerStandingOrdersNoStandingOrderId() throws Exception {
    CustomerStandingOrderId id = CustomerStandingOrderId.of(UUID.randomUUID());
    CustomerStandingOrderRequest request =
        CustomerStandingOrderRequest.builder()
            .customerId(UUID.fromString("8abe1236-a7c0-41df-a85e-1e1e90a69c1d"))
            .build();

    mvc.perform(
            MockMvcRequestBuilders.put(String.format(BASE_PATH + "/%s", id))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void updateCustomerStandingOrdersNoParams() throws Exception {
    CustomerStandingOrderId id = CustomerStandingOrderId.of(UUID.randomUUID());
    CustomerStandingOrderRequest request = CustomerStandingOrderRequest.builder().build();

    mvc.perform(
            MockMvcRequestBuilders.put(String.format(BASE_PATH + "/%s", id))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void updatedAllocation() throws Exception {
    AllocationId allocationId = AllocationId.of(UUID.randomUUID());

    UpdateAllocationInfo request =
        UpdateAllocationInfo.builder()
            .allocationId(allocationId.getId())
            .customerReference("Test Customere reference")
            .deliveryInstructions("delivery instruction")
            .notes("notes")
            .allocationPreferenceId(UUID.randomUUID())
            .fundId(UUID.randomUUID())
            .customerStandingOrderId(UUID.randomUUID())
            .categories(List.of("Y"))
            .status(AllocationStatus.ACTIVE)
            .build();

    String url = BASE_PATH + "/allocations/%s";

    mvc.perform(
            MockMvcRequestBuilders.put(String.format(url, allocationId))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isOk());
  }

  @Test
  public void updatedAllocationAllocationNotExist() throws Exception {
    AllocationId allocationId = AllocationId.of(UUID.randomUUID());

    UpdateAllocationInfo request =
        UpdateAllocationInfo.builder()
            .allocationId(allocationId.getId())
            .customerReference("Test Customere reference")
            .deliveryInstructions("delivery instruction")
            .notes("notes")
            .allocationPreferenceId(UUID.randomUUID())
            .fundId(UUID.randomUUID())
            .customerStandingOrderId(UUID.randomUUID())
            .categories(List.of("Y"))
            .status(AllocationStatus.ACTIVE)
            .build();

    String url = BASE_PATH + "/allocations/%s";

    when(customerStandingOrderBL.handle(eq(request), eq(allocationId), eq(USERNAME)))
        .thenThrow(new ResourceNotFoundException(Allocation.class, String.valueOf(allocationId)));

    mvc.perform(
            MockMvcRequestBuilders.put(String.format(url, allocationId))
                .contentType(MediaType.APPLICATION_JSON)
                .header(HEADER_USERNAME, USERNAME)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isNotFound());
  }

  @Test
  public void allocationAddNote() throws Exception {

    String allocationId = UUID.randomUUID().toString();
    String note = "Test Notes";

    mvc.perform(
            MockMvcRequestBuilders.put(BASE_PATH + "/allocations/" + allocationId + "/notes")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(note)))
        .andExpect(status().isOk());
  }

  @Test
  public void allocationAddNoteNoAllocationId() throws Exception {
    mvc.perform(
            MockMvcRequestBuilders.put(BASE_PATH + "/allocations/notes")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString("")))
        .andExpect(status().is4xxClientError());
  }

  @Test
  public void allocationAddNoteNoNotes() throws Exception {

    String allocationId = UUID.randomUUID().toString();

    mvc.perform(
            MockMvcRequestBuilders.put(BASE_PATH + "/allocations/" + allocationId + "/notes")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void allocationsAssignFormat() throws Exception {

    String allocationId = UUID.randomUUID().toString();
    AssignFormat request = AssignFormat.builder().format(Format.HARDBACK).build();

    mvc.perform(
            MockMvcRequestBuilders.put(BASE_PATH + "/allocations/" + allocationId + "/assignFormat")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().isOk());
  }

  @Test
  public void allocationsAssignFormatNoAllocationId() throws Exception {

    AssignFormat request = AssignFormat.builder().format(Format.HARDBACK).build();

    mvc.perform(
            MockMvcRequestBuilders.put(BASE_PATH + "/allocations/assignFormat")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().is4xxClientError());
  }

  @Test
  public void allocationsAssignFormatNoFormat() throws Exception {

    String allocationId = UUID.randomUUID().toString();

    mvc.perform(
            MockMvcRequestBuilders.put(BASE_PATH + "/allocations/" + allocationId + "/assignFormat")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void allocationsAssignCategory() throws Exception {
    String allocationId = UUID.randomUUID().toString();
    mvc.perform(
            MockMvcRequestBuilders.put(BASE_PATH + "/allocations/" + allocationId + "/categories")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(
                    mapper.writeValueAsString(
                        AssignCategory.builder().categories(List.of("Y")).build())))
        .andExpect(status().isOk());
  }

  @Test
  public void allocationsAssignCategoryNoAllocationId() throws Exception {

    AssignCategory request = AssignCategory.builder().categories(List.of("Y")).build();

    mvc.perform(
            MockMvcRequestBuilders.put(BASE_PATH + "/allocations/categories")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().is4xxClientError());
  }

  @Test
  public void allocationsAssignCategoryNoCategory() throws Exception {

    String allocationId = UUID.randomUUID().toString();

    mvc.perform(
            MockMvcRequestBuilders.put(BASE_PATH + "/allocations/" + allocationId + "/categories")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void allocationsAssignFormatNoParams() throws Exception {

    AssignCategory request = AssignCategory.builder().build();

    mvc.perform(
            MockMvcRequestBuilders.put(BASE_PATH + "/allocations/categories")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(mapper.writeValueAsString(request)))
        .andExpect(status().is4xxClientError());
  }

  @Test
  public void deleteAllocation() throws Exception {
    AllocationId allocationId = AllocationId.of(UUID.randomUUID());

    when(allocationBL.handle(eq(allocationId), eq(USERNAME))).thenReturn(allocationId);

    mvc.perform(
            MockMvcRequestBuilders.delete(BASE_PATH + "/allocations/{allocationId}", allocationId)
                .header(HEADER_USERNAME, USERNAME))
        .andExpect(status().isOk())
        .andExpect(content().json(mapper.writeValueAsString(allocationId)));
  }
}


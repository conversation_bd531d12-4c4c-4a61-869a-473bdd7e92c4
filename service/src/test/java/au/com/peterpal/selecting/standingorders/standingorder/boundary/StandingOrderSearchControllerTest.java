package au.com.peterpal.selecting.standingorders.standingorder.boundary;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.common.security.config.CustomKeycloakSpringBootConfigResolver;
import au.com.peterpal.selecting.standingorders.SpringSecurityTestConfig;
import au.com.peterpal.selecting.standingorders.allocation.commands.SearchAllocationsByStandingOrder;
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationBL;
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationRepository;
import au.com.peterpal.selecting.standingorders.allocation.dto.AllocationInfo;
import au.com.peterpal.selecting.standingorders.allocation.dto.AllocationSearchByStandingOrderResponse;
import au.com.peterpal.selecting.standingorders.allocation.dto.ReleaseInfo;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.SmallFormatPaperbackRule;
import au.com.peterpal.selecting.standingorders.catalog.model.LanguageCode;
import au.com.peterpal.selecting.standingorders.catalog.model.TitleTypeCode;
import au.com.peterpal.selecting.standingorders.ext.category.control.CategoryRepository;
import au.com.peterpal.selecting.standingorders.config.SecurityConfig;
import au.com.peterpal.selecting.standingorders.customerstandingorder.control.CustomerStandingOrderBL;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.customer.dto.CustomerInfo;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.ClientWebApiGateway;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.CwProductInfo;
import au.com.peterpal.selecting.standingorders.products.control.ProductRepository;
import au.com.peterpal.selecting.standingorders.standingorder.control.*;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.titles.control.MatchAndMergeTitleService;
import au.com.peterpal.selecting.standingorders.titles.control.ProductMatchingBL;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.keycloak.adapters.springboot.KeycloakSpringBootProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.config.EnableSpringDataWebSupport;
import org.springframework.http.MediaType;
import org.springframework.messaging.MessageChannel;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(StandingOrderController.class)
@EnableSpringDataWebSupport
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = StandingOrderController.class)
@WithUserDetails("dev")
@Import({
  SecurityConfig.class,
  SpringSecurityTestConfig.class,
  CustomKeycloakSpringBootConfigResolver.class,
  KeycloakSpringBootProperties.class
})
public class StandingOrderSearchControllerTest {

  private static final String BASE_URL = "/api/standing-orders/";
  private static final String PRODUCTS_FIND_BY_ISBN = "/products/find-by-isbn";

  @Rule public ExpectedException rule = ExpectedException.none();

  @Autowired private MockMvc mockMvc;

  @MockBean public AllocationRepository allocationRepository;
  @MockBean public StandingOrderRepository standingOrderRepository;
  @MockBean public AllocationBL allocationBL;
  @MockBean public StandingOrderBL standingOrderBL;
  @MockBean(name ="productChannel") public MessageChannel productChannel;
  @MockBean public CategoryService catService;
  @MockBean public CategoryRepository categoryRepository;
  @MockBean public ClientWebApiGateway clientWeb;
  @MockBean public CustomerStandingOrderBL customerStandingOrderBL;
  @MockBean public ProductSearchService productSearchService;
  @MockBean public RejectionReasonTypeService rejectionReasonTypeService;
  @MockBean public ProductMatchingBL productMatchingBL;
  @MockBean public ProductRepository productRepository;
  @MockBean public MatchAndMergeTitleService matchAndMergeTitleService;
  @MockBean(name ="rematchStandingOrderOutboundChannel") public MessageChannel rematchStandingOrderOutboundChannel;

  private List<AllocationSearchByStandingOrderResponse> searchAllocationByStandingOrderResponse;
  private List<AllocationInfo> allocationInfoList;

  @Before
  public void init() {
    searchAllocationByStandingOrderResponse =
        List.of(
            AllocationSearchByStandingOrderResponse.builder()
                .actionType(ActionType.ORDER)
                .allocationId(AllocationId.of(UUID.randomUUID()))
                .releaseId(UUID.randomUUID())
                .assignmentRule(AssignmentRule.FIRST_AVAILABLE)
                .customerCode("CUSTCODE01")
                .customerReference("CUSTREF01")
                .deliveryInstructions("deliverInstructions")
                .fundCode("FCODE01")
                .fundName("FUND")
                .fundQuantity(1)
                .hbFundCode("FCODE01")
                .hbFundName("FUND")
                .hbQuantity(1)
                .pbFundCode("FCODE01")
                .pbFundName("FUND")
                .pbQuantity(1)
                .notes("Note1")
                .releaseType(ReleaseType.INITIAL)
                .smallFormatPaperbackRule(SmallFormatPaperbackRule.FIRST)
                .status(AllocationStatus.ACTIVE)
                .build());
    AllocationId allocId = AllocationId.of(UUID.randomUUID());

    allocationInfoList = List.of(AllocationInfo.builder()
            .allocationId(allocId)
            .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
            .customer(CustomerInfo.of(CustomerId.of(UUID.randomUUID()), "CODE", "Customer"))
            .status(AllocationStatus.ACTIVE)
            .categories(List.of("AF"))
            .customerReference(null)
            .deliveryInstructions("No instructions")
            .notes("This is a test")
            .releases(Arrays.asList(ReleaseInfo.builder()
                  .releaseId(ReleaseId.of(UUID.randomUUID()))
                  .allocationId(allocId)
                  .releaseType(ReleaseType.INITIAL)
                  .actionType(ActionType.ORDER)
                  .build())
            )
        .build());
  }

  @Test
  public void searchAllocationByStandingOrderRequiredOnly() throws Exception {
    when(allocationBL.handle(any(SearchAllocationsByStandingOrder.class)))
        .thenReturn(new PageImpl<>(allocationInfoList));

    mockMvc
        .perform(get(String.format(BASE_URL + "%s/allocations/search", UUID.randomUUID())))
        .andDo(print())
        .andExpect(status().isOk())
        .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON));
  }

  @Test
  public void searchAllocationByStandingOrderWithParams() throws Exception {
    String url = BASE_URL + "%s/allocations/search?customerId=%s&status=%s&releaseType=%s";
    when(allocationBL.handle(any(SearchAllocationsByStandingOrder.class)))
        .thenReturn(new PageImpl<>(allocationInfoList));

    mockMvc
        .perform(get(String.format(url, UUID.randomUUID(), UUID.randomUUID(), "ACTIVE", "INITIAL")))
        .andDo(print())
        .andExpect(status().isOk())
        .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON));
  }

  @Test
  public void searchAllocationByStandingOrderNoExist() throws Exception {
    SearchAllocationsByStandingOrder request =
        SearchAllocationsByStandingOrder.builder()
            .standingOrderId(StandingOrderId.of(UUID.randomUUID()))
            .pageRequest(
                PageRequest.of(0, 10, Sort.Direction.ASC, "allocation.customerStandingOrder.customer.code"))
            .build();

    when(allocationBL.handle(request))
        .thenThrow(
            new ResourceNotFoundException(
                StandingOrder.class, String.valueOf(request.getStandingOrderId())));

    mockMvc
        .perform(
            get(String.format(BASE_URL + "%s/allocations/search", request.getStandingOrderId())))
        .andDo(print())
        .andExpect(status().is4xxClientError());
  }

  @Test
  public void searchStandingWithParams() throws Exception {

    String customerId = UUID.randomUUID().toString();
    String status = "ACTIVE";
    String standingOrderNumber = "STO-00000001";
    String description = "test";

    mockMvc
        .perform(
            get(BASE_URL + "search")
                .param("customerId", customerId)
                .param("status", status)
                .param("standingOrderNumber", standingOrderNumber)
                .param("description", description)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andDo(print())
        .andExpect(status().isOk());
  }

  @Test
  public void searchStandingWithParamsAndPagingAndSorting() throws Exception {

    String customerId = UUID.randomUUID().toString();
    String status = "ACTIVE";
    String standingOrderNumber = "STO-00000001";
    String description = "test";
    PageRequest pageRequest = PageRequest.of(1, 10, Sort.Direction.ASC, "standingOrderNumber");

    mockMvc
        .perform(
            get(BASE_URL + "search")
                .param("customerId", customerId)
                .param("status", status)
                .param("standingOrderNumber", standingOrderNumber)
                .param("description", description)
                .param("page", "1")
                .param("size", "10")
                .param("sort", "standingOrderNumber,asc")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  public void searchDeliveryWithoutParams() throws Exception {

    mockMvc
        .perform(
            get(BASE_URL + "search")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  public void searchStandingOrderByStatus() throws Exception {

    String status = "ACTIVE";

    mockMvc
        .perform(
            get(BASE_URL + "search")
                .param("status", status)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  public void searchStandingOrderByInvalidStatus() throws Exception {

    String status = "NEWS";

    mockMvc
        .perform(
            get(BASE_URL + "search")
                .param("status", status)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void searchStandingOrderByCustomerId() throws Exception {
    String customerId = UUID.randomUUID().toString();

    mockMvc
        .perform(
            get(BASE_URL + "search")
                .param("customerId", customerId)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  public void searchStandingOrderByInvalidCustomerId() throws Exception {
    String customerId = "INVALID_UUID_FORMAT";

    rule.expectMessage(String.format("Invalid UUID string: %s", customerId));

    mockMvc.perform(
        get(BASE_URL + "search")
            .param("customerId", customerId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON));
  }

  @Test
  public void findByIsbnShouldReturnProductInfo() throws Exception {
    ObjectMapper mapper = new ObjectMapper();
    String isbn = "9780997298741";
    CwProductInfo productInfo =
        CwProductInfo.builder()
            .id(1)
            .title(
                CwProductInfo.TitleInfo.builder()
                    .type(TitleTypeCode.TITLE)
                    .text("The Observer Effect")
                    .prefix("The")
                    .withoutPrefix("Observer Effect")
                    .subtitle("Poems")
                    .build())
            .productReference(isbn)
            .languageCode(LanguageCode.ENG)
            .build();

    given(clientWeb.searchProductByIsbn(isbn)).willReturn(productInfo);

    mockMvc
        .perform(
            get(BASE_URL + PRODUCTS_FIND_BY_ISBN)
                .param("isbn", isbn)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().json(mapper.writeValueAsString(productInfo)));
  }

  @Test
  public void findByIsbnWithoutResultShouldThrowException() throws Exception {
    given(clientWeb.searchProductByIsbn(anyString())).willThrow(ResourceNotFoundException.class);

    mockMvc
        .perform(
            get(BASE_URL + PRODUCTS_FIND_BY_ISBN)
                .param("isbn", "isbn")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound());
  }
}

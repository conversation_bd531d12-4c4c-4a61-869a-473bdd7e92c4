@startuml

/' diagram meta data start
config=CallConfiguration;
{
  "rootMethod": "au.com.peterpal.selecting.standingorders.standingorder.control.MatchingServiceTest#testOnMatchFound()",
  "projectClassification": {
    "searchMode": "OpenProject", // OpenProject, AllProjects
    "includedProjects": "",
    "pathEndKeywords": "*.impl",
    "isClientPath": "",
    "isClientName": "",
    "isTestPath": "",
    "isTestName": "",
    "isMappingPath": "",
    "isMappingName": "",
    "isDataAccessPath": "",
    "isDataAccessName": "",
    "isDataStructurePath": "",
    "isDataStructureName": "",
    "isInterfaceStructuresPath": "",
    "isInterfaceStructuresName": "",
    "isEntryPointPath": "",
    "isEntryPointName": ""
  },
  "graphRestriction": {
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "removeByInheritance": "", // inheritance/annotation based filtering is done in a second step
    "removeByAnnotation": "",
    "removeByClassPackage": "", // cleanup the graph after inheritance/annotation based filtering is done
    "removeByClassName": "",
    "cutMappings": false,
    "cutEnum": true,
    "cutTests": true,
    "cutClient": true,
    "cutDataAccess": true,
    "cutInterfaceStructures": true,
    "cutDataStructures": true,
    "cutGetterAndSetter": true,
    "cutConstructors": true
  },
  "graphTraversal": {
    "forwardDepth": 3,
    "backwardDepth": 3,
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "hideMappings": false,
    "hideDataStructures": false,
    "hidePrivateMethods": true,
    "hideInterfaceCalls": true, // indirection: implementation -> interface (is hidden) -> implementation
    "onlyShowApplicationEntryPoints": false // root node is included
  },
  "details": {
    "aggregation": "GroupByClass", // ByClass, GroupByClass, None
    "showMethodParametersTypes": false,
    "showMethodParametersNames": false,
    "showMethodReturnType": false,
    "showPackageLevels": 2,
    "showCallOrder": false,
    "edgeMode": "MethodsOnly", // TypesOnly, MethodsOnly, TypesAndMethods, MethodsAndDirectTypeUsage
    "showDetailedClassStructure": false
  },
  "rootClass": "au.com.peterpal.selecting.standingorders.standingorder.control.MatchingServiceTest"
}
diagram meta data end '/



digraph g {
    rankdir="LR"
    splines=polyline


'nodes
subgraph cluster_98689 {
   	label=com
	labeljust=l
	fillcolor="#ececec"
	style=filled

   subgraph cluster_1300071644 {
   	label=peterpal
	labeljust=l
	fillcolor="#d8d8d8"
	style=filled

   subgraph cluster_1092055162 {
   	label=CategoryInfo
	labeljust=l
	fillcolor=white
	style=filled

   CategoryInfo843015776XXXof1820369945[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="CategoryInfo

null"
	fontcolor=darkgreen
];
}

subgraph cluster_122548397 {
   	label=CustomerStandingOrderId
	labeljust=l
	fillcolor=white
	style=filled

   CustomerStandingOrderId1486942718XXXof2616251[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="CustomerStandingOrderId

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1225991170 {
   	label=MatchingService
	labeljust=l
	fillcolor=white
	style=filled

   MatchingService1335247380XXXon459473699[
	label="+ on()"
	style=filled
	fillcolor=white
	tooltip="MatchingService

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1427499709 {
   	label=Customer
	labeljust=l
	fillcolor=white
	style=filled

   Customer402580727XXXof821590178[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="Customer

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1484343261 {
   	label=StandingOrderRepositoryCustomImpl
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderRepositoryCustomImpl1335247380XXXfindCustomers542694141[
	label="+ findCustomers()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderRepositoryCustomImpl

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1587582757 {
   	label=TitleId
	labeljust=l
	fillcolor=white
	style=filled

   TitleId843015776XXXof2616251[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="TitleId

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1711118752 {
   	label=SubjectMatcher
	labeljust=l
	fillcolor=white
	style=filled

   SubjectMatcher1335247380XXXmatch1808118735[
	label="+ match()"
	style=filled
	fillcolor=white
	tooltip="SubjectMatcher

&#10;  Find a category and priority for given subject string.&#10;  \<p\>&#10;  Get the first character of the subject.&#10;  Search for a mapping in the map.&#10;  If mapping is found and there is one mapping then return a category info&#10;  If more than one mapping found: find first mapping that subject length is \>=&#10; &#10;  @param subject the subject string&#10;  @return a category and priority, or null if map is empty or subject is null or empty.&#10; "
	fontcolor=darkgreen
];

SubjectMatcher1335247380XXXof937476012[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="SubjectMatcher

&#10;  Create a subject matcher given a list of category mappings.&#10;  \<p\>&#10;  For each subject in the category mapping list get the first character. Find the list of&#10;  category mappings corresponding to the character in the map or create a new empty list. Add&#10;  the category mapping to the list.&#10; &#10;  @param mapping list of category mappings&#10;  @return a new subject matcher&#10; "
	fontcolor=darkgreen
];
}

subgraph cluster_1732650562 {
   	label=CustomerId
	labeljust=l
	fillcolor=white
	style=filled

   CustomerId402580727XXXof2616251[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="CustomerId

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1795977842 {
   	label=OrderRepositoryCustomImpl
	labeljust=l
	fillcolor=white
	style=filled

   OrderRepositoryCustomImpl175987885XXXfindOrders788304350[
	label="+ findOrders()"
	style=filled
	fillcolor=white
	tooltip="OrderRepositoryCustomImpl

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1859343336 {
   	label=CustomerStandingOrder
	labeljust=l
	fillcolor=white
	style=filled

   CustomerStandingOrder1486942718XXXaddAllocation81014016[
	label="+ addAllocation()"
	style=filled
	fillcolor=white
	tooltip="CustomerStandingOrder

null"
	fontcolor=darkgreen
];

CustomerStandingOrder1486942718XXXbuilder0[
	label="+ builder()"
	style=filled
	fillcolor=white
	tooltip="CustomerStandingOrder

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1867065251 {
   	label=Affirm
	labeljust=l
	fillcolor=white
	style=filled

   Affirm1046088404XXXnotNull1808118735[
	label="+ notNull()"
	style=filled
	fillcolor=white
	tooltip="Affirm

&#10;  Affirm that the object is not {@code null}&#10;  \<pre\>&#10;  Validator.of(value).notNull(\"The value must not be null\");&#10;  \</pre\>&#10;  @param message&#10; "
	fontcolor=darkgreen
];

Affirm1046088404XXXof1939501217[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="Affirm

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1867661726 {
   	label=OrderService
	labeljust=l
	fillcolor=white
	style=filled

   OrderService175987885XXXhasReissue754683741[
	label="+ hasReissue()"
	style=filled
	fillcolor=white
	tooltip="OrderService

null"
	fontcolor=darkgreen
];
}

subgraph cluster_2053046378 {
   	label=Title
	labeljust=l
	fillcolor=white
	style=filled

   Title843015776XXXbuilder0[
	label="+ builder()"
	style=filled
	fillcolor=white
	tooltip="Title

null"
	fontcolor=darkgreen
];
}

subgraph cluster_2099930544 {
   	label=MatchingServiceTest
	labeljust=l
	fillcolor=white
	style=filled

   MatchingServiceTest1335247380XXXtestOnMatchFound0[
	label="+ testOnMatchFound()"
	style=filled
	fillcolor=white
	tooltip="MatchingServiceTest

null"
	penwidth=4
	fontcolor=darkgreen
];
}

subgraph cluster_2144876010 {
   	label=Allocation
	labeljust=l
	fillcolor=white
	style=filled

   Allocation50557592XXXbuilder0[
	label="+ builder()"
	style=filled
	fillcolor=white
	tooltip="Allocation

null"
	fontcolor=darkgreen
];
}

subgraph cluster_225928393 {
   	label=MatchFound
	labeljust=l
	fillcolor=white
	style=filled

   MatchFound586215038XXXbuilder0[
	label="+ builder()"
	style=filled
	fillcolor=white
	tooltip="MatchFound

null"
	fontcolor=darkgreen
];
}

subgraph cluster_358458833 {
   	label=AllocationId
	labeljust=l
	fillcolor=white
	style=filled

   AllocationId50557592XXXof2616251[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="AllocationId

null"
	fontcolor=darkgreen
];
}

subgraph cluster_43033608 {
   	label=CategoryAssigned
	labeljust=l
	fillcolor=white
	style=filled

   CategoryAssigned586215038XXXbuilder0[
	label="+ builder()"
	style=filled
	fillcolor=white
	tooltip="CategoryAssigned

null"
	fontcolor=darkgreen
];

CategoryAssigned586215038XXXof2013066710[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="CategoryAssigned

null"
	fontcolor=darkgreen
];
}

subgraph cluster_433581393 {
   	label=StandingOrderId
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderId843015776XXXof2616251[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderId

null"
	fontcolor=darkgreen
];
}

subgraph cluster_468234162 {
   	label=StringAffirm
	labeljust=l
	fillcolor=white
	style=filled

   StringAffirm1046088404XXXof1808118735[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="StringAffirm

null"
	fontcolor=darkgreen
];
}

subgraph cluster_943466892 {
   	label=StandingOrder
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrder843015776XXXbuilder0[
	label="+ builder()"
	style=filled
	fillcolor=white
	tooltip="StandingOrder

null"
	fontcolor=darkgreen
];
}
}
}

'edges
CategoryAssigned586215038XXXof2013066710 -> CategoryAssigned586215038XXXbuilder0;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> Allocation50557592XXXbuilder0;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> AllocationId50557592XXXof2616251;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> CategoryAssigned586215038XXXof2013066710;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> Customer402580727XXXof821590178;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> CustomerId402580727XXXof2616251;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> CustomerStandingOrder1486942718XXXaddAllocation81014016;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> CustomerStandingOrder1486942718XXXbuilder0;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> CustomerStandingOrderId1486942718XXXof2616251;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> MatchFound586215038XXXbuilder0;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> MatchingService1335247380XXXon459473699;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> OrderService175987885XXXhasReissue754683741;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> StandingOrder843015776XXXbuilder0;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> StandingOrderId843015776XXXof2616251;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> StandingOrderRepositoryCustomImpl1335247380XXXfindCustomers542694141;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> SubjectMatcher1335247380XXXmatch1808118735;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> SubjectMatcher1335247380XXXof937476012;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> Title843015776XXXbuilder0;
MatchingServiceTest1335247380XXXtestOnMatchFound0 -> TitleId843015776XXXof2616251;
OrderService175987885XXXhasReissue754683741 -> OrderRepositoryCustomImpl175987885XXXfindOrders788304350;
StandingOrderRepositoryCustomImpl1335247380XXXfindCustomers542694141 -> Affirm1046088404XXXnotNull1808118735;
StandingOrderRepositoryCustomImpl1335247380XXXfindCustomers542694141 -> Affirm1046088404XXXof1939501217;
SubjectMatcher1335247380XXXmatch1808118735 -> CategoryInfo843015776XXXof1820369945;
SubjectMatcher1335247380XXXmatch1808118735 -> StringAffirm1046088404XXXof1808118735;

}
@enduml

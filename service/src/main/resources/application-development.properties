logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
server.port=31170

spring.artemis.mode=native
spring.artemis.host=localhost
spring.artemis.port=61616
spring.artemis.user=artemis
spring.artemis.password=simetraehcapa

spring.datasource.url=jdbc:h2:mem:testdb;IGNORECASE=TRUE;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.flyway.enabled=false
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

lucy-api-service.url=http://internal-services-test:8080/lucy/api
catalogue-service.url=http://catalogue.cw-uat.peterpal.local:8280/cat

logging.level.com.querydsl.sql=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true


server.servlet.context-path=/standing-orders

management.endpoints.web.exposure.include=*
management.endpoint.shutdown.enabled=true
management.endpoint.restart.enabled=true
server.error.include-stacktrace=always

spring.jackson.mapper.default-view-inclusion=true
spring.jackson.serialization.write_dates_as_timestamps=false
spring.jackson.deserialization.read-unknown-enum-values-as-null=false

spring.artemis.mode=native
spring.artemis.host=artemis
spring.artemis.port=61616
spring.artemis.user=artemis
spring.artemis.password=simetraehcapa

spring.jpa.database=postgresql
spring.jpa.show-sql=false
spring.jpa.generate-ddl=false

spring.datasource.platform=postgres
spring.datasource.url=****************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driverClassName=org.postgresql.Driver

# batch size
spring.jpa.properties.hibernate.jdbc.batch_size=5000

standingorders.standingOrderNumberMask=SO-%08d
standingorders.standingOrderNumberInitialValue=25000

lucy-api-service.url=http://k8s-prod.peterpal.local/lucy/api
catalogue-service.url=http://catalogue.cw-uat.peterpal.local:8280/cat

## MULTIPART (Multipart Properties)
# Enable multipart uploads
spring.servlet.multipart.enabled=true

# Threshold after which files are written to disk.
spring.servlet.multipart.file-size-threshold=2KB

# Max file size
spring.servlet.multipart.max-file-size=2000MB

# Max Request Size
spring.servlet.multipart.max-request-size=215MB

## File Storage Properties
# All files uploaded through the REST API will be stored in this directory
file.upload-dir=

# Standing Order match related
# Uncomment to overwrite
#match.products.queue.name=new-products-to-match

# Keycloak properties
keycloak.realm = ppls-dev
keycloak.resource = standing-orders-api
keycloak.auth-server-url = http://keycloak.k8s-test.peterpal.local/auth
keycloak.ssl-required = external
keycloak.bearer-only = true
keycloak.cors = true
keycloak.credentials.secret = 21cb2e1b-f015-4982-b2c9-b8fca41c7401
ppls.keycloak.client-id=standing-orders-ui
# Switch on/off security
security.enabled=false

# matcher properties
matcher.ignore-pub-date.before-months-ago = 3
matcher.ignore-pub-date.after-months-ahead = 6
matcher.defer-pub-date.after-months-ahead = 3

# cron scheduler un-defer title At 12:01 AM
title.un-defer.scheduled.cron = 0 1 0 * * *

# Matching service properties
#match-service.form-details = B101,B104
match-service.form-width = 110
match-service.form-height = 178
match-service.form-tolerance = 10

# Restricted publishers
match-service.restricted-publishers = PublishAmerica; \
  Kessinger Publishing Co; \
  Forgotten Books; \
  Xlibris Corporation; \
  Lulu.com; \
  Indypublish.com; \
  iUniverse.com; \
  British Library, Historical Print Editions; \
  ABC-CLIO; \
  General Books LLC; \
  BiblioBazaar, LLC; \
  AuthorHouse; \
  Literary Licensing, LLC; \
  Wisehouse Classics; \
  Academic Internet Publishers Incorporated; \
  Richardson; \
  Rarebooksclub.com; \
  Palala Press; \
  Andesite Press; \
  Nabu Press; \
  Gale Ecco; \
  Bastian Books; \
  Sagwan Press; \
  Legare Street Press; \
  Wentworth Press; \
  Hassell Street Press; \
  Scholar''s Choice

# catalogue product update date range limit
process-product-update-after.date=
process-product-update-after.date.format=dd/MM/yyyy


standing-orders.post-flyway-script=GRANT ALL ON ALL TABLES IN SCHEMA public TO pplsit
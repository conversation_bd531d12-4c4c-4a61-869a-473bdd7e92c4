<?xml version="1.0" encoding="UTF-8" ?>
<entity-mappings
    xmlns="http://java.sun.com/xml/ns/persistence/orm"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://java.sun.com/xml/ns/persistence/orm orm_2_0.xsd"
    version="2.0">

  <entity class="org.axonframework.eventsourcing.eventstore.jpa.DomainEventEntry">
    <attribute-override name="payload">
      <column name="payload" column-definition="TEXT"/>
    </attribute-override>
    <attribute-override name="metaData">
      <column name="meta_data" column-definition="TEXT"/>

    </attribute-override>
  </entity>

  <entity class="org.axonframework.eventsourcing.eventstore.jpa.SnapshotEventEntry">
    <attribute-override name="payload">
      <column name="payload" column-definition="TEXT"/>
    </attribute-override>
    <attribute-override name="metaData">
      <column name="meta_data" column-definition="TEXT"/>
    </attribute-override>
  </entity>

  <entity class="org.axonframework.eventhandling.saga.repository.jpa.SagaEntry">
    <attribute-override name="serializedSaga">
      <column name="serialized_saga" column-definition="TEXT"/>
    </attribute-override>
  </entity>

  <entity class="org.axonframework.eventhandling.tokenstore.jpa.TokenEntry">
    <attribute-override name="token">
      <column name="token" column-definition="TEXT"/>
    </attribute-override>
  </entity>

  <mapped-superclass class="org.axonframework.eventsourcing.eventstore.AbstractEventEntry">
    <attributes>
      <basic name="payload" optional="false">
        <column column-definition="TEXT"/>
        <convert converter="au.com.peterpal.selecting.standingorders.JpaPostgreSqlBlobToTextConverter"/>
      </basic>

      <basic name="metaData" optional="false">
        <column column-definition="TEXT"/>
        <convert converter="au.com.peterpal.selecting.standingorders.JpaPostgreSqlBlobToTextConverter"/>
      </basic>

    </attributes>
  </mapped-superclass>

  <mapped-superclass class="org.axonframework.eventhandling.saga.repository.jpa.AbstractSagaEntry">
    <attributes>
      <basic name="serializedSaga" optional="false">
        <column column-definition="TEXT"/>
        <convert converter="au.com.peterpal.selecting.standingorders.JpaPostgreSqlBlobToTextConverter"/>
      </basic>
    </attributes>
  </mapped-superclass>

  <mapped-superclass class="org.axonframework.eventhandling.tokenstore.AbstractTokenEntry">
    <attributes>
      <basic name="token" optional="false">
        <column column-definition="TEXT"/>
        <convert converter="au.com.peterpal.selecting.standingorders.JpaPostgreSqlBlobToTextConverter"/>
      </basic>
    </attributes>
  </mapped-superclass>
</entity-mappings>

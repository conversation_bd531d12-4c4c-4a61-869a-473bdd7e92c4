#logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
#logging.level.org.springframework.integration.handler.LoggingHandler=DEBUG

# this is a dummy port, it might be used by other projects so care should be taken in development
server.port=41170

spring.datasource.url=********************************************************

#spring.flyway.enabled=false
#spring.jpa.generate-ddl=true
#spring.jpa.hibernate.ddl-auto=create-drop
#spring.jpa.properties.hibernate.format_sql=true
spring.flyway.out-of-order=true
spring.flyway.ignore-missing-migrations=true

#logging.level.com.querydsl.sql=DEBUG
#spring.jpa.show-sql=true


spring.artemis.host=localhost
lucy-api-service.url=http://internal-services-test:8080/lucy/api
catalogue-service.url=http://catalogue.cw-uat.peterpal.local:8280/cat

logging.level.au.com.peterpal.selecting.standingorders.gateways.clientweb.ClientWebApiClient = DEBUG

skip-matching-engine-initialisation=true
#skip-matching-engine-initialisation=false

# cron scheduler
title.un-defer.scheduled.cron = 0 */10 * * * *

#logging.level.com.querydsl.sql=DEBUG
#logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
#spring.jpa.show-sql=true
#spring.jpa.properties.hibernate.format_sql=true
#spring.jpa.hibernate.ddl-auto=validate

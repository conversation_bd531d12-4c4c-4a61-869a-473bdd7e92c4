ALTER TABLE public.allocation ADD COLUMN IF NOT EXISTS date_added timestamptz NULL;
ALTER TABLE public.allocation ADD COLUMN IF NOT EXISTS date_updated timestamptz NULL;

-- Update date_added and date_updated in allocation table based on latest record in audit_domain_event
WITH latest_created_audit AS (
    SELECT
        entity_id,
        MAX(date_time_created) AS latest_date
    FROM
        audit_domain_event
    WHERE
            payload_type = 'au.com.peterpal.selecting.standingorders.customerstandingorder.events.AllocationCreated'
    GROUP BY
        entity_id
),
     latest_updated_audit AS (
         SELECT
             entity_id,
             MAX(date_time_created) AS latest_date
         FROM
             audit_domain_event
         WHERE
                 payload_type = 'au.com.peterpal.selecting.standingorders.customerstandingorder.events.AllocationUpdated'
         GROUP BY
             entity_id
     ),
     alloc_to_update AS (
         SELECT
             a.id,
             la_created.latest_date AS created_date,
             la_updated.latest_date AS updated_date
         FROM
             allocation a
                 LEFT JOIN
             latest_created_audit la_created
             ON
                     a.id = la_created.entity_id
                 LEFT JOIN
             latest_updated_audit la_updated
             ON
                     a.id = la_updated.entity_id
         WHERE
             a.date_added IS NULL
            OR a.date_updated IS NULL
     )
UPDATE allocation
SET
    date_added = COALESCE(date_added, alloc_to_update.created_date),
    date_updated = COALESCE(date_updated, alloc_to_update.updated_date)
    FROM
    alloc_to_update
WHERE
    allocation.id = alloc_to_update.id;

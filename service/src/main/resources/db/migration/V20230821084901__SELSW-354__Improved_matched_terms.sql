ALTER TABLE matched_standing_order
    ADD COLUMN IF NOT EXISTS matched_term_tuples json;
ALTER TABLE standing_order_aggregated
    ADD COLUMN IF NOT EXISTS matched_term_tuples json;

ALTER TABLE standing_order_aggregated
    DROP COLUMN IF EXISTS term_id;
ALTER TABLE standing_order_aggregated
    DROP COLUMN IF EXISTS term_operation;
ALTER TABLE standing_order_aggregated
    DROP COLUMN IF EXISTS term_type;
ALTER TABLE standing_order_aggregated
    DROP COLUMN IF EXISTS term_value;

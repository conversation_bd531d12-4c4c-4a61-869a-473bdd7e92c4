CREATE TABLE IF NOT EXISTS public.title_allocations(
    allocation_id uuid NOT NULL CONSTRAINT fk_allocation_id REFERENCES allocation,
    title_id uuid NOT NULL CONSTRAINT fk_title_id REFERENCES title,
    PRIMARY KEY (allocation_id, title_id)
);

ALTER TABLE public.allocation DROP COLUMN IF EXISTS title_id;
ALTER TABLE public.pending_order ADD COLUMN IF NOT EXISTS title_id uuid CONSTRAINT fk_title_id REFERENCES title;



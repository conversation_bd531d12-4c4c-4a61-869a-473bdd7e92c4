ALTER TABLE public.rejection_reason_type
    ADD COLUMN IF NOT EXISTS displayable boolean default true;

INSERT INTO "public"."rejection_reason_type" (id, name, status, displayable)
VALUES ('STANDING_ORDER_REMATCHED', 'No longer matched after Standing Order Rematch', 'ACTIVE', false)
ON CONFLICT DO NOTHING;

update rejection_reason_type
set displayable = false
where id in ('ALLOCATION_MADE_INACTIVE', 'PRODUCT_UPDATED', 'REMATCHED', 'STANDING_ORDER_REMATCHED');
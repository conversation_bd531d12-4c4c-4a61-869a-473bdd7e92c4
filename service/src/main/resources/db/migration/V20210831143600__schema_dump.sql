--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';

SET default_tablespace = '';

--
-- Name: allocation; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.allocation (
                                   id uuid NOT NULL PRIMARY KEY,
                                   customer_reference character varying,
                                   delivery_instructions character varying,
                                   notes character varying,
                                   allocation_preference_id uuid,
                                   customer_standing_order_id uuid,
                                   fund_id uuid,
                                   orders_id uuid,
                                   category character varying,
                                   status text DEFAULT 'ACTIVE'::text,
                                   title_id uuid
);


--
-- Name: allocation_preference; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.allocation_preference (
                                              id uuid NOT NULL PRIMARY KEY,
                                              customer_reference character varying,
                                              delivery_instructions character varying,
                                              notes character varying,
                                              fund_id uuid,
                                              category character varying,
                                              status text,
                                              customer_id uuid
);


--
-- Name: audit_domain_event; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.audit_domain_event (
                                           id uuid NOT NULL PRIMARY KEY,
                                           entity_id uuid NOT NULL,
                                           date_time_created timestamp without time zone NOT NULL,
                                           event character varying NOT NULL,
                                           payload_type character varying NOT NULL,
                                           username character varying NOT NULL
);


--
-- Name: category_codes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.category_codes (
                                       title_id uuid NOT NULL,
                                       code text
);


--
-- Name: customer; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.customer (
                                 id uuid NOT NULL PRIMARY KEY,
                                 code character varying NOT NULL,
                                 name character varying NOT NULL
);


--
-- Name: customer_standing_order; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.customer_standing_order (
                                                id uuid NOT NULL PRIMARY KEY,
                                                customer_standing_order_status integer,
                                                customer_id uuid,
                                                standing_order_id uuid NOT NULL
);


--
-- Name: customer_standing_order_allocations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.customer_standing_order_allocations (
                                                            customer_standing_order_id uuid NOT NULL,
                                                            allocations_id uuid NOT NULL
);


--
-- Name: flyway_schema_history; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.flyway_schema_history (
                                              installed_rank integer NOT NULL PRIMARY KEY,
                                              version character varying(50),
                                              description character varying(200) NOT NULL,
                                              type character varying(20) NOT NULL,
                                              script character varying(1000) NOT NULL,
                                              checksum integer,
                                              installed_by character varying(100) NOT NULL,
                                              installed_on timestamp without time zone DEFAULT now() NOT NULL,
                                              execution_time integer NOT NULL,
                                              success boolean NOT NULL
);


--
-- Name: fund; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.fund (
                             id uuid NOT NULL PRIMARY KEY,
                             code character varying NOT NULL,
                             name character varying NOT NULL,
                             customer_id uuid NOT NULL
);


--
-- Name: hibernate_sequence; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE IF NOT EXISTS public.hibernate_sequence
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: item_type; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.item_type (
                                  id character varying NOT NULL PRIMARY KEY,
                                  code character varying NOT NULL,
                                  name character varying NOT NULL
);


--
-- Name: order_ids; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.order_ids (
                                  title_id uuid NOT NULL,
                                  order_ids character varying
);


--
-- Name: orders; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.orders (
                               id uuid NOT NULL PRIMARY KEY,
                               customer_reference character varying,
                               order_date date NOT NULL,
                               order_number character varying NOT NULL,
                               ordered_product_reference character varying,
                               customer_id uuid NOT NULL,
                               fund_id uuid
);


--
-- Name: pending_order; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.pending_order (
                                      id uuid NOT NULL PRIMARY KEY,
                                      order_status character varying NOT NULL,
                                      customer_id uuid NOT NULL,
                                      supplier_id uuid NOT NULL,
                                      customer_reference character varying,
                                      ordered_product_reference character varying,
                                      fund_id uuid,
                                      release_id uuid NOT NULL
);


--
-- Name: release; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.release (
                                id uuid NOT NULL,
                                allocation_id uuid,
                                release_preference_id uuid,
                                action_type text,
                                release_type text,
                                initial_assignment_rule text,
                                small_format_paperback_rule text,
                                fund_id uuid,
                                hard_back_fund_id uuid,
                                paper_back_fund_id uuid,
                                quantity integer,
                                hard_back_quantity integer,
                                paper_back_quantity integer
);


--
-- Name: release_preference; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.release_preference (
                                           id uuid NOT NULL PRIMARY KEY,
                                           action_type character varying,
                                           initial_assignment_rule character varying,
                                           release_type character varying,
                                           small_format_paperback_rule character varying,
                                           allocation_preference_id uuid,
                                           fund_id uuid,
                                           hard_back_fund_id uuid,
                                           paper_back_fund_id uuid,
                                           quantity integer DEFAULT 0,
                                           hardback_quantity integer DEFAULT 0,
                                           paperback_quantity integer DEFAULT 0
);


--
-- Name: sequence_definition; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.sequence_definition (
                                            id bigint NOT NULL PRIMARY KEY,
                                            format character varying,
                                            name character varying NOT NULL,
                                            tenant_id bigint NOT NULL,
                                            version bigint NOT NULL
);


--
-- Name: sequence_number; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.sequence_number (
                                        id bigint NOT NULL PRIMARY KEY,
                                        sequence_group character varying,
                                        number bigint NOT NULL,
                                        version bigint NOT NULL,
                                        definition_id bigint
);


--
-- Name: standing_order; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.standing_order (
                                       id uuid NOT NULL PRIMARY KEY,
                                       description character varying,
                                       notes character varying,
                                       standing_order_number character varying,
                                       standing_order_status integer
);


--
-- Name: subject_map; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.subject_map (
                                    subject text NOT NULL PRIMARY KEY,
                                    category text
);


--
-- Name: supplier; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.supplier (
                                 id uuid NOT NULL PRIMARY KEY,
                                 name character varying,
                                 code character varying
);


--
-- Name: term; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.term (
                             standing_order_id uuid NOT NULL,
                             corporate_name character varying,
                             imprint_name character varying,
                             person_name character varying,
                             series_title character varying,
                             title_without_prefix character varying,
                             sequence_id integer NOT NULL
);


--
-- Name: title; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE IF NOT EXISTS public.title (
                              id uuid NOT NULL PRIMARY KEY,
                              deferred_date timestamp with time zone,
                              release_type integer,
                              standing_order_id uuid,
                              title_status character varying,
                              product_id integer DEFAULT 0
);

--
-- Name: standing_order standing_order_number_idx; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.standing_order drop constraint if exists standing_order_number_idx;
ALTER TABLE ONLY public.standing_order
    ADD CONSTRAINT standing_order_number_idx UNIQUE (standing_order_number);


--
-- Name: term term_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.term drop constraint if exists term_pkey;
ALTER TABLE ONLY public.term
    ADD CONSTRAINT term_pkey PRIMARY KEY (standing_order_id, sequence_id);


--
-- Name: customer_standing_order_allocations uk_7sf41u3a4j6p8d0tqgpxxkpfm; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.customer_standing_order_allocations drop constraint if exists uk_7sf41u3a4j6p8d0tqgpxxkpfm;
ALTER TABLE ONLY public.customer_standing_order_allocations
    ADD CONSTRAINT uk_7sf41u3a4j6p8d0tqgpxxkpfm UNIQUE (allocations_id);


--
-- Name: flyway_schema_history_s_idx; Type: INDEX; Schema: public; Owner: -
--

drop index if exists flyway_schema_history_s_idx;
CREATE INDEX flyway_schema_history_s_idx ON public.flyway_schema_history USING btree (success);


--
-- Name: unq_allocation_pref_customer_id_category_idx; Type: INDEX; Schema: public; Owner: -
--

drop index if exists unq_allocation_pref_customer_id_category_idx;
CREATE UNIQUE INDEX unq_allocation_pref_customer_id_category_idx ON public.allocation_preference USING btree (customer_id, category);


--
-- Name: unq_title_id_idx; Type: INDEX; Schema: public; Owner: -
--
drop index if exists unq_title_id_idx;
CREATE UNIQUE INDEX unq_title_id_idx ON public.title USING btree (id);


--
-- Name: orders fk1svuvq0n1jfbbq1t6rsccqwlx; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.orders drop constraint if exists fk1svuvq0n1jfbbq1t6rsccqwlx;
ALTER TABLE ONLY public.orders
    ADD CONSTRAINT fk1svuvq0n1jfbbq1t6rsccqwlx FOREIGN KEY (fund_id) REFERENCES public.fund(id);


--
-- Name: allocation fk2l3bby75pwok8qkpyvxmhlv7r; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.allocation drop constraint if exists fk2l3bby75pwok8qkpyvxmhlv7r;
ALTER TABLE ONLY public.allocation
    ADD CONSTRAINT fk2l3bby75pwok8qkpyvxmhlv7r FOREIGN KEY (fund_id) REFERENCES public.fund(id);


--
-- Name: allocation_preference fk2l3bby75pwok8qkpyvxmhlv7r; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.allocation_preference drop constraint if exists fk2l3bby75pwok8qkpyvxmhlv7r;
ALTER TABLE ONLY public.allocation_preference
    ADD CONSTRAINT fk2l3bby75pwok8qkpyvxmhlv7r FOREIGN KEY (fund_id) REFERENCES public.fund(id);


--
-- Name: allocation fk4b5tkj1qos7txsv8l0dtcul9p; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.allocation drop constraint if exists fk4b5tkj1qos7txsv8l0dtcul9p;
ALTER TABLE ONLY public.allocation
    ADD CONSTRAINT fk4b5tkj1qos7txsv8l0dtcul9p FOREIGN KEY (allocation_preference_id) REFERENCES public.allocation_preference(id);


--
-- Name: customer_standing_order fk4phd0otqs97xxm1er2tl308nh; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.customer_standing_order drop constraint if exists fk4phd0otqs97xxm1er2tl308nh;
ALTER TABLE ONLY public.customer_standing_order
    ADD CONSTRAINT fk4phd0otqs97xxm1er2tl308nh FOREIGN KEY (customer_id) REFERENCES public.customer(id);


--
-- Name: orders fk624gtjin3po807j3vix093tlf; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.orders drop constraint if exists fk624gtjin3po807j3vix093tlf;
ALTER TABLE ONLY public.orders
    ADD CONSTRAINT fk624gtjin3po807j3vix093tlf FOREIGN KEY (customer_id) REFERENCES public.customer(id);


--
-- Name: release_preference fk8rxqo0g20fg4yu3nrqk6dgajl; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.release_preference drop constraint if exists fk8rxqo0g20fg4yu3nrqk6dgajl;
ALTER TABLE ONLY public.release_preference
    ADD CONSTRAINT fk8rxqo0g20fg4yu3nrqk6dgajl FOREIGN KEY (paper_back_fund_id) REFERENCES public.fund(id);


--
-- Name: release_preference fk8rxqo7g10fg4yu3nrqk6dgajl; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.release_preference drop constraint if exists fk8rxqo7g10fg4yu3nrqk6dgajl;
ALTER TABLE ONLY public.release_preference
    ADD CONSTRAINT fk8rxqo7g10fg4yu3nrqk6dgajl FOREIGN KEY (hard_back_fund_id) REFERENCES public.fund(id);


--
-- Name: release_preference fk8rxqo9g80fg4yu3nrqk6dgajl; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.release_preference drop constraint if exists fk8rxqo9g80fg4yu3nrqk6dgajl;
ALTER TABLE ONLY public.release_preference
    ADD CONSTRAINT fk8rxqo9g80fg4yu3nrqk6dgajl FOREIGN KEY (allocation_preference_id) REFERENCES public.allocation_preference(id);


--
-- Name: allocation fk_title_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.allocation drop constraint if exists fk_title_id;
ALTER TABLE ONLY public.allocation
    ADD CONSTRAINT fk_title_id FOREIGN KEY (title_id) REFERENCES public.title(id);


--
-- Name: customer_standing_order_allocations fkcnjgutf8rx1kppqop37qoaw7q; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.customer_standing_order_allocations drop constraint if exists fkcnjgutf8rx1kppqop37qoaw7q;
ALTER TABLE ONLY public.customer_standing_order_allocations
    ADD CONSTRAINT fkcnjgutf8rx1kppqop37qoaw7q FOREIGN KEY (allocations_id) REFERENCES public.allocation(id);


--
-- Name: pending_order fkfv6s0s4xknhc6ndl50jsoc0ot; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.pending_order drop constraint if exists fkfv6s0s4xknhc6ndl50jsoc0ot;
ALTER TABLE ONLY public.pending_order
    ADD CONSTRAINT fkfv6s0s4xknhc6ndl50jsoc0ot FOREIGN KEY (fund_id) REFERENCES public.fund(id);


--
-- Name: sequence_number fkimfdeun8v5x7v8edou5jhocp; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sequence_number drop constraint if exists fkimfdeun8v5x7v8edou5jhocp;
ALTER TABLE ONLY public.sequence_number
    ADD CONSTRAINT fkimfdeun8v5x7v8edou5jhocp FOREIGN KEY (definition_id) REFERENCES public.sequence_definition(id);


--
-- Name: fund fkiqcvd8wtdyl9uasnjr216s0u; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.fund drop constraint if exists fkiqcvd8wtdyl9uasnjr216s0u;
ALTER TABLE ONLY public.fund
    ADD CONSTRAINT fkiqcvd8wtdyl9uasnjr216s0u FOREIGN KEY (customer_id) REFERENCES public.customer(id);


--
-- Name: allocation fkj0tvh1x3sx2inrdq1edldgw8g; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.allocation drop constraint if exists fkj0tvh1x3sx2inrdq1edldgw8g;
ALTER TABLE ONLY public.allocation
    ADD CONSTRAINT fkj0tvh1x3sx2inrdq1edldgw8g FOREIGN KEY (customer_standing_order_id) REFERENCES public.customer_standing_order(id);


--
-- Name: term fkned086nudle8j6et3112l3pvq; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.term drop constraint if exists fkned086nudle8j6et3112l3pvq;
ALTER TABLE ONLY public.term
    ADD CONSTRAINT fkned086nudle8j6et3112l3pvq FOREIGN KEY (standing_order_id) REFERENCES public.standing_order(id);


--
-- Name: customer_standing_order fkned086nudle8j6et5912l3pvq; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.customer_standing_order drop constraint if exists fkned086nudle8j6et5912l3pvq;
ALTER TABLE ONLY public.customer_standing_order
    ADD CONSTRAINT fkned086nudle8j6et5912l3pvq FOREIGN KEY (standing_order_id) REFERENCES public.standing_order(id);


--
-- Name: pending_order fknezrvx9k5avrogj5v6ktrhrfk; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.pending_order drop constraint if exists fknezrvx9k5avrogj5v6ktrhrfk;
ALTER TABLE ONLY public.pending_order
    ADD CONSTRAINT fknezrvx9k5avrogj5v6ktrhrfk FOREIGN KEY (customer_id) REFERENCES public.customer(id);


--
-- Name: title fkntref7nt33m031wje62ngxa5t; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.title drop constraint if exists fkntref7nt33m031wje62ngxa5t;
ALTER TABLE ONLY public.title
    ADD CONSTRAINT fkntref7nt33m031wje62ngxa5t FOREIGN KEY (standing_order_id) REFERENCES public.standing_order(id);


--
-- Name: pending_order fknxst0ujcgpbf9xdwua9487fh3; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.pending_order drop constraint if exists fknxst0ujcgpbf9xdwua9487fh3;
ALTER TABLE ONLY public.pending_order
    ADD CONSTRAINT fknxst0ujcgpbf9xdwua9487fh3 FOREIGN KEY (supplier_id) REFERENCES public.supplier(id);


--
-- Name: customer_standing_order_allocations fkowjvsvumxiie8eg4c72jns49j; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.customer_standing_order_allocations drop constraint if exists fkowjvsvumxiie8eg4c72jns49j;
ALTER TABLE ONLY public.customer_standing_order_allocations
    ADD CONSTRAINT fkowjvsvumxiie8eg4c72jns49j FOREIGN KEY (customer_standing_order_id) REFERENCES public.customer_standing_order(id);


--
-- Name: order_ids fks6s382em20205stpoa9916ssj; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.order_ids drop constraint if exists fks6s382em20205stpoa9916ssj;
ALTER TABLE ONLY public.order_ids
    ADD CONSTRAINT fks6s382em20205stpoa9916ssj FOREIGN KEY (title_id) REFERENCES public.title(id);


--
-- Name: allocation fks6s382em20205stpoa9916ssp; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.allocation drop constraint if exists fks6s382em20205stpoa9916ssp;
ALTER TABLE ONLY public.allocation
    ADD CONSTRAINT fks6s382em20205stpoa9916ssp FOREIGN KEY (orders_id) REFERENCES public.orders(id);


--
-- Name: release_preference fkt64avsg4shonnhwofe3w6hpfw; Type: FK CONSTRAINT; Schema: public; Owner: -
--
ALTER TABLE ONLY public.release_preference drop constraint if exists fkt64avsg4shonnhwofe3w6hpfw;
ALTER TABLE ONLY public.release_preference
    ADD CONSTRAINT fkt64avsg4shonnhwofe3w6hpfw FOREIGN KEY (fund_id) REFERENCES public.fund(id);

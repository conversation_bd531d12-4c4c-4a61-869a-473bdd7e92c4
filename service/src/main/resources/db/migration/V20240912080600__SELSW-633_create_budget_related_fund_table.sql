CREATE TABLE IF NOT EXISTS public.budget_related_fund (
                                                          id uuid NOT NULL,
                                                          fund_id uuid NOT NULL,
                                                          budget_id uuid NOT NULL,
                                                          CONSTRAINT budget_related_fund_pkey PRIMARY KEY (id),
                                                          CONSTRAINT fk_budget_related_fund_fund_id FOREIGN KEY (fund_id) REFERENCES public.fund(id),
                                                          CONSTRAINT fk_budget_related_fund_budget_id FOREIGN KEY (budget_id) REFERENCES public.budget(id));

ALTER TABLE public.budget ADD COLUMN IF NOT EXISTS top_up_amount numeric(10, 2) NOT NULL DEFAULT 0;
GRANT ALL ON public.budget_related_fund TO pplsit;

CREATE TABLE IF NOT EXISTS public.budget_target (
                                                    id uuid NOT NULL,
                                                    customer_id uuid NOT NULL,
                                                    start_date date NOT NULL,
                                                    end_date date NULL,
                                                    jul_target float NOT NULL,
                                                    aug_target float NOT NULL,
                                                    sep_target float NOT NULL,
                                                    oct_target float NOT NULL,
                                                    nov_target float NOT NULL,
                                                    dec_target float NOT NULL,
                                                    jan_target float NOT NULL,
                                                    feb_target float NOT NULL,
                                                    mar_target float NOT NULL,
                                                    apr_target float NOT NULL,
                                                    may_target float NOT NULL,
                                                    jun_target float NOT NULL,
                                                    CONSTRAINT budget_target_pkey PRIMARY KEY (id)
    );
SELECT create_foreign_key_if_not_exists('budget_target', 'budget_target__customer_fk',
                                        'FOREIGN KEY (customer_id) REFERENCES public.customer(id)');
SELECT create_constraint_if_not_exists('budget_target', 'budget_target_uni',  'UNIQUE (customer_id,start_date,end_date)');
GRANT ALL ON public.budget_target TO pplsit;
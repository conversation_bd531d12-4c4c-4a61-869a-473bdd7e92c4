DO $$
    BEGIN
        IF EXISTS
            ( SELECT 1
              FROM   information_schema.tables
              WHERE  table_schema = 'public'
              AND    table_name = 'release'
            )
        THEN
            UPDATE release
            set initial_assignment_rule = 'FIRST_AVAILABLE'
            where initial_assignment_rule is null;
        END IF ;
    END
$$ ;

ALTER TABLE allocation
    ADD COLUMN IF NOT EXISTS ba_parent_id uuid;

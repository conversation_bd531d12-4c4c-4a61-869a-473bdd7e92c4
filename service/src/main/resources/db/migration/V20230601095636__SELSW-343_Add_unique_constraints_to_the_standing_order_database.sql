SELECT create_constraint_if_not_exists('supplier', 'supplier_code_idx', 'UNIQUE (code, status)');
SELECT create_constraint_if_not_exists('customer', 'customer_code_idx', 'UNIQUE (code)');
SELECT create_constraint_if_not_exists('branch', 'branch_customer_code_idx', 'UNIQUE (customer_id, code, status)');
SELECT create_constraint_if_not_exists('fund', 'fund_customer_code_idx', 'UNIQUE (customer_id, code, status)');

create table if not exists allocation_categories
(
    allocation_id  uuid not null,
    categories_id    uuid not null
);

SELECT create_foreign_key_if_not_exists('allocation_categories', 'allocation_category_unique_index',
                                        'UNIQUE (allocation_id, categories_id)');

SELECT create_foreign_key_if_not_exists('allocation_categories', 'allocation_category__allocation_fk',
                                        'FOREIGN KEY (allocation_id) REFERENCES public.allocation(id)');

SELECT create_foreign_key_if_not_exists('allocation_categories', 'allocation_category__category_fk',
                                        'FOREIGN KEY (categories_id) REFERENCES public.category(id)');

SELECT rename_column_key_if_not_exists('allocation', 'category', 'category_to_be_deleted');

create table if not exists allocation_preference_categories
(
    allocation_preference_id  uuid not null,
    categories_id               uuid not null
);

SELECT create_foreign_key_if_not_exists('allocation_preference_categories', 'allocation_preference_category_unique_index',
                                        'UNIQUE (allocation_preference_id, categories_id)');

SELECT create_foreign_key_if_not_exists('allocation_preference_categories', 'allocation_preference_category__allocation_fk',
                                        'FOREIGN KEY (allocation_preference_id) REFERENCES public.allocation_preference(id)');

SELECT create_foreign_key_if_not_exists('allocation_preference_categories', 'allocation_preference_category__category_fk',
                                        'FOREIGN KEY (categories_id) REFERENCES public.category(id)');

SELECT rename_column_key_if_not_exists('allocation_preference', 'category', 'category_to_be_deleted');

SELECT create_foreign_key_if_not_exists('allocation', 'allocation_branch_fk',
                                        'FOREIGN KEY (branch_id) REFERENCES public.branch(id)');

ALTER TABLE pending_order ADD COLUMN IF NOT EXISTS category_id uuid;
SELECT rename_column_key_if_not_exists('pending_order', 'category', 'category_to_be_deleted');


SELECT create_foreign_key_if_not_exists('pending_order', 'pending_order_category_fk',
                                        'FOREIGN KEY (category_id) REFERENCES public.category(id)');

DROP INDEX IF EXISTS pending_order_unique_idx;

CREATE UNIQUE INDEX
    IF NOT EXISTS pending_order_unique_idx
    ON public.pending_order (title_id, customer_id, category_id, fund_id, branch_id);

ALTER TABLE category_mapping ADD COLUMN IF NOT EXISTS category_id uuid;

SELECT create_foreign_key_if_not_exists('category_mapping', 'category_mapping_category_fk',
                                        'FOREIGN KEY (category_id) REFERENCES public.category(id)');

ALTER TABLE title ADD COLUMN IF NOT EXISTS category_id uuid;
SELECT rename_column_key_if_not_exists('title', 'category', 'category_to_be_deleted');

SELECT create_foreign_key_if_not_exists('title', 'title_category_fk',
                                        'FOREIGN KEY (category_id) REFERENCES public.category(id)');


alter table matched_product ADD COLUMN IF NOT EXISTS category_id uuid;
SELECT rename_column_key_if_not_exists('matched_product', 'category', 'category_to_be_deleted');

SELECT create_foreign_key_if_not_exists('matched_product', 'matched_product_category_fk',
                                        'FOREIGN KEY (category_id) REFERENCES public.category(id)');

alter table ad_hoc_product ADD COLUMN IF NOT EXISTS category_id uuid;
SELECT rename_column_key_if_not_exists('ad_hoc_product', 'category', 'category_to_be_deleted');

SELECT create_foreign_key_if_not_exists('ad_hoc_product', 'ad_hoc_product_category_fk',
                                        'FOREIGN KEY (category_id) REFERENCES public.category(id)');

INSERT INTO public.category (id, code, description, status)
VALUES ('027a0d36-f930-4e65-95ec-2d2c07e68d89', 'AN', 'Adult Non Fiction', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('6a06b3ae-bca5-4689-a164-71addc1d1fa0', 'YN', 'Young Adult Nonfiction', 'ACTIVE')
on conflict do nothing;

insert into allocation_categories
SELECT DISTINCT  a.id, c.id
FROM allocation a
         JOIN category c on c.code = upper(trim(a.category_to_be_deleted))
    and category_to_be_deleted not like '%,%'
on conflict do nothing;

insert into allocation_categories
SELECT DISTINCT  a.id, c.id
FROM allocation a
         JOIN category c on c.code = 'AFG'
    and category_to_be_deleted like '%AFG,%'
on conflict do nothing;

insert into allocation_categories
SELECT DISTINCT a.id, c.id
FROM allocation a
         JOIN category c on c.code = 'AN'
    and category_to_be_deleted like '%,AN%'
on conflict do nothing;

insert into allocation_preference_categories
SELECT DISTINCT a.id, c.id
FROM allocation_preference a
         JOIN category c on c.code = upper(trim(a.category_to_be_deleted))
    and category_to_be_deleted not like '%,%'
on conflict do nothing;

WITH ct AS (
    SELECT id, code
    FROM category
)
UPDATE title AS t
SET category_id = ct.id
FROM ct
WHERE upper(trim(t.category_to_be_deleted))  = ct.code;

WITH cmp AS (SELECT id, code
             FROM category)
UPDATE matched_product AS mp
SET category_id = cmp.id
FROM cmp
WHERE upper(trim(mp.category_to_be_deleted)) = cmp.code;

WITH cmp AS (SELECT id, code
             FROM category)
UPDATE ad_hoc_product AS mp
SET category_id = cmp.id
FROM cmp
WHERE upper(trim(mp.category_to_be_deleted)) = cmp.code;

WITH cmp AS (SELECT id, code
             FROM category)
UPDATE pending_order AS mp
SET category_id = cmp.id
FROM cmp
WHERE upper(trim(mp.category_to_be_deleted)) = cmp.code;

WITH cmp AS (SELECT id, code
             FROM category)
update category_mapping as cm
SET category_id = cmp.id
FROM cmp
WHERE upper(trim(cm.category)) = cmp.code;







ALTER TABLE public.orders ADD COLUMN IF NOT EXISTS staff varchar;
ALTER TABLE public.orders ADD COLUMN IF NOT EXISTS status varchar DEFAULT 'ON_ORDER' NOT NULL;
ALTER TABLE public.orders ADD COLUMN IF NOT EXISTS unit_price_amount numeric(11,4);
ALTER TABLE public.orders ADD COLUMN IF NOT EXISTS qty_ordered int;


CREATE TABLE IF NOT EXISTS public.budget (
                               id uuid NOT NULL,
                               customer_id uuid NOT NULL,
                               fund_id uuid NOT NULL,
                               amount numeric(10, 2) NOT NULL,
                               start_date date NOT NULL,
                               end_date date NULL,
                               currency_code text NOT NULL,
                               "type" text NULL,
                               CONSTRAINT budget_pkey PRIMARY KEY (id)
);

SELECT create_foreign_key_if_not_exists('budget', 'fk_budget_fund_id', 'FOREIGN KEY (fund_id) REFERENCES public.fund(id)');
SELECT create_foreign_key_if_not_exists('budget', 'fk_budget_customer_id', 'FOREIG<PERSON> KEY (customer_id) REFERENCES public.customer(id)');

GRANT ALL ON public.budget TO pplsit;
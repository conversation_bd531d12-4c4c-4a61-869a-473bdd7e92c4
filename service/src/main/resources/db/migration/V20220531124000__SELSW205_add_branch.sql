CREATE TABLE IF NOT EXISTS public.branch (
    id uuid NOT NULL PRIMARY KEY,
    code text NOT NULL,
    name text NOT NULL,
    status text NOT NULL,
    customer_id uuid NOT NULL
);

ALTER TABLE allocation ADD COLUMN IF NOT EXISTS branch_id uuid;

create index if not exists branch_code on public.branch(code);
SELECT create_foreign_key_if_not_exists('branch', 'fk_customer_id', 'FOREIGN KEY (customer_id) REFERENCES public.customer(id)');




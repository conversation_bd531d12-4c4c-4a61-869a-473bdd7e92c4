CREATE TABLE IF NOT EXISTS public.ad_hoc_product
(
    id                   uuid NOT NULL PRIMARY KEY,
    category             character varying,
    title_without_prefix character varying,
    subtitle             character varying,
    series               character varying,
    isbn                 character varying,
    person_name          character varying,
    corporate_name       character varying,
    imprint              character varying,
    publication_date     date,
    price                numeric(11,4),
    title_id             uuid NOT NULL
);



ALTER TABLE ONLY public.ad_hoc_product
    ADD CONSTRAINT ad_hoc_product_title FOREIGN KEY (title_id) REFERENCES public.title (id);
-- Add the title_number column
ALTER TABLE public.title ADD COLUMN IF NOT EXISTS title_number varchar;
SELECT create_constraint_if_not_exists('title', 'title_number_un', 'UNIQUE (title_number)');

INSERT INTO public.sequence_definition
(id, format, "name", tenant_id, "version")
VALUES(3, 'TM-%08d', 'TITLE_NUMBER', 1, 0) on conflict do nothing;

INSERT INTO public.sequence_number
(id, sequence_group, "number", "version", definition_id)
VALUES(4, NULL, 1, 0, 3) on conflict do nothing;

-- Generate the title_number values
DO
$$
DECLARE
rec RECORD;
    counter INTEGER := 1;
    formatted_number TEXT;
BEGIN
FOR rec IN
SELECT id
FROM public.title
WHERE title_number IS NULL
ORDER BY date_added ASC
    LOOP
        -- Format the counter to match the desired pattern
        formatted_number := 'TM-' || LPAD(counter::text, 8, '0');

-- Update the title_number for the current record
UPDATE public.title
SET title_number = formatted_number
WHERE id = rec.id;

-- Increment the counter
counter := counter + 1;
END LOOP;

UPDATE public.sequence_number
SET "number"= counter;
END $$;



CREATE TABLE IF NOT EXISTS public.title
(
    id             uuid NOT NULL PRIMARY KEY,
    title          character varying,
    person_name    character varying,
    series         character varying,
    corporate_name character varying,
    imprint        character varying,
    title_status   character varying,
    date_added     TIMESTAMP with time zone,
    date_modified  TIMESTAMP with time zone
);
ALTER TABLE ONLY public.title
    ADD CONSTRAINT title_unique_const UNIQUE (title, person_name);

CREATE TABLE IF NOT EXISTS public.matched_product
(
    id                   uuid NOT NULL PRIMARY KEY,
    title_id             uuid NOT NULL,
    category             character varying,
    catalogue_id         numeric,
    product_title        character varying,
    series               character varying,
    isbn                 character varying,
    person_name          character varying,
    corporate_name       character varying,
    imprint              character varying
);
ALTER TABLE ONLY public.matched_product
    ADD CONSTRAINT matchedProduct_title_fk FOREIGN KEY (title_id) REFERENCES public.title (id);

CREATE TABLE IF NOT EXISTS public.matched_standing_order
(
    id                uuid NOT NULL PRIMARY KEY,
    matched_product_id    uuid NOT NULL,
    standing_order_id uuid NOT NULL,
    term_id           uuid NOT NULL
);
ALTER TABLE ONLY public.matched_standing_order
    ADD CONSTRAINT matchedStandingOrder_matchedProduct_fk FOREIGN KEY (matched_product_id) REFERENCES public.matched_product (id);
ALTER TABLE ONLY public.matched_standing_order
    ADD CONSTRAINT matchedStandingOrder_standingOrder_fk FOREIGN KEY (standing_order_id) REFERENCES public.standing_order (id);
ALTER TABLE ONLY public.matched_standing_order
    ADD CONSTRAINT matchedStandingOrder_term_fk FOREIGN KEY (term_id) REFERENCES public.term (id);

CREATE TABLE IF NOT EXISTS public.product_aggregated
(
    id             uuid NOT NULL PRIMARY KEY,
    title_id       uuid NOT NULL,
    matched_product_id uuid NOT NULL,
    status         character varying
);
ALTER TABLE ONLY public.product_aggregated
    ADD CONSTRAINT productAggregated_title_fk FOREIGN KEY (title_id) REFERENCES public.title (id);
ALTER TABLE ONLY public.product_aggregated
    ADD CONSTRAINT productAggregated_matchedProduct_fk FOREIGN KEY (matched_product_id) REFERENCES public.matched_product (id);

CREATE TABLE IF NOT EXISTS public.standing_order_aggregated
(
    id                uuid NOT NULL PRIMARY KEY,
    title_id          uuid NOT NULL,
    standing_order_id uuid NOT NULL,
    term_id           uuid NOT NULL,
    status            character varying
);
ALTER TABLE ONLY public.standing_order_aggregated
    ADD CONSTRAINT standingOrderAggregated_title_fk FOREIGN KEY (title_id) REFERENCES public.title (id);
ALTER TABLE ONLY public.standing_order_aggregated
    ADD CONSTRAINT standingOrderAggregated_standingOrder_fk FOREIGN KEY (standing_order_id) REFERENCES public.standing_order (id);
ALTER TABLE ONLY public.standing_order_aggregated
    ADD CONSTRAINT standingOrderAggregated_term_fk FOREIGN KEY (term_id) REFERENCES public.term (id);

CREATE TABLE IF NOT EXISTS public.category
(
    id          uuid              NOT NULL PRIMARY KEY,
    code        character varying NOT NULL UNIQUE,
    description character varying NOT NULL,
    status      character varying NOT NULL
);

CREATE INDEX IF NOT EXISTS category_code_idx ON public.category (code);

INSERT INTO public.category (id, code, description, status)
VALUES ('60c03c08-d39a-448f-af0b-3b3962626def', 'AF', 'Adult Fiction', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('2d1d1a00-406b-4bb6-8916-8a4534503f70', 'AFG', 'Adult Fiction - Graphic Novel', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('5c97947e-0b0c-470c-8acd-0d7de6734dcb', 'AFR', 'Adult Fiction - Romance', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('70b96315-48e8-4f66-91c0-2b91482d454c', 'AFW', 'Adult Fiction - Wester', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('81f5591c-94ac-427c-a908-27a2d59d210f', 'A', 'Adult Nonfiction', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('293376ce-959d-401b-8364-b5eba542d15b', 'ANR', 'Adult Reference', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('69774d29-39d5-403e-99ab-41a54d94d76c', 'C', 'Audio', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('fc0ba5bf-dfd5-49e6-8326-d4f69febd21c', 'CAF', 'Audiobook Adult Fiction', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('2ada8075-8e47-4eb8-a82f-0fa114a0d660', 'CA', 'Audiobook Adult Nonfiction', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('e9c794b3-cfed-442b-b492-d36785a397f0', 'JFSO', 'Junior Fiction Standing Order', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('f335269d-c80d-44b9-8aa4-6c7a565e7e3c', 'L', 'Large Print', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('765af645-d04b-4f14-a6e0-bab67bac2c3d', 'LAF', 'Large Print Adult Fiction', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('601d735b-8a0c-46d3-ac92-f05bcff965ad', 'LA', 'Large Print Adult Nonfiction', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('25a18ec2-ace2-4dfc-bb61-eadf78d54b36', 'Y', 'Children''s/YA All', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('2fb3b510-99bb-4193-99b6-db61c7a216d6', 'YF', 'Children''s/YA Fictio', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('4b17ce7c-3672-4f13-8912-d1515353c3c3', 'YFG', 'Children''s/YA Fiction - Graphic Novel', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('84d7f44a-ddfe-43f2-9af6-8ec0154ffd44', 'YFJ', 'Junior Fiction', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('34807739-a377-46b3-ab92-3d79bea25e92', 'YFJB', 'Junior Board Books', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('80074dc1-6bde-4e65-a876-599076680c49', 'YFJE', 'Junior Beginner Readers (Easies)', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('9be83117-976e-4596-8d57-b8b4e654cd28', 'YFJG', 'Junior Graphic Novels', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('8696034b-2beb-476c-98b6-c2e1e781b848', 'YFP', 'Children''s/YA Picture Books', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('c973e88e-615e-4a38-9864-5e7b634ef045', 'YFY', 'Young Adult Fiction', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('e2165c82-7fb6-4785-866e-d60db71698f5', 'YFYG', 'Young Adult Graphic Novels', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('57b5cbe8-967a-4341-b721-4128aebd62bf', 'Y', 'Children''s/YA Nonfiction', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('d6721399-dfeb-49ae-bf94-11f23390abfd', 'YNJ', 'Junior Nonfiction', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('5faf8ec3-53a7-4309-8aa7-6d488b174320', 'YNJR', 'Junior Reference', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('dbc4d780-2e55-4701-aed8-bdabeb8ad1f7', 'YNR', 'Children''s/YA Reference', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('e8347c7d-ea73-4515-84ff-6e3708be5271', 'YNY', 'Young Adult Nonfiction', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('aa14b3e0-8344-431a-ab3e-ec51e14eacf3', 'YNYR', 'Young Adult Reference', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('3aba4c01-541e-4e56-ba1c-97e51ad7af00', 'YX', 'Children''s/YA - Personal and social topics', 'ACTIVE')
on conflict do nothing;
INSERT INTO public.category (id, code, description, status)
VALUES ('341b2c6c-e451-4341-bee6-e763eb026fed', 'YXK',
        'Childre''s/YA - Personal and social topics - Disability, impairments and special needs', 'ACTIVE')
on conflict do nothing;

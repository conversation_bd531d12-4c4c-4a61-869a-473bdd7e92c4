CREATE TABLE IF NOT EXISTS public.rejection_reason_type (
    id character varying PRIMARY KEY,
    name character varying NOT NULL,
    status character varying
);

INSERT INTO "public"."rejection_reason_type" (id, name, status) VALUES ('UNSUITABLE_FORMAT' ,'Unsuitable Format' ,'ACTIVE') ON CONFLICT DO NOTHING;
INSERT INTO "public"."rejection_reason_type" (id, name, status) VALUES ('ANTHOLOGY' ,'Anthology (group of authors contributing)' ,'ACTIVE') ON CONFLICT DO NOTHING;
INSERT INTO "public"."rejection_reason_type" (id, name, status) VALUES ('INCORRECT_AUTHOR' ,'Incorrect Author (not the correct author we are looking for but has the same name)' ,'ACTIVE') ON CONFLICT DO NOTHING;
INSERT INTO "public"."rejection_reason_type" (id, name, status) VALUES ('REISSUE' ,'Reissue' ,'ACTIVE') ON CONFLICT DO NOTHING;
INSERT INTO "public"."rejection_reason_type" (id, name, status) VALUES ('NON_ENGLISH_EDITION' ,'Non English edition' ,'ACTIVE') ON CONFLICT DO NOTHING;
INSERT INTO "public"."rejection_reason_type" (id, name, status) VALUES ('BIND_UPS' ,'Bind ups' ,'ACTIVE') ON CONFLICT DO NOTHING;
INSERT INTO "public"."rejection_reason_type" (id, name, status) VALUES ('NOT_ATTACHED_TO_STANDING_ORDER' ,'Not attached to a standing order ( we say NO S/O)' ,'ACTIVE') ON CONFLICT DO NOTHING;
INSERT INTO "public"."rejection_reason_type" (id, name, status) VALUES ('OVERSEAS_EDITION' ,'Overseas edition' ,'ACTIVE') ON CONFLICT DO NOTHING;
INSERT INTO "public"."rejection_reason_type" (id, name, status) VALUES ('DUPLICATE_RECORDS' ,'Duplicate Records' ,'ACTIVE') ON CONFLICT DO NOTHING;
INSERT INTO "public"."rejection_reason_type" (id, name, status) VALUES ('PUBLICATION_CANCELLED' ,'Publication Cancelled or Not Available to Order' ,'ACTIVE') ON CONFLICT DO NOTHING;
INSERT INTO "public"."rejection_reason_type" (id, name, status) VALUES ('ALREADY_PROCESSED' ,'Standing Order Already processed - we add in the date that the standing order is processed' ,'ACTIVE') ON CONFLICT DO NOTHING;
INSERT INTO "public"."rejection_reason_type" (id, name, status) VALUES ('OTHER' ,'Other reason' ,'ACTIVE') ON CONFLICT DO NOTHING;

ALTER TABLE title
    ADD COLUMN IF NOT EXISTS rejection_reason_type_id character varying,
    ADD COLUMN IF NOT EXISTS rejection_other_reason character varying;

ALTER TABLE public.standing_order_aggregated
    ADD COLUMN IF NOT EXISTS term_type character varying;
ALTER TABLE public.standing_order_aggregated
    ADD COLUMN IF NOT EXISTS term_operation character varying;
ALTER TABLE public.standing_order_aggregated
    ADD COLUMN IF NOT EXISTS term_value character varying;

ALTER TABLE public.standing_order_aggregated
    DROP CONSTRAINT IF EXISTS standingorderaggregated_term_fk;

ALTER TABLE public.matched_standing_order
    DROP CONSTRAINT IF EXISTS matchedstandingorder_term_fk;

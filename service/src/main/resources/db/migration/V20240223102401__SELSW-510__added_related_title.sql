CREATE TABLE IF NOT EXISTS public.related_title
(
    id                uuid NOT NULL PRIMARY KEY,
    original_title_id uuid NOT NULL,
    related_title_id  uuid NOT NULL
);

CREATE INDEX IF NOT EXISTS related_title_original_title_id_idx ON public.related_title (original_title_id);
CREATE INDEX IF NOT EXISTS related_title_related_title_id_idx ON public.related_title (related_title_id);

ALTER TABLE public.title
    ADD COLUMN IF NOT EXISTS type text default 'ORIGINAL';
CREATE TABLE IF NOT EXISTS public.product
(
    id                   uuid NOT NULL PRIMARY KEY,
    product_reference    character varying,
    title_without_prefix character varying,
    series_title         character varying,
    bic_subject_code     character varying,
    publication_date     date,
    edition_statement    character varying,
    form_code            character varying,
    language_code        character varying,
    catalogue_id         numeric,
    height               numeric,
    width                numeric,
    title                json,
    imprint_names        json,
    contributors         json,
    publishers           json,
    subject_codes        json,
    form_details         json
);

CREATE INDEX IF NOT EXISTS product_pubDate_idx ON public.product (publication_date);
CREATE INDEX IF NOT EXISTS product_productReference_idx ON public.product (product_reference);
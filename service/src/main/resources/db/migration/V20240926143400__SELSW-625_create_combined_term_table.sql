-- Create the new combined_term table
CREATE TABLE IF NOT EXISTS public.combined_term (
                                      id uuid NOT NULL,
                                      PRIMARY KEY (id)
);

-- Add the combined_term_id column to the term table
ALTER TABLE term ADD COLUMN IF NOT EXISTS combined_term_id uuid;

-- For existing terms that have an and_term, create a new combined_term entry
WITH term_pairs AS (
    SELECT DISTINCT and_term
    FROM term
    WHERE and_term IS NOT NULL
)
INSERT INTO combined_term (id)
SELECT DISTINCT and_term FROM term_pairs on conflict do nothing;

-- Update the term table to link terms to the new combined_term
UPDATE term
SET combined_term_id = and_term
WHERE and_term is not null;

-- Create combined_term entries for terms that don't have an and_term
INSERT INTO combined_term (id)
SELECT id FROM term
WHERE and_term IS NULL
    ON CONFLICT DO NOTHING;

-- For terms that don't have and_term, set combined_term_id to the term id
UPDATE term
SET combined_term_id = id
WHERE combined_term_id IS NULL;

-- Drop the old and_term column
ALTER TABLE term DROP COLUMN IF EXISTS and_term;
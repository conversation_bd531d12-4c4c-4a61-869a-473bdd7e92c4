CREATE TABLE IF NOT EXISTS public.fee (
                            id uuid NOT NULL,
                            amount numeric(9, 2) NULL,
                            code text NULL,
                            status text NULL,
                            customer_id uuid NULL,
                            currency_code text DEFAULT 'AUD'::text NOT NULL,
                            department text NULL,
                            description text NULL,
                            CONSTRAINT fee_code_idx UNIQUE (customer_id, code, status),
                            CONSTRAINT fee_pkey PRIMARY KEY (id),
                            CONSTRAINT fk_fee_customer_id FOREIGN KEY (customer_id) REFERENCES public.customer(id)
);

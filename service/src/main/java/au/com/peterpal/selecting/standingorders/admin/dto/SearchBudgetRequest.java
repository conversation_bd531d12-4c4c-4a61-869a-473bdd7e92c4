package au.com.peterpal.selecting.standingorders.admin.dto;

import au.com.peterpal.selecting.standingorders.ext.budget.entity.FundType;
import java.time.LocalDate;
import java.util.List;

import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import lombok.*;
import org.springframework.data.domain.Sort;

@Data
@Builder
@ToString
@Setter
public class SearchBudgetRequest {
  String customerCode;
  List<String> fundCodes;
  FundType fundType;
  String staff;
  String sortByKey;
  LocalDate budgetStartDate;
  LocalDate budgetEndDate;
  List<CategoryId> categoryIds;
  @Builder.Default Sort.Direction sortByDirection = Sort.Direction.ASC;
}

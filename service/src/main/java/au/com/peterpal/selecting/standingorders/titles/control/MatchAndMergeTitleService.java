package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationService;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.standingorder.control.CategoryService;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderRepository;
import au.com.peterpal.selecting.standingorders.standingorder.control.TermRepository;
import au.com.peterpal.selecting.standingorders.standingorder.events.CategoryAssigned;
import au.com.peterpal.selecting.standingorders.standingorder.events.MatchFound;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.Term;
import au.com.peterpal.selecting.standingorders.titles.dto.MatchedTitleResult;
import au.com.peterpal.selecting.standingorders.titles.entity.*;
import au.com.peterpal.selecting.standingorders.titles.events.TitleCreated;
import au.com.peterpal.selecting.standingorders.titles.events.TitleUpdated;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;

@Log4j2
@Service
@RequiredArgsConstructor
@Transactional
public class MatchAndMergeTitleService {

  private final StandingOrderRepository standingOrderRepository;
  private final TitleRepository titleRepository;
  private final TitleBL titleBL;
  private final TermRepository termRepository;
  private final CategoryService categoryService;
  private final ProductFormatService productFormatService;
  private final AllocationService allocationService;
  private final EventPublisher eventPublisher;
  private final TitleNumberGenerator titleNumberGenerator;

  @Value("${matcher.defer-pub-date.after-months-ahead:3}")
  Integer deferPubDateMonthAfter;

  LocalDate deferTitleStartDate = LocalDate.now();

  public void handle(MatchFound event) {

    try {
      Title title = match(event);
      title = merge(title);
      log.trace(
          String.format(
              "match found handled, product: %s to title:%s",
              event.getProductId(), title.getTitleId()));

    } catch (BusinessException e) {
      log.warn(e);
    }
  }

  public Title match(MatchFound event) throws BusinessException {
    MatchedTitleResult matchedTitleResult = findMatchedTitle(event);
    Title title = matchedTitleResult.getTitle();
    Optional<MatchedProduct> existingMatchedProduct =
        title.findActiveMatchedProductByIsbn(event.getProductReference());

    Term term =
        termRepository
            .findById(event.getMatchedTermId())
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        String.format("Term not found for id %s", event.getMatchedTermId())));

    MatchedProduct matchedProduct;
    if (existingMatchedProduct.isPresent()) {
      matchedProduct = existingMatchedProduct.get();

      Optional<MatchedStandingOrder> existingMatchedStandingOrder =
          matchedProduct.findMatchedStandingOrderByStandingOrderId(
              StandingOrderId.of(event.getSoId()));

      if (existingMatchedStandingOrder.isEmpty()) {
        // add standing order to title
        matchedProduct.addMatchedStandingOrder(
            MatchedStandingOrder.builder()
                .matchedStandingOrderId(new MatchedStandingOrderId())
                .status(MatchedStandingOrderStatus.ACTIVE)
                .standingOrder(standingOrderRepository.getOne(StandingOrderId.of(event.getSoId())))
                .matchedProduct(matchedProduct)
                .termId(term.getTermId())
                .matchedTermTuples(MatchedTermTuple.from(term))
                .build());
      }
    } else {
      matchedProduct = buildMatchedProduct(event, title).build();
      matchedProduct.addMatchedStandingOrder(
          MatchedStandingOrder.builder()
              .matchedStandingOrderId(new MatchedStandingOrderId())
              .status(MatchedStandingOrderStatus.ACTIVE)
              .standingOrder(standingOrderRepository.getOne(StandingOrderId.of(event.getSoId())))
              .matchedProduct(matchedProduct)
              .termId(term.getTermId())
              .matchedTermTuples(MatchedTermTuple.from(term))
              .build());

      title.addMatchedProduct(matchedProduct);
    }

    title.setDateModified(LocalDateTime.now());
    if (matchedTitleResult.isCreated()) {
      eventPublisher.publishEvent(TitleCreated.from(title, event));
    } else {
      eventPublisher.publishEvent(TitleUpdated.from(title, event));
    }
    return titleRepository.save(title);
  }

  public Title merge(TitleId titleId) {
    return merge(
        titleRepository
            .findById(titleId)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        String.format("Title not found for id %s", titleId))));
  }

  public Title merge(Title title) {
    aggregateProducts(title);
    aggregateStandingOrders(title);
    handleCategoryUpdate(title);
    handleTitleState(title);

    title.setDateModified(LocalDateTime.now());
    return titleRepository.save(title);
  }

  void handleTitleState(Title title) {
    switch (title.getTitleStatus()) {
      case PAUSED:
        title.setTitleStatus(TitleStatus.NEW);
        break;
      case NEW:
      case DEFERRED:
        if (title.isNew()
            && allocationService.isAllAllocationPaused(title.getStandingOrderAggregatedIds())) {
          title.setTitleStatus(TitleStatus.PAUSED);
        } else {
          handleDeferringTitle(title);
        }
        break;
      case PENDING:
      case PROCESSED:
      default:
        break;
    }
  }

  void handleDeferringTitle(Title title) {
    title
        .getMatchedProductWithEarliestPubDate()
        .ifPresent(
            product -> {
              LocalDate deferDateLimit = deferTitleStartDate.plusMonths(deferPubDateMonthAfter);
              if (product.shouldBeDeferred(deferDateLimit)) {
                if (!title.isTitlePendingOrProcessed()) {
                  titleBL.deferTitle(
                      title.getTitleId(),
                      product.calculateDeferredDate(deferPubDateMonthAfter),
                      false,
                      "system");
                }
              } else {
                if (title.isDeferred() && !title.isManuallyDeferred()) {
                  titleBL.unDeferTitle(title.getTitleId(), "system");
                }
              }
            });
  }

  void handleCategoryUpdate(Title title) {
    Category category = null;
    if (isNull(title.getCategory())) {
      // merge category
      for (MatchedProduct matchedProduct : title.getActiveMatchProducts()) {
        Category categoryTemp = matchedProduct.getCategory();
        if (category == null
            || (categoryTemp != null
                && StringUtils.containsIgnoreCase(categoryTemp.getCode(), category.getCode()))) {
          category = categoryTemp;
        }
      }
      if (category != null) {
        eventPublisher.publishEvent(CategoryAssigned.of(null, category.getCode(), "system"));
        title.setCategory(category);
      }
    }
  }

  void aggregateProducts(Title title) {
    for (MatchedProduct matchedProduct : title.getActiveMatchProducts()) {
      Optional<ProductAggregated> productAggregated =
          title.getProductAggregatedList().stream()
              .filter(
                  p ->
                      p.getMatchedProduct()
                          .getMatchedProductId()
                          .equals(matchedProduct.getMatchedProductId()))
              .findFirst();

      if (productAggregated.isEmpty()) {
        title.addProductAggregated(
            ProductAggregated.builder()
                .productAggregatedId(new ProductAggregatedId())
                .title(title)
                .matchedProduct(matchedProduct)
                .status(null)
                .build());
      }
    }
  }

  void aggregateStandingOrders(Title title) {
    Map<StandingOrder, Set<MatchedTermTuple>> standingOrderTermMap =
        title.getActiveMatchProducts().stream()
            .flatMap(matchedProduct -> matchedProduct.getActiveMatchedStandingOrder().stream())
            .distinct()
            .collect(
                Collectors.toMap(
                    MatchedStandingOrder::getStandingOrder,
                    MatchedStandingOrder::getMatchedTermTuples,
                    (term1, term2) -> term1));

    standingOrderTermMap
        .entrySet()
        .forEach(
            entry -> {
              Optional<StandingOrderAggregated> standingOrderAggregated =
                  title.getStandingOrderAggregatedList().stream()
                      .filter(
                          so ->
                              so.getStandingOrder()
                                  .getStandingOrderId()
                                  .equals(entry.getKey().getStandingOrderId()))
                      .findAny();
              if (standingOrderAggregated.isEmpty()) {
                title.addStandingOrderAggregated(
                    StandingOrderAggregated.builder()
                        .standingOrderAggregatedId(new StandingOrderAggregatedId())
                        .title(title)
                        .standingOrder(entry.getKey())
                        .matchedTermTuples(entry.getValue())
                        .status(null)
                        .build());
              }
            });
  }

  MatchedTitleResult findMatchedTitle(MatchFound event) {
    // TODO match title process will be enhanced on SELSW-356

    List<Title> titleContainingIsbn =
        titleRepository.findTitleContainingIsbn(event.getProductReference(), TitleType.ORIGINAL, MatchedProductStatus.ACTIVE);

    Optional<Title> existingTitle;
    if (titleContainingIsbn.stream().anyMatch(t -> t.isProcessed() || t.isRejected())) {
      throw new BusinessException(
          String.format(
              "ISBN %s already processed/ rejected on title", event.getProductReference()));
    } else {
      existingTitle =
          titleContainingIsbn.stream().filter(Title::isNotProcessedOrRejected).findAny();
    }

    if (existingTitle.isEmpty()) {
      existingTitle =
          titleRepository.findByTitleAndPersonNameAndSubtitleAndTypeAndTitleStatusIn(
              event.getTitle(),
              event.getMatchedPersonName(),
              event.getSubtitle(),
              TitleType.ORIGINAL,
              Lists.newArrayList(
                  TitleStatus.NEW, TitleStatus.PAUSED, TitleStatus.PENDING, TitleStatus.DEFERRED));
    }

    Title title;
    if (existingTitle.isPresent()) {
      title = existingTitle.get();
    } else {
      title =
          Title.builder()
              .titleId(TitleId.of(UUID.randomUUID()))
              .titleStatus(TitleStatus.NEW)
              .type(TitleType.ORIGINAL)
              .title(event.getTitle())
              .subtitle(event.getSubtitle())
              .personName(event.getMatchedPersonName())
              .series(event.getSeries())
              .imprint(event.getImprint())
              .dateAdded(LocalDateTime.now())
              .dateModified(LocalDateTime.now())
              .titleNumber(titleNumberGenerator.getNextTitleNumber())
              .build();
      return MatchedTitleResult.of(title, true);
    }
    return MatchedTitleResult.of(title, false);
  }

  MatchedProduct.MatchedProductBuilder buildMatchedProduct(MatchFound event, Title title) {
    List<String> subjectCodes = new ArrayList<>();
    appendIfNotNull(subjectCodes, event.getSubjectCode1());
    appendIfNotNull(subjectCodes, event.getSubjectCode2());
    appendIfNotNull(subjectCodes, event.getSubjectCode3());
    appendIfNotNull(subjectCodes, event.getSubjectCode4());
    appendIfNotNull(subjectCodes, event.getSubjectCode5());
    return MatchedProduct.builder()
        .matchedProductId(new MatchedProductId())
        .status(MatchedProductStatus.ACTIVE)
        .title(title)
        .category(categoryService.categoryFromSubject(subjectCodes))
        .format(productFormatService.map(event.getFormCode()))
        .catalogueId(event.getProductId())
        .productTitle(StringUtils.trim(event.getTitle()))
        .subtitle(StringUtils.trim(event.getSubtitle()))
        .series(StringUtils.trim(event.getSeries()))
        .isbn(event.getProductReference())
        .personName(StringUtils.trim(event.getMatchedPersonName()))
        .imprint(StringUtils.trim(event.getImprint()))
        .publicationDate(event.getPublicationDate());
  }

  private static void appendIfNotNull(List<String> subjectCodes, String subjectCode) {
    if (subjectCode != null) {
      subjectCodes.add(subjectCode);
    }
  }
}

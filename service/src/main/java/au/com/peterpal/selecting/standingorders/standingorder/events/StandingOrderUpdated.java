package au.com.peterpal.selecting.standingorders.standingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.Term;
import java.util.List;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class StandingOrderUpdated extends DomainEvent {

  private String description;

  private String notes;

  private String status;

  private String standingOrderNumber;

  private List<Term> terms;

  public static StandingOrderUpdated from(StandingOrder so, String username) {
    return StandingOrderUpdated.builder()
        .id(so.getStandingOrderId().getId())
        .standingOrderNumber(so.getStandingOrderNumber())
        .description(so.getDescription())
        .status(so.getStandingOrderStatus().name())
        .notes(so.getNotes())
        .username(username)
        .terms(so.getTerms())
        .build();
  }

  public static StandingOrderUpdated from(StandingOrderCreated so) {
    return StandingOrderUpdated.builder()
        .id(so.getId())
        .standingOrderNumber(so.getStandingOrderNumber())
        .description(so.getDescription())
        .status("ACTIVE")
        .notes(so.getNotes())
        .terms(so.getTerms())
        .build();
  }
}

package au.com.peterpal.selecting.standingorders.standingorder.model;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.UUID;
import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;

@Embeddable
public class CombinedTermId extends UuidEntityId {

  public static CombinedTermId of(@NotEmpty UUID id) {
    return new CombinedTermId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public CombinedTermId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public CombinedTermId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public CombinedTermId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }

}

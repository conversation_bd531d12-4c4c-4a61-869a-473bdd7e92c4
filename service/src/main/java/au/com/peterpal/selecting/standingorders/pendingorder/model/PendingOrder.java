package au.com.peterpal.selecting.standingorders.pendingorder.model;

import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.ext.order.dto.DuplicateOrder;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.Supplier;
import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import au.com.peterpal.selecting.standingorders.utils.CurrencyCode;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "pending_order")
public class PendingOrder {

  @EqualsAndHashCode.Include @NonNull @EmbeddedId private PendingOrderId pendingOrderId;

  @Enumerated(EnumType.STRING)
  private PendingOrderStatus orderStatus;

  private String customerReference;

  private String orderedProductReference;

  @ManyToOne
  private Category category;

  private String deliveryInstructions;

  private String notes;

  private String orderNumber;

  @NotNull
  @NonNull
  @ManyToOne
  @JoinColumn(name = "customerId", nullable = false)
  private Customer customer;

  @ManyToOne
  @JoinColumn(name = "supplierId", nullable = false)
  private Supplier supplier;

  @ManyToOne
  @JoinColumn(name = "fundId")
  private Fund fund;

  @ManyToOne
  @JoinColumn(name = "titleId", nullable = false)
  private Title title;

  @NotNull
  @NonNull
  @ManyToOne
  @JoinColumn(name = "releaseId")
  private Release release;

  private Integer quantity;

  @Enumerated(EnumType.STRING)
  private ReleaseFormat format;

  private String collectionCode;

  private BigDecimal price;

  private LocalDate publicationDate;;

  @Enumerated(EnumType.STRING)
  private CurrencyCode currencyCode;

  @Transient
  private int orderCount;

  @Transient
  private DuplicateOrder duplicateOrder;

  private LocalDate submittedDate;

  private String submittedBy;

  @Builder.Default
  @Type(type = "json")
  @Column(columnDefinition = "json")
  private Set<BranchDistribution> branchDistributions = new HashSet<>();

  public boolean isNew() {
    return orderStatus == PendingOrderStatus.NEW;
  }
  public boolean isProcessed() {
    return orderStatus == PendingOrderStatus.PROCESSED;
  }

  public boolean isProcessedInvalidCancelled() {
    return Lists.newArrayList(
            PendingOrderStatus.PROCESSED, PendingOrderStatus.INVALID, PendingOrderStatus.CANCELLED)
        .contains(orderStatus);
  }

  public boolean isSubmitted() {
    return orderStatus == PendingOrderStatus.SUBMITTED;
  }
  public boolean isSubmittedOrProcessed() {
    return orderStatus == PendingOrderStatus.SUBMITTED || orderStatus == PendingOrderStatus.PROCESSED;
  }

  public boolean isCancelled() {
    return orderStatus == PendingOrderStatus.CANCELLED;
  }
  public boolean isInvalid() {
    return orderStatus == PendingOrderStatus.INVALID;
  }

  public boolean isCancelledOrInvalid() {
    return orderStatus == PendingOrderStatus.CANCELLED || orderStatus == PendingOrderStatus.INVALID;
  }

  public boolean hasBranchAllocation() {
    return CollectionUtils.isNotEmpty(branchDistributions);
  }

  public boolean hasSupplier() {
    return Objects.nonNull(supplier);
  }

  @JsonIgnore
  public String getPublicationDateFormatted() {
    return Optional.ofNullable(getPublicationDate())
        .map(pd -> pd.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
        .orElse(null);
  }
  @JsonIgnore
  public Integer getPublicationYear() {
    return Optional.ofNullable(publicationDate).map(LocalDate::getYear).orElse(null);
  }
}

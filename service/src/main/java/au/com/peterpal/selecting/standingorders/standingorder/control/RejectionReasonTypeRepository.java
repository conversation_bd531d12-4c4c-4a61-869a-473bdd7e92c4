package au.com.peterpal.selecting.standingorders.standingorder.control;

import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonStatus;
import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonType;
import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonTypeId;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RejectionReasonTypeRepository
    extends JpaRepository<RejectionReasonType, RejectionReasonTypeId> {

  List<RejectionReasonType> findAllByStatusAndDisplayable(RejectionReasonStatus status, Boolean displayable, Sort sort);
}

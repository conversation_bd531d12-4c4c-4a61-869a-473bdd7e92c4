package au.com.peterpal.selecting.standingorders.allocation.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import java.util.List;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@ToString
public class BulkUpdateAllocationRequest {
  SearchAllocationRequest searchRequest;
  List<AllocationId> includedAllocationIds;
  List<AllocationId> excludedAllocationIds;
  List<String> categories;
  String notes;
  String collectionCode;
  FundId fundId;
  String customerReference;
  String deliveryInstructions;
  Integer hbQty;
  Integer pbQty;
  String appendNotes;
  String appendDeliveryInstructions;
  @Builder.Default
  Boolean rematchAllocationAfterUpdate = false;
  AllocationStatus status;
}

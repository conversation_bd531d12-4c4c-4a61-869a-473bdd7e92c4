package au.com.peterpal.selecting.standingorders.catalog.model;

/**
 * @docRoot
 * Based on Onix List 65
 */
public enum ProductAvailabilityCode {

	AV ("20", "Available", "Available from us (form of availability unspecified)"),
	IS ("21", "In stock", "Available from us as a stock item"),
	NYA ("10", "Not yet available", "Not yet available (requires <ExpectedShipDate>, except in exceptional circumstances where no date is known)"),
	AWS ("11", "Awaiting stock", "Not yet available, but will be a stock item when available (requires <ExpectedShipDate>, except in exceptional circumstances where no date is known). Used particularly for imports which have been published in the country of origin but have not yet arrived in the importing country."),
	NA ("40", "Not available", "Not available from us (reason unspecified; if the reason is rights-related, it should be specified in PR.21)"),
	NS ("43", "No longer supplied by us", "Identify new supplier in <NewSupplier> if possible"),
	OPPOD ("48", "Out of print, replaced by POD", "Out of print, but a print-on-demand edition is or will be available under a different ISBN. Use only when the POD successor has a different ISBN, normally because different trade terms apply."),
	CAN ("01", "Cancelled", "Cancelled: product was announced, and subsequently abandoned"),
	POD ("12", "Not yet available, will be POD", "Not yet available, to be published as print-on-demand only. May apply either to a POD successor to an existing conventional edition, when the successor will be published under a different ISBN (normally because different trade terms apply); or to a title that is being published as a POD original."),
	TO ("22", "To order", "Available from us as a non-stock item, by special order"),
	MOD ("23", "Manufactured on demand", "Available from us by manufacture on demand"),
	TUN ("30", "Temporarily unavailable", "Temporarily unavailable: temporarily unavailable from us (reason unspecified) (requires <ExpectedShipDate>, except in exceptional circumstances where no date is known)"),
	OS ("31", "Out of stock", "Stock item, temporarily out of stock (requires <ExpectedShipDate>, except in exceptional circumstances where no date is known)"),
	RP ("32", "Reprinting", "Temporarily unavailable, reprinting (requires <ExpectedShipDate>, except in exceptional circumstances where no date is known)"),
	RI ("33", "Awaiting reissue", "Temporarily unavailable, awaiting reissue (requires the <Reissue> composite, and <ExpectedShipDate>, except in exceptional circumstances where no date is known)"),
	REPL ("41", "Replaced by new product", "This product is unavailable, but a successor product or edition is or will be available from us (identify successor in <RelatedProduct>)"),
	OTH ("42", "Other format available", "This product is unavailable, but the same content is or will be available from us in an alternative format (identify other format product in <RelatedProduct>)"),
	AD ("44", "Apply direct", "Not available to trade, apply direct to publisher"),
	NSS ("45", "Not sold separately", "Must be bought as part of a set (identify set in <RelatedProduct>)"),
	WD ("46", "Withdrawn from sale", "May be for legal reasons or to avoid giving offence"),
	REM ("47", "Remaindered", "Remaindered"),
	NRUR ("97", "No recent update received", "Sender has not received any recent update for this product from the publisher/supplier"),
	NLRU ("98", "No longer receiving updates", "Sender is no longer receiving any updates from the publisher/supplier of this product"),
	UNK ("99", "Uncertain", "Apply to customer service");

	private final String code;
	private final String description;
	private final String notes;

	ProductAvailabilityCode(String code, String description, String notes) {
		this.code = code;
		this.description = description;
		this.notes = notes;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public String getNotes() {
		return notes;
	}

	public static ProductAvailabilityCode mapOnixCode(String onixCode) {
		for (ProductAvailabilityCode value : ProductAvailabilityCode.values()) {
			if (value.code.equals(onixCode)) {
				return value;
			}
		}
		throw new IllegalArgumentException("Invalid " + ProductAvailabilityCode.class.getSimpleName() + ": " + onixCode);
	}

	@Override
	public String toString() {
		return this.description;
	}
}

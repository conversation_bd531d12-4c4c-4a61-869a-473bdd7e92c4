package au.com.peterpal.selecting.standingorders.allocationpreference.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreference;

import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@ToString
public class ReleasePreferenceUpdated extends DomainEvent {

  private ReleasePreference preference;

  public static ReleasePreferenceUpdated from(UUID id, ReleasePreference preference, String username) {

    return ReleasePreferenceUpdated.builder()
        .id(id)
        .preference(preference)
        .username(username)
        .build();
  }
}

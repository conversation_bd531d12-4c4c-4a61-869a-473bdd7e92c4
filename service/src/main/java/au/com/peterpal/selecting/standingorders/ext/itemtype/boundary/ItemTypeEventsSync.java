package au.com.peterpal.selecting.standingorders.ext.itemtype.boundary;

import au.com.peterpal.selecting.standingorders.ext.itemtype.boundary.dto.ItemTypeMessage;
import au.com.peterpal.selecting.standingorders.ext.itemtype.control.ItemTypeSyncService;
import java.util.UUID;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.context.IntegrationContextUtils;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
//import org.springframework.integration.dsl.MessageChannels;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.dsl.channel.MessageChannels;
import org.springframework.integration.jms.dsl.Jms;
import org.springframework.messaging.MessageChannel;

import javax.jms.ConnectionFactory;
import org.springframework.util.backoff.BackOff;

@Log4j2
@Configuration
@EnableIntegration
public class ItemTypeEventsSync {
  private static final String CLIENT_ID = "standing-orders:item-type";
  private final ConnectionFactory connectionFactory;
  private final ItemTypeSyncService syncService;

  @Value("${events.item-type.channel:item-type-events-topic}")
  private String itemTypeEventsChannelName;

  @Value("${events.item-type.channel.subscription:item-type-events}")
  private String itemTypeEventsSubscriptionName;

  public ItemTypeEventsSync(ConnectionFactory connectionFactory, ItemTypeSyncService syncService) {
    this.connectionFactory = connectionFactory;
    this.syncService = syncService;
  }

  @Bean
  public MessageChannel itemTypeProcessChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow itemTypeEventsListener() {
    return IntegrationFlows.from(
            Jms.messageDrivenChannelAdapter(connectionFactory)
                .destination(itemTypeEventsChannelName)
                .configureListenerContainer(
                    spec -> {
                      spec.pubSubDomain(true);
                      spec.durableSubscriptionName(itemTypeEventsSubscriptionName);
                      spec.recoveryInterval(60000L);
                      spec.clientId(CLIENT_ID);
                    })
                .errorChannel(IntegrationContextUtils.ERROR_CHANNEL_BEAN_NAME))
        .channel(itemTypeProcessChannel())
        .get();
  }

  @Bean
  public IntegrationFlow itemTypeProcessingFlow() {
    return IntegrationFlows.from(itemTypeProcessChannel())
        .log(message -> "Item type event received: " + message)
        .transform(Transformers.fromJson(ItemTypeMessage.class))
        .log(msg -> "Processing message: " + msg)
        .<ItemTypeMessage>handle((msg, headers) -> this.syncService.handle(msg))
        .log(message -> "Item type synchronization completed")
        .get();
  }
}

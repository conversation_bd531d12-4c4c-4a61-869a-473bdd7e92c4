package au.com.peterpal.selecting.standingorders.standingorder.dto;

import au.com.peterpal.selecting.standingorders.standingorder.commands.CreateStandingOrderCmd;
import lombok.*;

import java.util.List;

@With
@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class CreateStandingOrderRequest {

  private String description;

  private String notes;

  private List<TermsRequest> terms;


  public CreateStandingOrderCmd toCmd(String username) {
    return CreateStandingOrderCmd.builder()
        .username(username == null ? "" : username)
        .description(description)
        .notes(notes)
        .terms(TermsRequest.from(terms))
        .build();
  }
}

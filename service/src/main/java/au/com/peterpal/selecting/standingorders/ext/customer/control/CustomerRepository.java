package au.com.peterpal.selecting.standingorders.ext.customer.control;

import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CustomerRepository extends JpaRepository<Customer, CustomerId> {
  Optional<Customer> findByCode(String code);

  List<Customer> findAllByCode(String code);
}

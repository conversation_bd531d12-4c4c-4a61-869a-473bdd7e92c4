package au.com.peterpal.selecting.standingorders.products.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.vladmihalcea.hibernate.type.json.JsonType;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.With;
import org.hibernate.annotations.TypeDef;

@With
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "product")
@TypeDef(name = "json", typeClass = JsonType.class)
public class Product implements Serializable {

  @EqualsAndHashCode.Include
  @NonNull
  @EmbeddedId
  private ProductId productId;

  @NotNull private String productReference;

  private LocalDateTime lastImportDate;

  public static Product from(String productReference) {
    return Product.builder().productId(new ProductId())
        .lastImportDate(LocalDateTime.now())
        .productReference(productReference).build();
  }
}

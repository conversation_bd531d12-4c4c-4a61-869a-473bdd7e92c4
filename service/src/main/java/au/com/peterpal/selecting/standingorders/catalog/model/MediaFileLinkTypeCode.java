package au.com.peterpal.selecting.standingorders.catalog.model;

/**
 * @docRoot
 * Based on Onix List 40
 */
public enum MediaFileLinkTypeCode {

	URL ("01", "URL", ""),
	DOI ("02", "DOI", ""),
	PURL ("03", "PURL", ""),
	URN ("04", "URN", ""),
	FTP ("05", "FTP address", ""),
	FILE ("06", "filename", "");

	private final String code;
	private final String description;
	private final String notes;

	MediaFileLinkTypeCode(String code, String description, String notes) {
		this.code = code;
		this.description = description;
		this.notes = notes;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public String getNotes() {
		return notes;
	}

	public static MediaFileLinkTypeCode mapOnixCode(String onixCode) {
		for (MediaFileLinkTypeCode value : MediaFileLinkTypeCode.values()) {
			if (value.code.equals(onixCode)) {
				return value;
			}
		}
		throw new IllegalArgumentException("Invalid " + MediaFileLinkTypeCode.class.getSimpleName() + ": " + onixCode);
	}

	@Override
	public String toString() {
		return this.description;
	}
}

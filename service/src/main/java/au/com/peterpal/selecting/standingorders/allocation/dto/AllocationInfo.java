package au.com.peterpal.selecting.standingorders.allocation.dto;

import au.com.peterpal.selecting.standingorders.allocation.commands.AllocationSearchResponse;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.branch.control.dto.BranchInfo;
import au.com.peterpal.selecting.standingorders.ext.customer.dto.CustomerInfo;
import au.com.peterpal.selecting.standingorders.ext.fund.dto.FundInfo;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import com.google.common.collect.Lists;
import lombok.*;
import lombok.experimental.NonFinal;

import javax.validation.constraints.NotNull;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class AllocationInfo {

  @NotNull @NonNull AllocationId allocationId;

  @NotNull @NonNull CustomerStandingOrderId customerStandingOrderId;

  @NotNull @NonNull CustomerInfo customer;

  BranchInfo branch;

  FundInfo fund;

  AllocationPreferenceId preferenceId;

  AllocationStatus status;

  List<String> categories;
  String customerReference;
  String deliveryInstructions;
  String notes;

  List<ReleaseInfo> releases;

  String standingOrderNumber;
  String standingOrderDescription;
  StandingOrderId standingOrderId;
  String collectionCode;

  AllocationId baParentId;

  @NonFinal
  @Setter
  boolean branchDistributed;

  public static AllocationInfo from(Allocation allocation) {
    return Optional.ofNullable(allocation)
        .map(a -> {
            Optional<StandingOrder> standingOrder =
                Optional.ofNullable(a.getCustomerStandingOrder())
                    .map(CustomerStandingOrder::getStandingOrder);
            String standingOrderNumber =
                standingOrder.map(StandingOrder::getStandingOrderNumber).orElse(null);
            String description = standingOrder.map(StandingOrder::getDescription).orElse(null);
            StandingOrderId stOrdId =
                standingOrder.map(StandingOrder::getStandingOrderId).orElse(null);

            return AllocationInfo.builder()
                .allocationId(a.getAllocationId())
                .customerStandingOrderId(
                    a.getCustomerStandingOrder().getCustomerStandingOrderId())
                .customer(
                    Optional.of(a.getCustomerStandingOrder().getCustomer())
                        .map(c -> CustomerInfo.of(c.getCustomerId(), c.getCode(), c.getName()))
                        .orElse(null))
                .fund(
                    Optional.ofNullable(a.getFund())
                        .map(f -> FundInfo.of(f.getFundId(), f.getCode(), f.getName(), f.getType()))
                        .orElse(null))
                .branch(
                    Optional.ofNullable(a.getBranch())
                        .map(b -> BranchInfo.builder()
                            .customerId(
                                a.getCustomerStandingOrder()
                                    .getCustomer()
                                    .getCustomerId())
                            .status(b.getStatus())
                            .branchId(b.getBranchId())
                            .code(b.getCode())
                            .name(b.getName())
                            .build())
                        .orElse(null))
                .status(a.getStatus())
                .categories(a.getCategoryCodes())
                .customerReference(a.getCustomerReference())
                .deliveryInstructions(a.getDeliveryInstructions())
                .notes(a.getNotes())
                .baParentId(Optional.ofNullable(a.getBaParent()).map(p -> p.getAllocationId()).orElse(null))
                .releases(Optional.of(a.getReleases())
                    .map(l ->l.stream()
                        .filter(r -> r.getReleaseType().equals(ReleaseType.INITIAL))
                        .map(r -> ReleaseInfo.builder()
                            .releaseId(r.getReleaseId())
                            .allocationId(r.getAllocation().getAllocationId())
                            .preferenceId(
                                Optional.ofNullable(r.getPreference())
                                    .map(
                                        ReleasePreference
                                            ::getReleasePreferenceId)
                                    .orElse(null))
                            .releaseType(r.getReleaseType())
                            .actionType(r.getActionType())
                            .initialAssignmentRule(
                                r.getInitialAssignmentRule())
                            .smallFormatPaperbackRule(
                                r.getSmallFormatPaperbackRule())
                            .fund(
                                Optional.ofNullable(r.getFund())
                                    .map(FundInfo::from)
                                    .orElse(null))
                            .hardbackfund(
                                Optional.ofNullable(r.getHardbackfund())
                                    .map(FundInfo::from)
                                    .orElse(null))
                            .paperbackfund(
                                Optional.ofNullable(r.getPaperbackfund())
                                    .map(FundInfo::from)
                                    .orElse(null))
                            .quantity(r.getQuantity())
                            .hardbackQuantity(r.getHardbackQuantity())
                            .paperbackQuantity(r.getPaperbackQuantity())
                            .build())
                        .collect(Collectors.toList()))
                    .orElse(new ArrayList<>()))
                .standingOrderNumber(standingOrderNumber)
                .standingOrderDescription(description)
                .standingOrderId(stOrdId)
                .collectionCode(allocation.getCollectionCode())
                .build();
            })
        .orElse(null);
  }

  public static AllocationInfo from(AllocationSearchResponse allocation) {
    return Optional.ofNullable(allocation)
        .map(a -> {

          String standingOrderNumber = a.getStandingOrderNumber();
          String description = a.getStandingOrderDescription();
          StandingOrderId stOrdId = a.getStandingOrderId();

          return AllocationInfo.builder()
              .allocationId(a.getAllocationId())
              .customerStandingOrderId(
                  a.getCustomerStandingOrder().getCustomerStandingOrderId())
              .customer(
                  Optional.of(a.getCustomerStandingOrder().getCustomer())
                      .map(c -> CustomerInfo.of(c.getCustomerId(), c.getCode(), c.getName()))
                      .orElse(null))
              .fund(
                  Optional.ofNullable(a.getFundId())
                      .map(f -> FundInfo.of(f, a.getFundCode(), a.getFundName(), null))
                      .orElse(null))
              .branch(
                  Optional.ofNullable(a.getBranchId())
                      .map(b -> BranchInfo.builder()
                          .customerId(
                              a.getCustomerId())
                          .status(a.getBranchStatus())
                          .branchId(b)
                          .code(a.getBranchCode())
                          .name(a.getBranchName())
                          .build())
                      .orElse(null))
              .status(a.getStatus())
              .categories(a.getCategories())
              .customerReference(a.getCustomerReference())
              .deliveryInstructions(a.getDeliveryInstructions())
              .notes(a.getNotes())
              .baParentId(a.getBaParentId())
              .releases(Optional.ofNullable(a.getInitialReleases())
                  .map(r -> Lists.newArrayList(ReleaseInfo.builder()
                      .releaseId(r.getReleaseId())
                      .allocationId(a.getAllocationId())
                      .preferenceId(
                          Optional.ofNullable(r.getPreference())
                              .map(
                                  ReleasePreference
                                      ::getReleasePreferenceId)
                              .orElse(null))
                      .releaseType(r.getReleaseType())
                      .actionType(r.getActionType())
                      .initialAssignmentRule(
                          r.getInitialAssignmentRule())
                      .smallFormatPaperbackRule(
                          r.getSmallFormatPaperbackRule())
                      .fund(
                          Optional.ofNullable(r.getFund())
                              .map(FundInfo::from)
                              .orElse(null))
                      .hardbackfund(
                          Optional.ofNullable(r.getHardbackfund())
                              .map(FundInfo::from)
                              .orElse(null))
                      .paperbackfund(
                          Optional.ofNullable(r.getPaperbackfund())
                              .map(FundInfo::from)
                              .orElse(null))
                      .quantity(r.getQuantity())
                      .hardbackQuantity(r.getHardbackQuantity())
                      .paperbackQuantity(r.getPaperbackQuantity())
                      .build())).orElse(null)
              )
              .standingOrderNumber(standingOrderNumber)
              .standingOrderDescription(description)
              .standingOrderId(stOrdId)
              .collectionCode(allocation.getCollectionCode())
              .build();
        })
        .orElse(null);
  }

  public static List<AllocationInfo> from(List<Allocation> allocationList) {
    return Optional.ofNullable(allocationList).stream()
        .flatMap(Collection::stream)
        .map(AllocationInfo::from)
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }
}

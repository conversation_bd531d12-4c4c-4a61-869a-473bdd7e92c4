package au.com.peterpal.selecting.standingorders.products.control;

import au.com.peterpal.selecting.standingorders.products.entity.Product;
import au.com.peterpal.selecting.standingorders.products.entity.ProductId;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ProductRepository extends JpaRepository<Product, ProductId> {
    Optional<Product> findByProductReference(String productReference);
    boolean existsByProductReference(String productReference);

    List<Product> findAllByLastImportDateBetween(LocalDateTime startDate, LocalDateTime endDate);
}

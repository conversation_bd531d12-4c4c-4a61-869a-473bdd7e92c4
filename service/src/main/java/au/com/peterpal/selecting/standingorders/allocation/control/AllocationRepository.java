package au.com.peterpal.selecting.standingorders.allocation.control;

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AllocationRepository
    extends JpaRepository<Allocation, AllocationId>, AllocationRepositoryCustom {
  Allocation findByAllocationPreference_AllocationPreferenceId(
      AllocationPreferenceId allocationPreferenceId);

  Allocation
      findByAllocationPreference_AllocationPreferenceIdAndCustomerStandingOrder_CustomerStandingOrderId(
          AllocationPreferenceId allocationPreferenceId,
          CustomerStandingOrderId customerStandingOrderId);

  List<Allocation> findAllByBaParentAllocationIdAndStatus(AllocationId allocationId, AllocationStatus status);
  List<Allocation> findAllByBaParentAllocationId(AllocationId allocationId);
}

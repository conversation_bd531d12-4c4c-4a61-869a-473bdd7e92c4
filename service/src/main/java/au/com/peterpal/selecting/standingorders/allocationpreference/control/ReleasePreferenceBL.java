package au.com.peterpal.selecting.standingorders.allocationpreference.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocationpreference.commands.AddReleasePreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.commands.RemoveReleasePreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.ReleasePreferenceInfo;
import au.com.peterpal.selecting.standingorders.allocationpreference.events.ReleasePreferenceAdded;
import au.com.peterpal.selecting.standingorders.allocationpreference.events.ReleasePreferenceRemoved;
import au.com.peterpal.selecting.standingorders.allocationpreference.events.ReleasePreferenceUpdated;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.SmallFormatPaperbackRule;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundService;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import java.util.Optional;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Log4j2
@Component
public class ReleasePreferenceBL {

  private final ReleasePreferenceRepository releasePreferenceRepository;
  private final FundService fundService;
  private final AllocationPreferenceService allocationPreferenceService;
  private final EventPublisher eventPublisher;

  public ReleasePreferenceBL(
      ReleasePreferenceRepository releasePreferenceRepository,
      FundService fundService,
      AllocationPreferenceService allocationPreferenceService,
      EventPublisher eventPublisher) {

    this.releasePreferenceRepository = releasePreferenceRepository;
    this.fundService = fundService;
    this.allocationPreferenceService = allocationPreferenceService;
    this.eventPublisher = eventPublisher;
  }

  public ReleasePreferenceId handle(AddReleasePreference addReleasePreference, String username) {
    Affirm.of(addReleasePreference).notNull("AddReleasePreference must not be null");
    Affirm.of(addReleasePreference.getPreference()).notNull("preference must not be null");

    ReleasePreferenceInfo preference = addReleasePreference.getPreference();
    AllocationPreference allocPref =
        allocationPreferenceService.findById(addReleasePreference.getAllocationPreferenceId());

    String cc = allocPref.getCustomer().getCode();
    Fund fund = fundService.findByCodeAndCustomer(preference.getFundCode(), cc)
        .orElse(null);
    Fund hbFund = fundService.findByCodeAndCustomer(preference.getHbFundCode(), cc)
        .orElse(null);
    Fund pbFund = fundService.findByCodeAndCustomer(preference.getPbFundCode(), cc)
        .orElse(null);

    ReleasePreference relPref = ReleasePreference.from(allocPref, fund, hbFund, pbFund, preference);
    UUID id = relPref.getReleasePreferenceId().getId();

    eventPublisher.publishEvent(ReleasePreferenceAdded.of(id, relPref, username));
    return relPref.getReleasePreferenceId();
  }

  public ReleasePreferenceId handle(
      RemoveReleasePreference removeReleasePreference, String username) {
    Affirm.of(releasePreferenceRepository).notNull("RemoveReleasePreference must not be null");

    ReleasePreferenceId releasePreferenceId = removeReleasePreference.getReleasePreferenceId();
    ReleasePreference releasePreference =
        releasePreferenceRepository
            .findById(releasePreferenceId)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        ReleasePreference.class, String.valueOf(releasePreferenceId)));

    eventPublisher.publishEvent(ReleasePreferenceRemoved.from(releasePreference, username));
    return releasePreferenceId;
  }

  public ReleasePreferenceUpdated handle(
      ReleasePreferenceId releasePreferenceId, ReleasePreferenceInfo preference, String username) {

    Affirm.of(releasePreferenceId).notNull("Release preference must not be null");
    Affirm.of(preference).notNull("Release preference information must not be null");

    ReleasePreference relPref =
        releasePreferenceRepository
            .findById(releasePreferenceId)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(
                        ReleasePreference.class, String.valueOf(releasePreferenceId)));

    Customer customer = relPref.getAllocationPreference().getCustomer();
    ReleasePreferenceUpdated event = null;

    if (customer != null) {
      String cc = customer.getCode();
      Fund fund = fundService.findByCodeAndCustomer(preference.getFundCode(), cc)
          .orElse(null);
      Fund hbFund = fundService.findByCodeAndCustomer(preference.getHbFundCode(), cc)
          .orElse(null);
      Fund pbFund = fundService.findByCodeAndCustomer(preference.getPbFundCode(), cc)
          .orElse(null);

      relPref.setActionType(Optional.ofNullable(preference.getActionType())
          .map(s -> ActionType.valueOf(s))
          .orElse(null));
      relPref.setReleaseType(preference.getReleaseType());
      relPref.setInitialAssignmentRule(Optional.ofNullable(preference.getInitialAssignmentRule())
          .map(s -> AssignmentRule.valueOf(s))
          .orElse(null));
      relPref.setSmallFormatPaperbackRule(Optional.ofNullable(preference.getSmallFormatPaperbackRule())
          .map(s -> SmallFormatPaperbackRule.valueOf(s))
          .orElse(null));
      relPref.setFund(fund);
      relPref.setHardbackfund(hbFund);
      relPref.setPaperbackfund(pbFund);

      event = ReleasePreferenceUpdated.from(releasePreferenceId.getId(), relPref, username);
      eventPublisher.publishEvent(event);
    }
    return event;
  }

  @EventListener
  private void on(final ReleasePreferenceAdded releasePreferenceAdded) {
    log.debug(() -> String.format("Adding release preference %s", releasePreferenceAdded));
    releasePreferenceRepository.save(releasePreferenceAdded.getReleasePreference());
  }

  @EventListener
  private void on(final ReleasePreferenceRemoved releasePreferenceRemoved) {
    ReleasePreferenceId releasePreferenceId =
        ReleasePreferenceId.of(releasePreferenceRemoved.getId());
    log.debug(() -> String.format("Deleting release preference %s", releasePreferenceId));
    releasePreferenceRepository.deleteById(releasePreferenceId);
  }

  @EventListener
  private void on(final ReleasePreferenceUpdated releasePreferenceUpdated) {
    log.debug(() -> String.format("Updating release preference %s", releasePreferenceUpdated));
    releasePreferenceRepository.save(releasePreferenceUpdated.getPreference());
  }
}

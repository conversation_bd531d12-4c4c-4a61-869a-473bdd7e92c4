package au.com.peterpal.selecting.standingorders.titles.entity;

import javax.persistence.Entity;
import javax.persistence.Id;

import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import lombok.Data;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;
import org.hibernate.annotations.Synchronize;

@Entity
@Data
@Subselect(
    "select t.id as id, min(c.code) as allocation_category from standing_order t \n"
        + "inner join customer_standing_order cso on t.id= cso.standing_order_id \n"
        + "inner join allocation a on cso.id = a.customer_standing_order_id \n"
        + "inner join allocation_categories ac on a.id = ac.allocation_id \n"
        + "inner join category c on c.id = ac.categories_id \n"
        + "where a.status in ('ACTIVE', 'PAUSED')\n"
        + "group by t.id ")
@Synchronize({
  "standing_order",
  "customer_standing_order",
  "allocation",
  "allocation_categories",
  "category"
})
@Immutable
public class StandingOrderAllocationCategoryAgg {
  @Id private StandingOrderId standingOrderId;
  private String allocationCategory;
}

package au.com.peterpal.selecting.standingorders.allocationpreference.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.*;
import au.com.peterpal.selecting.standingorders.allocationpreference.events.AllocationPreferenceAdded;
import au.com.peterpal.selecting.standingorders.allocationpreference.events.AllocationPreferenceRemoved;
import au.com.peterpal.selecting.standingorders.allocationpreference.events.AllocationPreferenceUpdated;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceStatus;
import au.com.peterpal.selecting.standingorders.ext.category.control.CategoryRepository;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerService;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundService;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Log4j2
@RequiredArgsConstructor
@Component
public class AllocationPreferenceBL {

  private final FundService fundService;
  private final EventPublisher publisher;
  private final AllocationPreferenceService allocationPreferenceService;
  private final CustomerService customerService;
  private final CategoryRepository categoryRepository;

  public AllocationPreferenceId handle(AddAllocationPreference request, String username) {
    Affirm.of(request).notNull("AddAllocationPreference must not be null");
    Affirm.of(request.getCustomerId()).notNull("CustomerId must not be null");

    Customer customer = customerService.findById(CustomerId.of(request.getCustomerId()));
    Fund fund = Optional.ofNullable(request.getFundCode())
        .map(f -> fundService.findByCodeAndCustomer(f, customer.getCode()).orElse(null))
        .orElse(null);

    List<Category> categories = categoryRepository.findByCodeIn(request.getCategories());
    AllocationPreferenceAdded allocationPreferenceAdded =
        AllocationPreferenceAdded.from(request, fund, customer, categories, username);


    AllocationPreference allocationPreference = AllocationPreference.from(allocationPreferenceAdded);
    validateCustomerCategory(customer, categories, allocationPreference.getAllocationPreferenceId());

    allocationPreferenceService.save(allocationPreference);
    publisher.publishEvent(allocationPreferenceAdded);
    return AllocationPreferenceId.of(allocationPreferenceAdded.getId());
  }

  public AllocationPreferenceId handle(RemoveAllocationPreference request, String username) {
    Affirm.of(request).notNull("RemoveAllocationPreference must not be null");

    AllocationPreference allocationPreference =
        allocationPreferenceService.findById(request.getAllocationPreferenceId());

    allocationPreference.setStatus(AllocationPreferenceStatus.INACTIVE);

    allocationPreferenceService.save(allocationPreference);
    publisher.publishEvent(AllocationPreferenceRemoved.from(allocationPreference, username));
    return request.getAllocationPreferenceId();
  }

  public AllocationPreferenceId handle(
      UpdateAllocationPreference request,
      AllocationPreferenceId allocationPreferenceId,
      String username) {

    Affirm.of(request).notNull("UpdateAllocationPreference must not be null");

    AllocationPreference allocationPreference =
        allocationPreferenceService
            .findById(allocationPreferenceId);

    String cc = allocationPreference.getCustomer().getCode();
    Fund fund = Optional.ofNullable(request.getFundCode())
        .map(f -> fundService.findByCodeAndCustomer(request.getFundCode(), cc).orElse(null))
        .orElse(null);

    List<Category> categories = categoryRepository.findByCodeIn(request.getCategory());
    validateCustomerCategory(allocationPreference.getCustomer(), categories, allocationPreference.getAllocationPreferenceId());

    allocationPreference.setCategories(categories);
    allocationPreference.setCustomerReference(request.getCustomerReference());
    allocationPreference.setDeliveryInstructions(request.getDeliveryInstructions());
    allocationPreference.setNotes(request.getNotes());
    allocationPreference.setFund(fund);
    allocationPreference.setStatus(request.getAllocationPreferenceStatus());

    allocationPreferenceService.save(allocationPreference);
    publisher.publishEvent(AllocationPreferenceUpdated.from(allocationPreference, username));
    return allocationPreferenceId;
  }

  void validateCustomerCategory(Customer customer, List<Category> categories, AllocationPreferenceId currentAllocationId) {
    // category shouldn't be overlapped between preferences for a customer
    List<AllocationPreference> preferences =
        allocationPreferenceService
            .findPreferenceByCustomerAndCategories(customer, categories)
            .stream()
            .filter(a -> !a.getAllocationPreferenceId().equals(currentAllocationId))
            .collect(Collectors.toList());

    if (CollectionUtils.isNotEmpty(preferences)) {
      throw new BusinessException("Can not have overlapped category across multiple preferences");
    }
  }

  public Page<AllocationPreferenceResponse> handle(AllocationPreferenceRequest request, Pageable pageRequest) {
    Affirm.of(request).notNull("AllocationPreferenceRequest should not be null");

    customerService.findByCustomerCode(request.getCustomerCode());

    Page<AllocationPreference> resultSet =
        allocationPreferenceService.searchAllByCustomer(request.getCustomerCode(), pageRequest);

    return resultSet.map(AllocationPreferenceResponse::from);
  }

  public List<AllocationPreferenceResponse> handle(
      AllocationPreferenceSearchRequest searchRequest) {
    return allocationPreferenceService.search(searchRequest);
  }

  @Transactional
  public List<AllocationPreferenceId> bulkUpdate(
      BulkUpdateAllocationPreference request, String username) {
    List<AllocationPreference> allocationPreferences;
      allocationPreferences =
          allocationPreferenceService.findAllBy(request.getIncludedAllocationPreferenceIds());
    List<Category> categories = null;
    if (CollectionUtils.isNotEmpty(request.getUpdatedCategory())) {
      categories = categoryRepository.findByCodeIn(request.getUpdatedCategory());
    }
    for (AllocationPreference allocationPreference : allocationPreferences) {
      updateAllocationPreference(request, categories, allocationPreference);
    }
    List<AllocationPreference> updatedAllocationPreferences =
        allocationPreferenceService.saveAll(allocationPreferences);
    updatedAllocationPreferences.forEach(
        allocationPreference ->
            publisher.publishEvent(
                AllocationPreferenceUpdated.from(allocationPreference, username)));
    return updatedAllocationPreferences.stream()
        .map(AllocationPreference::getAllocationPreferenceId)
        .collect(Collectors.toList());
  }

  private void updateAllocationPreference(
      BulkUpdateAllocationPreference request,
      List<Category> categories,
      AllocationPreference allocationPreference) {
    String cc = allocationPreference.getCustomer().getCode();
    if (StringUtils.isNotBlank(request.getUpdatedFundCode())) {
      Fund fund = fundService.findByCodeAndCustomer(request.getUpdatedFundCode(), cc).orElse(null);
      allocationPreference.setFund(fund);
    }

    if (CollectionUtils.isNotEmpty(categories)) {
      validateCustomerCategory(
          allocationPreference.getCustomer(),
          categories,
          allocationPreference.getAllocationPreferenceId());
      allocationPreference.setCategories(categories);
    }

    if (StringUtils.isNotBlank(request.getUpdatedCustomerReference())) {
      allocationPreference.setCustomerReference(request.getUpdatedCustomerReference());
    }
    if (StringUtils.isNotBlank(request.getUpdatedDeliveryInstructions())) {
      allocationPreference.setDeliveryInstructions(request.getUpdatedDeliveryInstructions());
    }
    if (StringUtils.isNotBlank(request.getUpdatedNotes())) {
      allocationPreference.setNotes(request.getUpdatedNotes());
    }
    if (request.getUpdatedAllocationPreferenceStatus() != null) {
      allocationPreference.setStatus(request.getUpdatedAllocationPreferenceStatus());
    }
  }
}

package au.com.peterpal.selecting.standingorders.ext.category.entity;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class CategoryId extends UuidEntityId {

  public static CategoryId of(@NotEmpty UUID id) {
    return new CategoryId(id);
  }

  public static CategoryId of(@NotEmpty String id) {
    return new CategoryId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public CategoryId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public CategoryId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public CategoryId() {}

  @Override
  public UUID getId() {
    return super.getId();
  }
}

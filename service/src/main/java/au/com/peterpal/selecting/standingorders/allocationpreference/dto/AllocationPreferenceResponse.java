package au.com.peterpal.selecting.standingorders.allocationpreference.dto;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceStatus;
import au.com.peterpal.selecting.standingorders.ext.fund.dto.FundInfo;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.stream.Collectors;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AllocationPreferenceResponse {

  @NotNull @NonNull private AllocationPreferenceId allocationPreferenceId;
  private List<ReleasePreferenceResponse> releasePreferences;
  private List<String> categories;
  private AllocationPreferenceStatus allocationPreferenceStatus;
  private String customerReference;
  private String deliveryInstructions;
  private String notes;

  private FundId fundId;
  private String fundCode;

  public static AllocationPreferenceResponse from(AllocationPreference allocationPreference) {
    Affirm.of(allocationPreference).notNull("AllocationPreference must not be null");
    Affirm.of(allocationPreference.getAllocationPreferenceId())
        .notNull("AllocationPreferenceId must not be null");
    Affirm.of(allocationPreference.getCustomer()).notNull("Customer must not be null");

    List<ReleasePreferenceResponse> releasePreferenceResponseList =
        Optional.ofNullable(allocationPreference.getReleasePreferences())
            .map(ReleasePreferenceResponse::from)
            .orElse(null);

    FundInfo fund =
        Optional.ofNullable(allocationPreference.getFund()).map(FundInfo::from).orElse(null);

    return AllocationPreferenceResponse.builder()
        .allocationPreferenceId(allocationPreference.getAllocationPreferenceId())
        .allocationPreferenceStatus(allocationPreference.getStatus())
        .categories(allocationPreference.getCategories().stream().map(e -> e.getCode()).collect(Collectors.toList()))
        .customerReference(allocationPreference.getCustomerReference())
        .deliveryInstructions(allocationPreference.getDeliveryInstructions())
        .notes(allocationPreference.getNotes())
        .releasePreferences(releasePreferenceResponseList)
        .fundId(Optional.ofNullable(fund).map(FundInfo::getFundId).orElse(null))
        .fundCode(Optional.ofNullable(fund).map(FundInfo::getCode).orElse(null))
        .build();
  }
}

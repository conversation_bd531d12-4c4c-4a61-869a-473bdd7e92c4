package au.com.peterpal.selecting.standingorders.pendingorder.dto;

import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import io.swagger.annotations.ApiModel;
import javax.validation.constraints.NotNull;
import lombok.*;

import java.util.List;

@With
@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
@ApiModel
public class SubmitPendingOrderRequest {
  @NotNull @NonNull TitleId titleId;
  @NotNull @NonNull List<PendingOrderId> acceptedPendingOrderIds;
  @NotNull @NonNull List<PendingOrderId> cancelledPendingOrderIds;
}

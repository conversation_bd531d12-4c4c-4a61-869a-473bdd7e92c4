package au.com.peterpal.selecting.standingorders.customerstandingorder.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.SmallFormatPaperbackRule;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.Value;
import lombok.With;

@With
@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class ReleaseRequest {

  @NotNull
  @NonNull
  private ReleaseId releaseId;

  private ReleasePreferenceId preferenceId;

  @NotNull
  @NonNull
  private ReleaseType releaseType;

  private ActionType actionType;

  private AssignmentRule assignmentRule;

  private SmallFormatPaperbackRule pbRule;

  private FundId fundId;
  private FundId hbFundId;
  private FundId pbFundId;
  private Integer qty;
  private Integer hbQty;
  private Integer pbQty;
  @Builder.Default private Boolean rematchAllocationAfterUpdate = Boolean.FALSE;

  public static ReleaseRequest get(
      @NotEmpty(message = "List of releases cannot be empty.")
      List<@Valid ReleaseRequest> releases, ReleaseId id) {
    Affirm.of(id).notNull("AllocationId must not be null");

    return releases.stream()
        .filter(r -> id.equals(r.releaseId))
        .findFirst()
        .orElse(null);
  }

  public boolean isQtyValid(){
    if(qty == null || hbQty == null || pbQty == null){
      return false;
    }
    return  qty == hbQty + pbQty;
  }

  public boolean isValidQuantity() {
    return (hbQty != null && hbQty > 0) || (pbQty != null && pbQty > 0);
  }
}

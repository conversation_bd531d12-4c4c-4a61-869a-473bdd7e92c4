@startuml

/' diagram meta data start
config=CallConfiguration;
{
  "rootMethod": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController#updateAlreadyOrderedTitle(String,TitleId)",
  "projectClassification": {
    "searchMode": "OpenProject", // OpenProject, AllProjects
    "includedProjects": "",
    "pathEndKeywords": "*.impl",
    "isClientPath": "",
    "isClientName": "",
    "isTestPath": "",
    "isTestName": "",
    "isMappingPath": "",
    "isMappingName": "",
    "isDataAccessPath": "",
    "isDataAccessName": "",
    "isDataStructurePath": "",
    "isDataStructureName": "",
    "isInterfaceStructuresPath": "",
    "isInterfaceStructuresName": "",
    "isEntryPointPath": "",
    "isEntryPointName": ""
  },
  "graphRestriction": {
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "removeByInheritance": "", // inheritance/annotation based filtering is done in a second step
    "removeByAnnotation": "",
    "removeByClassPackage": "", // cleanup the graph after inheritance/annotation based filtering is done
    "removeByClassName": "",
    "cutMappings": false,
    "cutEnum": true,
    "cutTests": true,
    "cutClient": true,
    "cutDataAccess": true,
    "cutInterfaceStructures": true,
    "cutDataStructures": true,
    "cutGetterAndSetter": true,
    "cutConstructors": true
  },
  "graphTraversal": {
    "forwardDepth": 3,
    "backwardDepth": 3,
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "hideMappings": false,
    "hideDataStructures": false,
    "hidePrivateMethods": true,
    "hideInterfaceCalls": true, // indirection: implementation -> interface (is hidden) -> implementation
    "onlyShowApplicationEntryPoints": false // root node is included
  },
  "details": {
    "aggregation": "GroupByClass", // ByClass, GroupByClass, None
    "showMethodParametersTypes": false,
    "showMethodParametersNames": false,
    "showMethodReturnType": false,
    "showPackageLevels": 2,
    "showCallOrder": false,
    "edgeMode": "MethodsOnly", // TypesOnly, MethodsOnly, TypesAndMethods, MethodsAndDirectTypeUsage
    "showDetailedClassStructure": false
  },
  "rootClass": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController"
}
diagram meta data end '/



digraph g {
    rankdir="LR"
    splines=polyline


'nodes
subgraph cluster_98689 {
   	label=com
	labeljust=l
	fillcolor="#ececec"
	style=filled

   subgraph cluster_1300071644 {
   	label=peterpal
	labeljust=l
	fillcolor="#d8d8d8"
	style=filled

   subgraph cluster_1774816029 {
   	label=StandingOrderController
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderController1189585491XXXupdateAlreadyOrderedTitle1444931474[
	label="+ updateAlreadyOrderedTitle()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderController

null"
	penwidth=4
	fontcolor=darkgreen
];
}

subgraph cluster_1867065251 {
   	label=Affirm
	labeljust=l
	fillcolor=white
	style=filled

   Affirm1046088404XXXnotNull1808118735[
	label="+ notNull()"
	style=filled
	fillcolor=white
	tooltip="Affirm

&#10;  Affirm that the object is not {@code null}&#10;  \<pre\>&#10;  Validator.of(value).notNull(\"The value must not be null\");&#10;  \</pre\>&#10;  @param message&#10; "
	fontcolor=darkgreen
];

Affirm1046088404XXXof1939501217[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="Affirm

null"
	fontcolor=darkgreen
];
}

subgraph cluster_614879794 {
   	label=StandingOrderBL
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderBL1335247380XXXhandle1831784018[
	label="+ handle()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderBL

null"
	fontcolor=darkgreen
];
}

subgraph cluster_844599670 {
   	label=TitleAlreadyOrderedCmd
	labeljust=l
	fillcolor=white
	style=filled

   TitleAlreadyOrderedCmd1592605519XXXof357404019[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="TitleAlreadyOrderedCmd

null"
	fontcolor=darkgreen
];
}

subgraph cluster_94292705 {
   	label=TitleAlreadyOrdered
	labeljust=l
	fillcolor=white
	style=filled

   TitleAlreadyOrdered586215038XXXbuilder0[
	label="+ builder()"
	style=filled
	fillcolor=white
	tooltip="TitleAlreadyOrdered

null"
	fontcolor=darkgreen
];

TitleAlreadyOrdered586215038XXXfrom348089184[
	label="+ from()"
	style=filled
	fillcolor=white
	tooltip="TitleAlreadyOrdered

null"
	fontcolor=darkgreen
];
}
}
}

'edges
StandingOrderBL1335247380XXXhandle1831784018 -> Affirm1046088404XXXnotNull1808118735;
StandingOrderBL1335247380XXXhandle1831784018 -> Affirm1046088404XXXof1939501217;
StandingOrderBL1335247380XXXhandle1831784018 -> TitleAlreadyOrdered586215038XXXfrom348089184;
StandingOrderController1189585491XXXupdateAlreadyOrderedTitle1444931474 -> StandingOrderBL1335247380XXXhandle1831784018;
StandingOrderController1189585491XXXupdateAlreadyOrderedTitle1444931474 -> TitleAlreadyOrderedCmd1592605519XXXof357404019;
TitleAlreadyOrdered586215038XXXfrom348089184 -> TitleAlreadyOrdered586215038XXXbuilder0;

}
@enduml

@startuml

/' diagram meta data start
config=CallConfiguration;
{
  "rootMethod": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController#getCategoryInfo(String,String)",
  "projectClassification": {
    "searchMode": "OpenProject", // OpenProject, AllProjects
    "includedProjects": "",
    "pathEndKeywords": "*.impl",
    "isClientPath": "",
    "isClientName": "",
    "isTestPath": "",
    "isTestName": "",
    "isMappingPath": "",
    "isMappingName": "",
    "isDataAccessPath": "",
    "isDataAccessName": "",
    "isDataStructurePath": "",
    "isDataStructureName": "",
    "isInterfaceStructuresPath": "",
    "isInterfaceStructuresName": "",
    "isEntryPointPath": "",
    "isEntryPointName": ""
  },
  "graphRestriction": {
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "removeByInheritance": "", // inheritance/annotation based filtering is done in a second step
    "removeByAnnotation": "",
    "removeByClassPackage": "", // cleanup the graph after inheritance/annotation based filtering is done
    "removeByClassName": "",
    "cutMappings": false,
    "cutEnum": true,
    "cutTests": true,
    "cutClient": true,
    "cutDataAccess": true,
    "cutInterfaceStructures": true,
    "cutDataStructures": true,
    "cutGetterAndSetter": true,
    "cutConstructors": true
  },
  "graphTraversal": {
    "forwardDepth": 3,
    "backwardDepth": 3,
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "hideMappings": false,
    "hideDataStructures": false,
    "hidePrivateMethods": true,
    "hideInterfaceCalls": true, // indirection: implementation -> interface (is hidden) -> implementation
    "onlyShowApplicationEntryPoints": false // root node is included
  },
  "details": {
    "aggregation": "GroupByClass", // ByClass, GroupByClass, None
    "showMethodParametersTypes": false,
    "showMethodParametersNames": false,
    "showMethodReturnType": false,
    "showPackageLevels": 2,
    "showCallOrder": false,
    "edgeMode": "MethodsOnly", // TypesOnly, MethodsOnly, TypesAndMethods, MethodsAndDirectTypeUsage
    "showDetailedClassStructure": false
  },
  "rootClass": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController"
}
diagram meta data end '/



digraph g {
    rankdir="LR"
    splines=polyline


'nodes
subgraph cluster_98689 {
   	label=com
	labeljust=l
	fillcolor="#ececec"
	style=filled

   subgraph cluster_1300071644 {
   	label=peterpal
	labeljust=l
	fillcolor="#d8d8d8"
	style=filled

   subgraph cluster_1092055162 {
   	label=CategoryInfo
	labeljust=l
	fillcolor=white
	style=filled

   CategoryInfo843015776XXXof1820369945[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="CategoryInfo

null"
	fontcolor=darkgreen
];
}

subgraph cluster_125360093 {
   	label=CategoryService
	labeljust=l
	fillcolor=white
	style=filled

   CategoryService1335247380XXXgetCategory1808118735[
	label="+ getCategory()"
	style=filled
	fillcolor=white
	tooltip="CategoryService

&#10;  Returns category information (category and priority) for given subject.&#10; &#10;  @param subject subject for which category has to be returned&#10;  @return \<class\>CategoryInfo\</class\> if a match exists or null&#10; "
	fontcolor=darkgreen
];
}

subgraph cluster_1711118752 {
   	label=SubjectMatcher
	labeljust=l
	fillcolor=white
	style=filled

   SubjectMatcher1335247380XXXmatch1808118735[
	label="+ match()"
	style=filled
	fillcolor=white
	tooltip="SubjectMatcher

&#10;  Find a category and priority for given subject string.&#10;  \<p\>&#10;  Get the first character of the subject.&#10;  Search for a mapping in the map.&#10;  If mapping is found and there is one mapping then return a category info&#10;  If more than one mapping found: find first mapping that subject length is \>=&#10; &#10;  @param subject the subject string&#10;  @return a category and priority, or null if map is empty or subject is null or empty.&#10; "
	fontcolor=darkgreen
];

SubjectMatcher1335247380XXXof937476012[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="SubjectMatcher

&#10;  Create a subject matcher given a list of category mappings.&#10;  \<p\>&#10;  For each subject in the category mapping list get the first character. Find the list of&#10;  category mappings corresponding to the character in the map or create a new empty list. Add&#10;  the category mapping to the list.&#10; &#10;  @param mapping list of category mappings&#10;  @return a new subject matcher&#10; "
	fontcolor=darkgreen
];
}

subgraph cluster_1774816029 {
   	label=StandingOrderController
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderController1189585491XXXgetCategoryInfo2013066710[
	label="+ getCategoryInfo()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderController

null"
	penwidth=4
	fontcolor=darkgreen
];
}

subgraph cluster_468234162 {
   	label=StringAffirm
	labeljust=l
	fillcolor=white
	style=filled

   StringAffirm1046088404XXXof1808118735[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="StringAffirm

null"
	fontcolor=darkgreen
];
}
}
}

'edges
CategoryService1335247380XXXgetCategory1808118735 -> SubjectMatcher1335247380XXXmatch1808118735;
CategoryService1335247380XXXgetCategory1808118735 -> SubjectMatcher1335247380XXXof937476012;
StandingOrderController1189585491XXXgetCategoryInfo2013066710 -> CategoryService1335247380XXXgetCategory1808118735;
SubjectMatcher1335247380XXXmatch1808118735 -> CategoryInfo843015776XXXof1820369945;
SubjectMatcher1335247380XXXmatch1808118735 -> StringAffirm1046088404XXXof1808118735;

}
@enduml

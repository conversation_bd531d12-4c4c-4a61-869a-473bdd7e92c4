package au.com.peterpal.selecting.standingorders.catalog.model;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ToString
@Embeddable
public class Dewey {

  @Enumerated(EnumType.STRING)
  @Builder.Default
  private DeweyEdition deweyEdition = DeweyEdition.DC22;

  private String deweyPrefix;

  @Column(precision=20, scale=17)
  private BigDecimal deweyNumber;

  private String deweySuffix;
}

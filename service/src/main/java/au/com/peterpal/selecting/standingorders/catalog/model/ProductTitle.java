package au.com.peterpal.selecting.standingorders.catalog.model;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Data
@Embeddable
public class ProductTitle {

  @Enumerated(EnumType.STRING)
  @Column(nullable=false)
  @Builder.Default
  private TitleTypeCode titleType = TitleTypeCode.TITLE;

  private Integer abbreviatedLength;

  @Column(nullable=false)
  private String titleText;

  private String titlePrefix;

  @Column(nullable=false)
  private String titleWithoutPrefix;

  private String subtitle;
}

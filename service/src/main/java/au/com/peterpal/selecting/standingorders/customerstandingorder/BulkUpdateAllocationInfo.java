package au.com.peterpal.selecting.standingorders.customerstandingorder;

import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.UpdateAllocationInfo;
import lombok.NoArgsConstructor;
import lombok.Value;

import java.util.ArrayList;
import java.util.List;

@Value
@NoArgsConstructor
public class BulkUpdateAllocationInfo {
  List<UpdateAllocationInfo> allocations = new ArrayList<>();
}

package au.com.peterpal.selecting.standingorders.titles.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.*;
import lombok.*;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder(toBuilder = true)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "related_title")
public class RelatedTitle {

  @EqualsAndHashCode.Include @NonNull @EmbeddedId private RelatedTitleId relatedTitleId;

  @OneToOne
  @JoinColumn(name = "original_title_id")
  private Title originalTitle;

  @OneToOne
  @JoinColumn(name = "related_title_id")
  private Title relatedTitle;
}

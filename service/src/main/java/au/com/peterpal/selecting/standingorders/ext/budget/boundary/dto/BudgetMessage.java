package au.com.peterpal.selecting.standingorders.ext.budget.boundary.dto;

import au.com.peterpal.selecting.standingorders.ext.budget.entity.BudgetId;
import au.com.peterpal.selecting.standingorders.ext.budget.entity.CurrencyCode;
import au.com.peterpal.selecting.standingorders.ext.budget.entity.BudgetRelatedFundId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Maps;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

@Data
@NoArgsConstructor(force = true)
@AllArgsConstructor(staticName = "of")
@Setter(AccessLevel.PRIVATE)
@Builder
public class BudgetMessage {

  @NotNull private BudgetId budgetId;

  @NotNull private CustomerId customerId;

  @NotNull private FundId fundId;

  @NotNull private BigDecimal amount;

  @NotNull @Builder.Default private BigDecimal topUpAmount = BigDecimal.ZERO;

  @NotNull
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDate startDate;

  @NotNull
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDate endDate;

  @NotNull private CurrencyCode currencyCode;

  @Builder.Default private Map<BudgetRelatedFundId, FundId> mapOfRelatedFunds = Maps.newHashMap();
}

package au.com.peterpal.selecting.standingorders.ext.supplier.entity;

import au.com.peterpal.selecting.standingorders.ext.supplier.boundary.dto.SupplierStatus;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@RequiredArgsConstructor(staticName = "of")
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "supplier")
public class Supplier {

  @EqualsAndHashCode.Include @ToString.Include @NonNull @NotNull @EmbeddedId
  private SupplierId supplierId;

  String name;
  String code;

  @Enumerated(EnumType.STRING)
  private SupplierStatus status;


  @Data
  @NoArgsConstructor(force = true)
  @AllArgsConstructor(staticName = "of")
  @Builder
  @With
  public static class SupplierIdentifier {
    SupplierIdentifierType type;
    String value;
  }
}

package au.com.peterpal.selecting.standingorders.products.entity;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class ProductId extends UuidEntityId {

  public static ProductId of(@NotEmpty UUID id) {
    return new ProductId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public ProductId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public ProductId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public ProductId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

package au.com.peterpal.selecting.standingorders.pendingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class PendingOrderInvalidated extends DomainEvent {

  public static PendingOrderInvalidated from(PendingOrderId pendingOrderId, String username) {
    return PendingOrderInvalidated.builder().id(pendingOrderId.getId()).username(username).build();
  }
}

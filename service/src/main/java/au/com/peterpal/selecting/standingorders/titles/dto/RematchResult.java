package au.com.peterpal.selecting.standingorders.titles.dto;

import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.titles.entity.*;
import lombok.*;

import java.util.List;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class RematchResult {
  Title title;
  List<MatchedProduct> productsNoLongerMatched;
  List<MatchedStandingOrder> standingOrdersNoLongerMatched;
  List<MatchedStandingOrder> newMatchedStandingOrders;
  List<PendingOrderId> invalidatedPendingOrders;
}

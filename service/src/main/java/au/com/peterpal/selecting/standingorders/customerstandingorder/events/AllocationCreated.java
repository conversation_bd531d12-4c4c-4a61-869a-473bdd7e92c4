package au.com.peterpal.selecting.standingorders.customerstandingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import java.util.List;
import java.util.Optional;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import org.apache.commons.lang3.StringUtils;

@Getter
@SuperBuilder
public class AllocationCreated extends DomainEvent {

  private CustomerStandingOrderId customerStandingOrderId;

  private StandingOrderId standingOrderId;

  private String customerReference;

  private String deliveryInstructions;

  private String notes;

  private AllocationPreferenceId allocationPreferenceId;

  private FundId fundId;

  private String collectionCode;

  private BranchId branchId;

  private List<String> categories;

  private AllocationStatus status;

  public static AllocationCreated from(Allocation allocation, String username) {
    return Optional.ofNullable(allocation)
        .map(a -> AllocationCreated.builder()
            .id(a.getAllocationId().getId())
            .username(StringUtils.isBlank(username) ? "system" : username)
            .customerStandingOrderId(a.getCustomerStandingOrder().getCustomerStandingOrderId())
            .standingOrderId(a.getCustomerStandingOrder().getStandingOrder().getStandingOrderId())
            .customerReference(a.getCustomerReference())
            .deliveryInstructions(a.getDeliveryInstructions())
            .notes(a.getNotes())
            .allocationPreferenceId(Optional.ofNullable(a.getAllocationPreference()).map(ap -> ap.getAllocationPreferenceId()).orElse(null))
            .fundId(Optional.ofNullable(a.getFund()).map(f -> f.getFundId()).orElse(null))
            .collectionCode(allocation.getCollectionCode())
            .branchId(Optional.ofNullable(a.getBranch()).map(b -> b.getBranchId()).orElse(null))
            .categories(a.getCategoryCodes())
            .status(a.getStatus())
            .build()
        )
        .orElseThrow(() -> new IllegalArgumentException("Can not create AllocationCreated event from null allocation"));
  }
}

package au.com.peterpal.selecting.standingorders.allocationpreference.model;

import lombok.extern.log4j.Log4j2;

@Log4j2
public enum ActionType {
  IGNORE,
  ORDER;

  public static ActionType valueAt(int i) {
    if (i < 0 || i > values().length -1) {
      int finalI = i;
      log.warn(() -> String.format("Converting action type from %d to 0", finalI));
      i = 0;
    }
    return values()[i];
  }
}

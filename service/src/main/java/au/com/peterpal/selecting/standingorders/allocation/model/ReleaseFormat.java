package au.com.peterpal.selecting.standingorders.allocation.model;

import au.com.peterpal.selecting.standingorders.customerstandingorder.model.Format;

import java.util.stream.Stream;

public enum ReleaseFormat {
  HB("Hardback"),
  PB("Paperback"),
  SFPB("Small Format Paperback");

  private final String description;

  ReleaseFormat(String description) {
    this.description = description;
  }

  public static ReleaseFormat fromProductFormat(Format productFormat) {
    switch (productFormat) {
      case PAPERBACK:
        return PB;
      case HARDBACK:
        return HB;
      case SMALL_FORMAT_PAPERBACK:
        return SFPB;
      default:
        return null;
    }
  }

  public static Stream<ReleaseFormat> stream() {
    return Stream.of(ReleaseFormat.values());
  }

  public String getDescription() {
    return description;
  }
}

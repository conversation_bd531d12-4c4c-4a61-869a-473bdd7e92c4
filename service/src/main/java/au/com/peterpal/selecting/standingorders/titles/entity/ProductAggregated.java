package au.com.peterpal.selecting.standingorders.titles.entity;

import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.standingorder.dto.ProductInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import javax.persistence.*;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder(toBuilder = true)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "product_aggregated")
public class ProductAggregated {

  @EqualsAndHashCode.Include @NonNull @EmbeddedId private ProductAggregatedId productAggregatedId;

  @ManyToOne
  @JoinColumn(name = "titleId", nullable = false)
  private Title title;

  @OneToOne
  @JoinColumn(name = "matchedProductId", nullable = false)
  private MatchedProduct matchedProduct;

  @Enumerated(EnumType.STRING)
  private ProductAggregatedStatus status;

  public static ProductAggregated copyAcceptedProductAggregated(
      Title relatedTitle, MatchedProduct matchedProduct) {
    return ProductAggregated.builder()
        .productAggregatedId(new ProductAggregatedId())
        .title(relatedTitle)
        .matchedProduct(matchedProduct)
        .status(ProductAggregatedStatus.ACCEPTED)
        .build();
  }

  public ProductInfo toProductInfo() {
    return ProductInfo.builder()
        .productAggregatedId(getProductAggregatedId())
        .isbn(getMatchedProduct().getIsbn())
        .format(ReleaseFormat.fromProductFormat(getMatchedProduct().getFormat()))
        .pubDate(getMatchedProduct().getPublicationDate())
        .build();
  }
}

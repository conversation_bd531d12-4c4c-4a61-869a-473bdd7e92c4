package au.com.peterpal.selecting.standingorders.standingorder.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.standingorder.model.AdHocProductId;
import au.com.peterpal.selecting.standingorders.titles.entity.ProductAggregatedId;
import au.com.peterpal.selecting.standingorders.utils.CurrencyCode;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.*;

import javax.validation.constraints.NotNull;

@With
@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class ProductInfo {

    ProductAggregatedId productAggregatedId;

    @NotNull
    @NonNull
    String isbn;

    @NotNull
    @NonNull
    ReleaseFormat format;

    @NotNull
    @NonNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    LocalDate pubDate;

    BigDecimal price;

    CurrencyCode currencyCode;
}

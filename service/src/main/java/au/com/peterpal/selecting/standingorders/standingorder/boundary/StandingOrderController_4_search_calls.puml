@startuml

/' diagram meta data start
config=CallConfiguration;
{
  "rootMethod": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController#search(StandingOrderId,AllocationStatus,String,ReleaseType,Pageable)",
  "projectClassification": {
    "searchMode": "OpenProject", // OpenProject, AllProjects
    "includedProjects": "",
    "pathEndKeywords": "*.impl",
    "isClientPath": "",
    "isClientName": "",
    "isTestPath": "",
    "isTestName": "",
    "isMappingPath": "",
    "isMappingName": "",
    "isDataAccessPath": "",
    "isDataAccessName": "",
    "isDataStructurePath": "",
    "isDataStructureName": "",
    "isInterfaceStructuresPath": "",
    "isInterfaceStructuresName": "",
    "isEntryPointPath": "",
    "isEntryPointName": ""
  },
  "graphRestriction": {
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "removeByInheritance": "", // inheritance/annotation based filtering is done in a second step
    "removeByAnnotation": "",
    "removeByClassPackage": "", // cleanup the graph after inheritance/annotation based filtering is done
    "removeByClassName": "",
    "cutMappings": false,
    "cutEnum": true,
    "cutTests": true,
    "cutClient": true,
    "cutDataAccess": true,
    "cutInterfaceStructures": true,
    "cutDataStructures": true,
    "cutGetterAndSetter": true,
    "cutConstructors": true
  },
  "graphTraversal": {
    "forwardDepth": 3,
    "backwardDepth": 3,
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "hideMappings": false,
    "hideDataStructures": false,
    "hidePrivateMethods": true,
    "hideInterfaceCalls": true, // indirection: implementation -> interface (is hidden) -> implementation
    "onlyShowApplicationEntryPoints": false // root node is included
  },
  "details": {
    "aggregation": "GroupByClass", // ByClass, GroupByClass, None
    "showMethodParametersTypes": false,
    "showMethodParametersNames": false,
    "showMethodReturnType": false,
    "showPackageLevels": 2,
    "showCallOrder": false,
    "edgeMode": "MethodsOnly", // TypesOnly, MethodsOnly, TypesAndMethods, MethodsAndDirectTypeUsage
    "showDetailedClassStructure": false
  },
  "rootClass": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController"
}
diagram meta data end '/



digraph g {
    rankdir="LR"
    splines=polyline


'nodes
subgraph cluster_98689 {
   	label=com
	labeljust=l
	fillcolor="#ececec"
	style=filled

   subgraph cluster_1300071644 {
   	label=peterpal
	labeljust=l
	fillcolor="#d8d8d8"
	style=filled

   subgraph cluster_152443467 {
   	label=SearchAllocationsByStandingOrder
	labeljust=l
	fillcolor=white
	style=filled

   SearchAllocationsByStandingOrder1355700039XXXbuilder0[
	label="+ builder()"
	style=filled
	fillcolor=white
	tooltip="SearchAllocationsByStandingOrder

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1774816029 {
   	label=StandingOrderController
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderController1189585491XXXsearch1953637726[
	label="+ search()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderController

null"
	penwidth=4
	fontcolor=darkgreen
];
}

subgraph cluster_1867065251 {
   	label=Affirm
	labeljust=l
	fillcolor=white
	style=filled

   Affirm1046088404XXXnotNull1808118735[
	label="+ notNull()"
	style=filled
	fillcolor=white
	tooltip="Affirm

&#10;  Affirm that the object is not {@code null}&#10;  \<pre\>&#10;  Validator.of(value).notNull(\"The value must not be null\");&#10;  \</pre\>&#10;  @param message&#10; "
	fontcolor=darkgreen
];

Affirm1046088404XXXof1939501217[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="Affirm

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1896637021 {
   	label=AllocationRepositoryCustomImpl
	labeljust=l
	fillcolor=white
	style=filled

   AllocationRepositoryCustomImpl1065794828XXXsearch1701967072[
	label="+ search()"
	style=filled
	fillcolor=white
	tooltip="AllocationRepositoryCustomImpl

null"
	fontcolor=darkgreen
];
}

subgraph cluster_2104509747 {
   	label=AllocationService
	labeljust=l
	fillcolor=white
	style=filled

   AllocationService1065794828XXXsearch1701967072[
	label="+ search()"
	style=filled
	fillcolor=white
	tooltip="AllocationService

null"
	fontcolor=darkgreen
];
}

subgraph cluster_92145940 {
   	label=AllocationBL
	labeljust=l
	fillcolor=white
	style=filled

   AllocationBL1065794828XXXhandle1701967072[
	label="+ handle()"
	style=filled
	fillcolor=white
	tooltip="AllocationBL

null"
	fontcolor=darkgreen
];
}
}
}

'edges
AllocationBL1065794828XXXhandle1701967072 -> Affirm1046088404XXXnotNull1808118735;
AllocationBL1065794828XXXhandle1701967072 -> Affirm1046088404XXXof1939501217;
AllocationBL1065794828XXXhandle1701967072 -> AllocationService1065794828XXXsearch1701967072;
AllocationService1065794828XXXsearch1701967072 -> AllocationRepositoryCustomImpl1065794828XXXsearch1701967072;
StandingOrderController1189585491XXXsearch1953637726 -> AllocationBL1065794828XXXhandle1701967072;
StandingOrderController1189585491XXXsearch1953637726 -> SearchAllocationsByStandingOrder1355700039XXXbuilder0;

}
@enduml

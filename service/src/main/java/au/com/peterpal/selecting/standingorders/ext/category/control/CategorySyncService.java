package au.com.peterpal.selecting.standingorders.ext.category.control;

import au.com.peterpal.common.utils.ext.SyncService;
import au.com.peterpal.selecting.standingorders.ext.category.boundary.dto.CategoryMessage;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import org.springframework.stereotype.Service;

@Service
public class CategorySyncService
    extends SyncService<CategoryRepository, CategoryMessage, Category> {

  protected CategorySyncService(CategoryRepository categoryRepository) {
    super(categoryRepository);
  }

  @Override
  protected Object getId(CategoryMessage msg) {
    return msg.getCategoryId();
  }

  @Override
  protected CategoryMessage update(Category existing, CategoryMessage msg) {
    existing.setCode(msg.getCode());
    existing.setDescription(msg.getDescription());
    existing.setStatus(msg.getStatus());
    save(existing);
    return msg;
  }

  @Override
  protected CategoryMessage create(CategoryMessage msg) {
    save(
        Category.builder()
            .categoryId(msg.getCategoryId())
            .code(msg.getCode())
            .description(msg.getDescription())
            .status(msg.getStatus())
            .build());
    return msg;
  }
}

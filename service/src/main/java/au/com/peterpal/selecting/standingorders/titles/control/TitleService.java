package au.com.peterpal.selecting.standingorders.titles.control;

import static au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus.NEW;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.ext.category.control.CategoryRepository;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.ClientWebApiGateway;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.ClientWebProductInfo;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.CwProductInfo;
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderService;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.control.RejectionReasonTypeRepository;
import au.com.peterpal.selecting.standingorders.standingorder.events.CategoryAssigned;
import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonType;
import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonTypeId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.titles.dto.*;
import au.com.peterpal.selecting.standingorders.titles.entity.*;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleType;
import au.com.peterpal.selecting.standingorders.titles.events.TitleProcessed;
import au.com.peterpal.selecting.standingorders.titles.events.TitleRejected;
import au.com.peterpal.selecting.standingorders.titles.events.TitleUndone;
import com.google.common.collect.Lists;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

@Log4j2
@Service
@RequiredArgsConstructor
public class TitleService {

  private final TitleRepository titleRepository;
  private final RelatedTitleRepository relatedTitleRepository;
  private final PendingOrderService pendingOrderService;
  private final ClientWebApiGateway clientWebApiGateway;
  private final RejectionReasonTypeRepository rejectionReasonTypeRepository;
  private final CategoryRepository categoryRepository;
  private final EventPublisher eventPublisher;

  public TitleSearchResponse search(TitleSearchRequest request) {
    return titleRepository.search(request);
  }

  public Page<Title> searchTitle(TitleSearchRequest request) {
    return titleRepository.searchTitle(request);
  }

  public TitleDetailResponse findTitleDetailById(TitleId titleId) {
    Map<StandingOrderId, TitleStandingOrderAllocationCategory> standingOrderCategories =
        titleRepository.findStandingOrderCategories(Set.of(titleId));
    TitleDetailResponse response =
        titleRepository
            .findById(titleId)
            .map(t -> TitleDetailResponse.from(t, standingOrderCategories))
            .orElse(TitleDetailResponse.builder().build());

    List<TitleDetailResponse.Product> detailedProduct =
        Optional.ofNullable(response.getProducts()).orElse(Lists.newArrayList()).stream()
            .map(this::includeProductInfoFroCatalogue)
            .collect(Collectors.toList());

    return response.withProducts(detailedProduct);
  }

  public Title findTitleById(TitleId titleId) {
    return titleRepository
        .findById(titleId)
        .orElseThrow(() -> new ResourceNotFoundException(Title.class, String.valueOf(titleId)));
  }

  public Title findOriginalTitleById(TitleId titleId) {
    Title title = findTitleById(titleId);
    if (title.isRelatedTitle()) {
      return relatedTitleRepository
          .findByRelatedTitleTitleId(titleId)
          .map(RelatedTitle::getOriginalTitle)
          .orElseThrow(() -> new ResourceNotFoundException(Title.class, String.valueOf(titleId)));
    } else {
      return title;
    }
  }

  public Optional<Title> findNonProcessedOriginalTitleByIsbn(String isbn) {
    return titleRepository.findTitleContainingIsbn(isbn, TitleType.ORIGINAL, null).stream()
        .filter(title -> !title.isProcessed())
        .findAny();
  }

  public List<Title> findAllTitleByIsbnAndStandingOrderId(
      String isbn, StandingOrderId standingOrderId) {
    return titleRepository.findAllTitleByIsbnAndStandingOrderId(isbn, standingOrderId);
  }

  private TitleDetailResponse.Product includeProductInfoFroCatalogue(
      TitleDetailResponse.Product product) {
    Optional<CwProductInfo> catProduct = clientWebApiGateway.findProductByIsbn(product.getIsbn());
    if (catProduct.isPresent()) {
      CwProductInfo p = catProduct.get();
      ClientWebProductInfo productInfoById = clientWebApiGateway.getProductInfoById(p.getId());
      product =
          product
              .withEditionStatement(p.getEditionStatement())
              .withHeight(p.getHeight())
              .withWidth(p.getWidth())
              .withSubjectCode(p.getBicSubjectCode1())
              .withAudience(p.getAudience())
              .withPrice(p.getRrpPrice())
              .withNumberOfPages(productInfoById.getProductSummary().getNumberOfPages());
    }
    return product;
  }

  public Title rejectTitle(
      TitleId titleId,
      RejectionReasonTypeId rejectionReasonTypeId,
      String rejectionReason,
      String username) {
    Title title =
        titleRepository
            .findById(titleId)
            .orElseThrow(
                () -> new EntityNotFoundException(String.format("Title not found %s", titleId)));
    return rejectTitle(title, rejectionReasonTypeId, rejectionReason, username, true);
  }

  public Title rejectTitle(
      Title title,
      RejectionReasonTypeId rejectionReasonTypeId,
      String rejectionReason,
      String username,
      boolean validate) {
    if (validate && title.isProcessed()) {
      throw new BusinessException(
          String.format("Can not reject title with status %s", title.getTitleStatus()));
    }

    RejectionReasonType rejectionReasonType =
        rejectionReasonTypeRepository.getOne(rejectionReasonTypeId);

    title.setTitleStatus(TitleStatus.REJECTED);
    title.setRejectionReasonType(rejectionReasonType);
    title.setRejectionOtherReason(rejectionReason);
    title
        .getStandingOrderAggregatedList()
        .forEach(s -> s.setStatus(StandingOrderAggregatedStatus.REJECTED));

    title.getProductAggregatedList().forEach(p -> p.setStatus(ProductAggregatedStatus.REJECTED));
    title.setDateModified(LocalDateTime.now());

    eventPublisher.publishEvent(
        TitleRejected.builder()
            .id(title.getTitleId().getId())
            .rejectionReasonName(rejectionReasonType.getName())
            .otherReason(rejectionReason)
            .username(username)
            .build());

    return titleRepository.save(title);
  }

  public List<Title> findOriginalTitlesByStatusAndTypeAndAllocationId(
      List<TitleStatus> statuses, AllocationId allocationId) {
    return findTitlesByStatusAndTypeAndAllocationId(
        statuses, Lists.newArrayList(TitleType.ORIGINAL), allocationId, false);
  }

  public List<Title> findRelatedTitlesByStatusAndTypeAndAllocationId(
      List<TitleStatus> statuses, AllocationId allocationId) {
    return findTitlesByStatusAndTypeAndAllocationId(
        statuses, Lists.newArrayList(TitleType.RELATED), allocationId, true);
  }

  public List<Title> findTitlesByStatusAndTypeAndAllocationId(
      List<TitleStatus> statuses,
      List<TitleType> types,
      AllocationId allocationId,
      boolean acceptedStandingOrderRequired) {
    return titleRepository.findTitlesByStatusAndTypeAndAllocationId(
        statuses, types, allocationId, acceptedStandingOrderRequired);
  }

  public void updateCategory(TitleId titleId, String categoryCode, String username) {
    Title title =
        titleRepository
            .findById(titleId)
            .orElseThrow(
                () -> new EntityNotFoundException(String.format("Title not found %s", titleId)));

    updateCategory(title, categoryCode, username);
  }

  public List<Title> findOriginalTitleStillOnOrderByStandingOrderId(
      StandingOrderId standingOrderId) {
    return titleRepository.findTitleStillOnOrderByStandingOrderIdAndType(
        standingOrderId, TitleType.ORIGINAL);
  }

  public void updateCategory(Title title, String categoryCode, String username) {
    TitleId titleId = title.getTitleId();
    int pendingOrderCount = pendingOrderService.findAllValidPendingOrdersByTitle(titleId).size();
    if (!title.isNew() || (title.isPending() && pendingOrderCount > 1)) {
      throw new BusinessException(
          "Assigning category only applied to a NEW title or PENDING title that has no pending orders.");
    }

    Category category = categoryRepository.findByCode(categoryCode);

    title.setCategory(category);

    titleRepository.save(title);

    eventPublisher.publishEvent(CategoryAssigned.of(null, category.getCode(), username));
  }

  public Title deferTitle(TitleId titleId, LocalDate deferredDate, boolean manualDeferring) {
    Title title =
        titleRepository
            .findById(titleId)
            .orElseThrow(
                () -> new EntityNotFoundException(String.format("Title not found %s", titleId)));
    if (title.isTitlePendingOrProcessed()) {
      throw new BusinessException(
          String.format("Cannot defer title with status %s", title.getTitleStatus()));
    }

    title.setTitleStatus(TitleStatus.DEFERRED);
    title.setDeferredDate(deferredDate);
    title.setManualDeferred(manualDeferring);
    return titleRepository.save(title);
  }

  public Title unDeferTitle(Title title) {
    if (!title.isDeferred()) {
      throw new BusinessException(
          String.format("Cannot un-defer title with status %s", title.getTitleStatus()));
    }

    title.setTitleStatus(TitleStatus.NEW);
    title.setDeferredDate(null);
    return titleRepository.save(title);
  }

  public Title undoTitle(Title title, String username) {
    TitleId titleId = title.getTitleId();
    TitleStatus previousStatus = title.getTitleStatus();
    title.setTitleStatus(NEW);
    title.setRejectionReasonType(null);
    title.setRejectionOtherReason(null);
    title.getStandingOrderAggregatedList().forEach(s -> s.setStatus(null));
    title.getProductAggregatedList().forEach(p -> p.setStatus(null));
    title.getMatchedProducts().forEach(mp -> mp.setStatus(MatchedProductStatus.ACTIVE));
    title
        .getMatchedProducts()
        .forEach(
            mp ->
                mp.getMatchedStandingOrders()
                    .forEach(mso -> mso.setStatus(MatchedStandingOrderStatus.ACTIVE)));
    title.setDateModified(LocalDateTime.now());
    title = update(title, username);

    eventPublisher.publishEvent(
        TitleUndone.builder()
            .id(titleId.getId())
            .username(username)
            .previousStatus(previousStatus)
            .newStatus(NEW)
            .build());
    return title;
  }

  public Title updateTitleStatus(Title title, TitleStatus titleStatus) {
    title.setTitleStatus(titleStatus);
    return titleRepository.save(title);
  }

  public void markTitleAsProcessed(TitleId titleId) {
    Optional<Title> titleOptional = titleRepository.findById(titleId);
    if (titleOptional.isPresent()) {
      List<PendingOrder> pendingOrderList = pendingOrderService.findByTitle(titleId);
      if (pendingOrderList.stream().allMatch(PendingOrder::isProcessedInvalidCancelled)) {
        Title title = titleOptional.get();
        title.setTitleStatus(TitleStatus.PROCESSED);
        title.setProcessedDate(LocalDate.now());
        titleRepository.save(title);
        eventPublisher.publishEvent(TitleProcessed.from(title));
        log.debug("title {} updated to PROCESSED", titleId);
      }
    }
  }

  public Title update(Title title, String user) {
    log.trace("Title {} updated by {}", title.getTitleId(), user);
    title.setDateModified(LocalDateTime.now());
    return titleRepository.saveAndFlush(title);
  }

  public void delete(TitleId titleId) {
    titleRepository.deleteById(titleId);
  }
}

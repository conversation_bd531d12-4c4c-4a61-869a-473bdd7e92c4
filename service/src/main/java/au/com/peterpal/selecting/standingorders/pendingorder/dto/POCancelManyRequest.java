package au.com.peterpal.selecting.standingorders.pendingorder.dto;

import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Value;
import lombok.With;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class POCancelManyRequest {

  @NotNull @NonNull TitleId titleId;

  RejectionReason rejectionReason;

  @NotNull @NonNull List<PendingOrderId> poIdList;

  @Data
  @Builder
  @With
  @NoArgsConstructor(force = true)
  @AllArgsConstructor(staticName = "of")
  public static class RejectionReason {

    String type;
    String text;
  }
}

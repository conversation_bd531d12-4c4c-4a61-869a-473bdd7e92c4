package au.com.peterpal.selecting.standingorders.pendingorder.model;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class PendingOrderId extends UuidEntityId {

  public static final List<PendingOrderId> EMPTY_LIST = new ArrayList<>();

  public static PendingOrderId of(@NotEmpty UUID id) {
    return new PendingOrderId(id);
  }

  public static PendingOrderId of(@NotEmpty String id) {
    return new PendingOrderId(UUID.fromString(id));
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public PendingOrderId(@NotEmpty UUID id) {
    super(id);
  }

  public PendingOrderId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

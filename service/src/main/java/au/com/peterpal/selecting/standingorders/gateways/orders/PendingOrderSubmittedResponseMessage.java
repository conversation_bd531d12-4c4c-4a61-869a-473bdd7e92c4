package au.com.peterpal.selecting.standingorders.gateways.orders;

import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Builder
@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
public class PendingOrderSubmittedResponseMessage {
  @NotNull @NotEmpty private String titleOrderNum;
  @NotNull @NotEmpty private PendingOrderId pendingOrderId;
}

package au.com.peterpal.selecting.standingorders.customerstandingorder.boundary;

import au.com.peterpal.selecting.standingorders.allocation.control.AllocationBL;
import au.com.peterpal.selecting.standingorders.allocation.dto.AllocationInfo;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.customerstandingorder.control.CustomerStandingOrderBL;
import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.*;
import au.com.peterpal.selecting.standingorders.customerstandingorder.events.*;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

@Log4j2
@RestController
@SecurityRequirement(name = "BearerAuth")
@RequestMapping("/api/customer-standing-orders")
public class CustomerStandingOrderController {

  private static final String RECEIVED_REQUEST_MSG = "Received request from user %s: %s";

  private CustomerStandingOrderBL customerStandingOrderBL;
  private AllocationBL allocationBL;

  public CustomerStandingOrderController(
      CustomerStandingOrderBL customerStandingOrderBL, AllocationBL allocationBL) {
    this.customerStandingOrderBL = customerStandingOrderBL;
    this.allocationBL = allocationBL;
  }

  @PostMapping
  @Operation(summary = "Create customer standing order")
  public UUID createCustomerStandingOrders(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestBody @Valid CreateCustomerStandingOrder request) {

    log.debug(String.format(RECEIVED_REQUEST_MSG, username, request));
    return this.customerStandingOrderBL.handle(request, username);
  }

  @PutMapping(path = "/{customerStandingOrderId}")
  @Operation(summary = "Update customer standing order")
  public CustomerStandingOrderUpdated updateCustomerStandingOrders(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable("customerStandingOrderId") CustomerStandingOrderId customerStandingOrderId,
      @RequestBody @Valid CustomerStandingOrderRequest request) {

    log.debug(String.format(RECEIVED_REQUEST_MSG, username, request));
    return this.customerStandingOrderBL.handle(request, customerStandingOrderId, username);
  }

  @GetMapping("/{customerStandingOrderId}")
  @Operation(summary = "Get customer standing order given it's identifier")
  public CustomerStandingOrder getCustomerStandingOrders(
      @PathVariable UUID customerStandingOrderId) {
    return this.customerStandingOrderBL.handle(customerStandingOrderId);
  }

  @PostMapping("/allocations")
  @Operation(summary = "Create allocation")
  public CreateAllocationResponse createAllocation(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestBody @Valid AllocationRequest request) {
    log.debug(String.format(RECEIVED_REQUEST_MSG, username, request));
    return customerStandingOrderBL.createAllocation(request, username);
  }

  @PutMapping("/allocations/{allocationId}")
  @Operation(summary = "Update allocation")
  public AllocationUpdated updatedAllocation(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable("allocationId") AllocationId allocationId,
      @RequestBody @Valid UpdateAllocationInfo request) {

    return this.customerStandingOrderBL.handle(request, allocationId, username);
  }

  @PutMapping("/allocations/{allocationId}/assignFormat")
  @Operation(summary = "Assign format to allocation - paperback, hardback, trade paperback, small format paperback, alternate product")
  public FormatAssigned allocationAssignFormat(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable @NotBlank AllocationId allocationId,
      @RequestBody @Valid AssignFormat request) {

    return this.customerStandingOrderBL.handle(allocationId, request.getFormat(), username);
  }

  @PutMapping("/allocations/{allocationId}/categories")
  @Operation(summary = "Assign category to allocation")
  public CategoryAssigned allocationAssignCategory(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable @NotBlank AllocationId allocationId,
      @RequestBody @Valid AssignCategory request) {

    return this.customerStandingOrderBL.handle(allocationId, request.getCategories(), username);
  }

  @PutMapping("/allocations/{allocationId}/notes")
  @Operation(summary = "Add note to allocation")
  public NoteAdded allocationAddNote(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable @NotBlank AllocationId allocationId,
      @RequestBody @Valid Note note) {

    return this.customerStandingOrderBL.handleNoteAdded(allocationId, note.getText(), username);
  }

  @DeleteMapping("/allocations/{allocationId}")
  @Operation(summary = "Make allocation INACTIVE")
  public AllocationId deleteAllocation(
      @RequestHeader(value = "username", defaultValue = "") String username,
      @PathVariable String allocationId) {

    AllocationId id = AllocationId.of(allocationId);
    return this.allocationBL.handle(id, username);
  }

  @PutMapping("/allocations/{allocationId}/releases")
  @Operation(summary = "Update allocation releases")
  public void updateReleases(
      @RequestHeader(value = "username", defaultValue = "") String username,
      @PathVariable AllocationId allocationId,
      @RequestBody
      @NotEmpty(message = "List of releases cannot be empty.")
      List<@Valid ReleaseRequest> releases
  ) {
    customerStandingOrderBL.updateReleases(allocationId, releases, username);
  }

  @PutMapping("/allocations/branches")
  @Operation(summary = "Update branch allocations")
  public List<AllocationInfo> updateBranchAllocations(
      @RequestHeader(value = "username", defaultValue = "") String username,
      @RequestBody @Valid UpdateBranchAllocationRequest request) {
    return customerStandingOrderBL
        .updateBranchAllocations(
            request.getBranchAllocationInfos(), request.getRematchAllocationAfterUpdate(), username)
        .stream()
        .sorted(Comparator.comparing(a -> a.getBranch().getCode()))
        .collect(Collectors.toList());
  }
}

package au.com.peterpal.selecting.standingorders.titles.entity;

import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import javax.persistence.Entity;
import javax.persistence.Id;
import lombok.Data;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;
import org.hibernate.annotations.Synchronize;

@Entity
@Data
@Subselect(
    "select t.id as id, min(c.code) as customer_code from standing_order t \n"
        + "inner join customer_standing_order cso on t.id= cso.standing_order_id \n"
        + "inner join customer c on c.id = cso.customer_id \n"
        + "group by t.id ")
@Synchronize({
  "standing_order",
  "customer_standing_order",
  "customer"
})
@Immutable
public class StandingOrderCustomerAgg {
  @Id private StandingOrderId standingOrderId;
  private String customerCode;
}

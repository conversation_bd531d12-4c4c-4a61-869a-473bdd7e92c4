package au.com.peterpal.selecting.standingorders.gateways.orders;

import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderBL;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.context.IntegrationContextUtils;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.handler.LoggingHandler;
import org.springframework.integration.jms.dsl.Jms;

import javax.jms.ConnectionFactory;

@Log4j2
@Configuration
@EnableIntegration
@RequiredArgsConstructor
public class OrderServiceInboundIntegration {

  private final ConnectionFactory connectionFactory;
  private final PendingOrderBL pendingOrderBL;

  @Value("${events.orders.channel:standing-orders-events-topic}")
  private String standingOrderToOrdersIntegrationQueue;

  private static final String MESSAGE_TYPE = "message_type";
  private static final String LUCY4_ORDER_CREATED = "lucy4-order-created";

  /**
   * inbound flow that consumed a response from lucy4-api everytime an order is successfully created
   * and then send the response back to standing-order-service.
   *
   * @return
   */
  @Bean
  public IntegrationFlow orderCreatedEventInboundFlow() {
    return IntegrationFlows.from(
            Jms.messageDrivenChannelAdapter(this.connectionFactory)
                .destination(standingOrderToOrdersIntegrationQueue)
                .errorChannel(IntegrationContextUtils.ERROR_CHANNEL_BEAN_NAME)
                .configureListenerContainer(
                    spec ->
                        spec.messageSelector(
                            String.format("%s = '%s'", MESSAGE_TYPE, LUCY4_ORDER_CREATED))))
        .transform(Transformers.fromJson(PendingOrderSubmittedResponseMessage.class))
        .<PendingOrderSubmittedResponseMessage>handle(
            (p, h) ->
                pendingOrderBL.addOrderNumber(p.getPendingOrderId(), p.getTitleOrderNum()))
        .log(LoggingHandler.Level.DEBUG, message -> message)
        .get();
  }
}

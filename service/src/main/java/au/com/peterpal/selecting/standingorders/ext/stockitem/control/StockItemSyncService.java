package au.com.peterpal.selecting.standingorders.ext.stockitem.control;

import au.com.peterpal.common.utils.ext.SyncService;
import au.com.peterpal.selecting.standingorders.ext.stockitem.boundary.dto.StockItemMessage;
import au.com.peterpal.selecting.standingorders.ext.stockitem.entity.StockItem;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;

@Log4j2
@Service
public class StockItemSyncService extends SyncService<StockItemRepository, StockItemMessage, StockItem> {

  private StockItemRepository repo;

  protected StockItemSyncService(StockItemRepository repo) {
    super(repo);
    this.repo = repo;
  }

  @Override
  protected Object getId(StockItemMessage msg) {
    return msg.getStockItemId();
  }

  @Override
  protected StockItemMessage update(StockItem existing, StockItemMessage msg) {
    existing.setTitleWithoutPrefix(msg.getTitleWithoutPrefix());
    existing.setAuthor(msg.getAuthor());
    save(existing);
    return msg;
  }

  @Override
  protected StockItemMessage create(StockItemMessage msg) {
    String str = "Stock item will not be created because one with product reference %s already exists.";
    List<StockItem> l = repo.findByProductReference(msg.getProductReference());
    if (!l.isEmpty()) {
      log.warn(() -> String.format(str, msg.getProductReference()));
    } else {
      save(StockItem.builder()
        .stockItemId(msg.getStockItemId())
        .author(msg.getAuthor())
        .productReference(msg.getProductReference())
        .titleWithoutPrefix(msg.getTitleWithoutPrefix())
        .build());
    }
    return msg;
  }
}

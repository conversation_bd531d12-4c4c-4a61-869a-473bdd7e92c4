package au.com.peterpal.selecting.standingorders.customerstandingorder.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import lombok.*;

@With
@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class CreateAllocationResponse {
    CustomerStandingOrderId customerStandingOrderId;
    AllocationId allocationId;
}

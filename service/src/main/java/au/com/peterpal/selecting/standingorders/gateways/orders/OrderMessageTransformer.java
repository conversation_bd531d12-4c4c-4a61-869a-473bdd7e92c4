package au.com.peterpal.selecting.standingorders.gateways.orders;

import au.com.peterpal.selecting.standingorders.gateways.clientweb.ClientWebApiGateway;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.CwProductInfo;
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderService;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.titles.control.MatchedProductRepository;
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProduct;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Log4j2
@RequiredArgsConstructor
@Component
public class OrderMessageTransformer {
  public final PendingOrderService pendingOrderService;
  public final ClientWebApiGateway clientWebApiGateway;
  public final MatchedProductRepository matchedProductRepository;

  public List<OrderMessage> transform(List<PendingOrderId> pendingOrderIds) {

    List<PendingOrder> pendingOrders =
        pendingOrderIds.stream()
            .map(id -> pendingOrderService.findById(id))
            .collect(Collectors.toList());

    Map<String, CwProductInfo> catalogueMap =
        pendingOrders.stream()
            .map(PendingOrder::getOrderedProductReference)
            .distinct()
            .map(isbn -> clientWebApiGateway.findProductByIsbn(isbn).orElse(null))
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(CwProductInfo::getProductReference, Function.identity()));

    List<OrderMessage> orderMessages =
        pendingOrders.stream()
            .map(
                pendingOrder -> {
                  String isbn = pendingOrder.getOrderedProductReference();
                  OrderMessage orderMessage = OrderMessage.from(pendingOrder);

                  Optional<CwProductInfo> productInfo = Optional.ofNullable(catalogueMap.get(isbn));
                  if (productInfo.isPresent()) {
                    log.debug("ISBN {} found in the CW catalogue.", isbn);
                    orderMessage.setTitleWithoutPrefix(
                        productInfo.map(CwProductInfo::getTitleWithoutPrefix).orElse(""));
                    orderMessage.setSeriesTitle(
                        productInfo.map(CwProductInfo::getSeries).orElse(""));
                    orderMessage.setSubtitle(
                        productInfo.map(p -> p.getTitle().getSubtitle()).orElse(null));
                    orderMessage.setEditionStatement(
                        productInfo.map(CwProductInfo::getEditionStatement).orElse(""));
                    orderMessage.setAuthorsInverted(
                        productInfo.map(CwProductInfo::getAuthorsInverted).orElse(""));
                    orderMessage.setPublisher(
                        productInfo.map(CwProductInfo::getPublisherName).orElse(""));
                    orderMessage.setPublicationDate(
                        StringUtils.defaultString(
                            orderMessage.getPublicationDate(),
                            productInfo
                                .map(CwProductInfo::getPublicationDateFormatted)
                                .orElse("")));
                    orderMessage.setPublicationYear(
                        Optional.ofNullable(orderMessage.getPublicationYear())
                            .orElse(
                                productInfo.map(CwProductInfo::getPublicationYear).orElse(null)));
                    orderMessage.setCountryOfPublication(
                        productInfo.map(CwProductInfo::getCountryOfPublication).orElse(""));
                  } else {
                    log.debug(
                        "ISBN {} not found in the CW catalogue. Use detail from matched product instead",
                        isbn);
                    Optional<MatchedProduct> matchedProduct =
                        matchedProductRepository.findByTitleTitleIdAndIsbn(
                            pendingOrder.getTitle().getTitleId(), isbn);
                    if (matchedProduct.isPresent()) {
                      MatchedProduct product = matchedProduct.get();
                      orderMessage.setTitleWithoutPrefix(product.getProductTitle());
                      orderMessage.setSeriesTitle(product.getSeries());
                      orderMessage.setAuthorsInverted(product.getPersonName());
                      orderMessage.setPublicationDate(
                          Optional.ofNullable(product.getPublicationDate())
                              .map(pd -> pd.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                              .orElse(null));
                    }
                  }
                  return orderMessage;
                })
            .collect(Collectors.toList());

    return orderMessages;
  }
}

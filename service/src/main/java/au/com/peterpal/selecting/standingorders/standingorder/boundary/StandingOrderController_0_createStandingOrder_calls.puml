@startuml

/' diagram meta data start
config=CallConfiguration;
{
  "rootMethod": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController#createStandingOrder(String,CreateStandingOrderRequest)",
  "projectClassification": {
    "searchMode": "OpenProject", // OpenProject, AllProjects
    "includedProjects": "",
    "pathEndKeywords": "*.impl",
    "isClientPath": "",
    "isClientName": "",
    "isTestPath": "",
    "isTestName": "",
    "isMappingPath": "",
    "isMappingName": "",
    "isDataAccessPath": "",
    "isDataAccessName": "",
    "isDataStructurePath": "",
    "isDataStructureName": "",
    "isInterfaceStructuresPath": "",
    "isInterfaceStructuresName": "",
    "isEntryPointPath": "",
    "isEntryPointName": ""
  },
  "graphRestriction": {
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "removeByInheritance": "", // inheritance/annotation based filtering is done in a second step
    "removeByAnnotation": "",
    "removeByClassPackage": "", // cleanup the graph after inheritance/annotation based filtering is done
    "removeByClassName": "",
    "cutMappings": false,
    "cutEnum": true,
    "cutTests": true,
    "cutClient": true,
    "cutDataAccess": true,
    "cutInterfaceStructures": true,
    "cutDataStructures": true,
    "cutGetterAndSetter": true,
    "cutConstructors": true
  },
  "graphTraversal": {
    "forwardDepth": 3,
    "backwardDepth": 3,
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "hideMappings": false,
    "hideDataStructures": false,
    "hidePrivateMethods": true,
    "hideInterfaceCalls": true, // indirection: implementation -> interface (is hidden) -> implementation
    "onlyShowApplicationEntryPoints": false // root node is included
  },
  "details": {
    "aggregation": "GroupByClass", // ByClass, GroupByClass, None
    "showMethodParametersTypes": false,
    "showMethodParametersNames": false,
    "showMethodReturnType": false,
    "showPackageLevels": 2,
    "showCallOrder": false,
    "edgeMode": "MethodsOnly", // TypesOnly, MethodsOnly, TypesAndMethods, MethodsAndDirectTypeUsage
    "showDetailedClassStructure": false
  },
  "rootClass": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController"
}
diagram meta data end '/



digraph g {
    rankdir="LR"
    splines=polyline


'nodes
subgraph cluster_98689 {
   	label=com
	labeljust=l
	fillcolor="#ececec"
	style=filled

   subgraph cluster_1300071644 {
   	label=peterpal
	labeljust=l
	fillcolor="#d8d8d8"
	style=filled

   subgraph cluster_118294559 {
   	label=CreateStandingOrderRequest
	labeljust=l
	fillcolor=white
	style=filled

   CreateStandingOrderRequest666806762XXXtoCmd1808118735[
	label="+ toCmd()"
	style=filled
	fillcolor=white
	tooltip="CreateStandingOrderRequest

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1665136640 {
   	label=TermsRequest
	labeljust=l
	fillcolor=white
	style=filled

   TermsRequest666806762XXXfrom1857943656[
	label="+ from()"
	style=filled
	fillcolor=white
	tooltip="TermsRequest

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1774816029 {
   	label=StandingOrderController
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderController1189585491XXXcreateStandingOrder1111281198[
	label="+ createStandingOrder()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderController

null"
	penwidth=4
	fontcolor=darkgreen
];
}

subgraph cluster_1867065251 {
   	label=Affirm
	labeljust=l
	fillcolor=white
	style=filled

   Affirm1046088404XXXnotNull1808118735[
	label="+ notNull()"
	style=filled
	fillcolor=white
	tooltip="Affirm

&#10;  Affirm that the object is not {@code null}&#10;  \<pre\>&#10;  Validator.of(value).notNull(\"The value must not be null\");&#10;  \</pre\>&#10;  @param message&#10; "
	fontcolor=darkgreen
];

Affirm1046088404XXXof1939501217[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="Affirm

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1909489870 {
   	label=StandingOrderCreated
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderCreated586215038XXXfromCmd244890444[
	label="+ fromCmd()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderCreated

null"
	fontcolor=darkgreen
];
}

subgraph cluster_614879794 {
   	label=StandingOrderBL
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderBL1335247380XXXhandle1944904192[
	label="+ handle()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderBL

null"
	fontcolor=darkgreen
];

StandingOrderBL1335247380XXXhandle244890444[
	label="+ handle()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderBL

null"
	fontcolor=darkgreen
];
}

subgraph cluster_9088367 {
   	label=CreateStandingOrderCmd
	labeljust=l
	fillcolor=white
	style=filled

   CreateStandingOrderCmd1592605519XXXbuilder0[
	label="+ builder()"
	style=filled
	fillcolor=white
	tooltip="CreateStandingOrderCmd

null"
	fontcolor=darkgreen
];
}

subgraph cluster_943466892 {
   	label=StandingOrder
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrder843015776XXXfrom418050490[
	label="+ from()"
	style=filled
	fillcolor=white
	tooltip="StandingOrder

null"
	fontcolor=darkgreen
];
}
}
}

'edges
CreateStandingOrderRequest666806762XXXtoCmd1808118735 -> CreateStandingOrderCmd1592605519XXXbuilder0;
CreateStandingOrderRequest666806762XXXtoCmd1808118735 -> TermsRequest666806762XXXfrom1857943656;
StandingOrderBL1335247380XXXhandle1944904192 -> Affirm1046088404XXXnotNull1808118735;
StandingOrderBL1335247380XXXhandle1944904192 -> Affirm1046088404XXXof1939501217;
StandingOrderBL1335247380XXXhandle1944904192 -> StandingOrder843015776XXXfrom418050490;
StandingOrderBL1335247380XXXhandle1944904192 -> StandingOrderCreated586215038XXXfromCmd244890444;
StandingOrderBL1335247380XXXhandle244890444 -> StandingOrderBL1335247380XXXhandle1944904192;
StandingOrderController1189585491XXXcreateStandingOrder1111281198 -> CreateStandingOrderRequest666806762XXXtoCmd1808118735;
StandingOrderController1189585491XXXcreateStandingOrder1111281198 -> StandingOrderBL1335247380XXXhandle244890444;

}
@enduml

package au.com.peterpal.selecting.standingorders.profile.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.AllocationPreferenceRepository;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.ReleasePreferenceRepository;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AllocationPrefInfo;
import au.com.peterpal.selecting.standingorders.allocationpreference.events.AllocationPreferenceImported;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceStatus;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreference;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundService;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.standingorder.control.CategoryService;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import au.com.peterpal.selecting.standingorders.utils.StringAffirm;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.*;

@Log4j2
@RequiredArgsConstructor
@Component
public class CustomerProfileBL {

  private static final String HANDLING_EVENT_MSG = "Handling event: %s";

  private final CustomerProfileService customerProfileService;
  private final AllocationPreferenceRepository allocPrefsRepo;
  private final ReleasePreferenceRepository relPrefRepo;
  private final FundService fundService;
  private final EventPublisher publisher;
  private final CategoryService categoryService;

  public UUID handle(AllocationPrefInfo allocPref, boolean test, String username) {
    Affirm.of(allocPref).notNull("Allocation preference information must not be null");

    validate(allocPref);
    AllocationPreferenceImported event = AllocationPreferenceImported.from(allocPref, username);
    return test ? event.getId() : publisher.publish(event);
  }

  @EventListener
  public void onAllocationPreferenceImported(final AllocationPreferenceImported event) {
    Customer customer =
        customerProfileService.getCustomerByCode(event.getAllocPref().getCustomerCode());

    List<Category> categories = categoryService.getCategoryByCodeIn(
        event.getAllocPref().getCategories());

    Fund fund = null;
    if (StringAffirm.of(event.getAllocPref().getFundCode()).hasText()) {
      String msg = String.format("Fund %s for customer %s not found",
          event.getAllocPref().getFundCode(), customer.getCode());
      fund = fundService.findByCodeAndCustomer(event.getAllocPref().getFundCode(), customer.getCode())
          .orElseThrow(() -> new IllegalStateException(msg));
    }

    AllocationPreference ap = AllocationPreference.builder()
      .allocationPreferenceId(AllocationPreferenceId.of(event.getId()))
      .status(AllocationPreferenceStatus.ACTIVE)
      .customer(customer)
      .fund(fund)
      .categories(categories)
      .customerReference(event.getAllocPref().getCustomerReference())
      .deliveryInstructions(event.getAllocPref().getDeliveryInstructions())
      .notes(event.getAllocPref().getNotes())
      .build();
    ap.setReleasePreferences(from(event.getAllocPref().getRelPrefInfo(), ap));
    allocPrefsRepo.save(ap);
  }

  private List<ReleasePreference> from(
      AllocationPrefInfo.ReleasePrefInfo relPrefInfo, AllocationPreference allocPref) {
    String cc = allocPref.getCustomer().getCode();
    return Optional.ofNullable(relPrefInfo)
        .map(ri -> {
          Fund fund = null;
          if (StringAffirm.of(ri.getFundCode()).hasText()) {
            String fc = ri.getFundCode();
            fund = fundService.findByCodeAndCustomer(fc, cc)
                    .orElse(null);
          }
          Fund hbFund = null;
          if (StringAffirm.of(ri.getHardbackFundCode()).hasText()) {
            hbFund = fundService.findByCodeAndCustomer(ri.getHardbackFundCode(), cc)
                    .orElse(null);
          }
          Fund pbFund = null;
          if (StringAffirm.of(ri.getHardbackFundCode()).hasText()) {
            pbFund = fundService.findByCodeAndCustomer(ri.getPaperbackFundCode(), cc)
                    .orElse(null);
          }
          return Arrays.asList(ReleasePreference.from(ri, fund, hbFund, pbFund, allocPref));
        })
        .orElse(new ArrayList<>());
  }

  private void validate(AllocationPrefInfo allocPref) {

    if (StringAffirm.of(allocPref.getFundCode()).hasText()) {
      fundService.findByCodeAndCustomer(allocPref.getFundCode(), allocPref.getCustomerCode());
    }

    customerProfileService.getCustomerByCode(allocPref.getCustomerCode());

    Optional.ofNullable(allocPref.getRelPrefInfo())
        .ifPresent(rp -> validate(rp, allocPref.getCustomerCode()));
  }

  private void validate(AllocationPrefInfo.ReleasePrefInfo relInfo, String customerCode) {
    Optional.ofNullable(relInfo.getReleaseType())
        .orElseThrow(() -> new BusinessException("Release preference type must not be null"));

    Optional.ofNullable(relInfo.getActionType())
        .orElseThrow(
            () -> new BusinessException("Release preference action type must not be null"));

    if (StringAffirm.of(relInfo.getFundCode()).hasText()) {
      fundService.findByCodeAndCustomer(relInfo.getFundCode(), customerCode);
    }
  }
}

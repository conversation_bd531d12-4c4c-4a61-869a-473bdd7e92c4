package au.com.peterpal.selecting.standingorders.gateways.lucy;

import au.com.peterpal.selecting.standingorders.gateways.lucy.dto.TitleOrderInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "lucy-api-service", url = "${lucy-api-service.url}")
public interface LucyApiClient {

  @PutMapping(
      path = "/orders",
      headers = "content-type=application/json",
      consumes = MediaType.APPLICATION_JSON_VALUE)
  TitleOrderInfo addTitleOrder(@RequestBody TitleOrderInfo titleOrderInfo);
}

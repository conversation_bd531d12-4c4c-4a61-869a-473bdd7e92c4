package au.com.peterpal.selecting.standingorders.pendingorder.dto;

import au.com.peterpal.selecting.standingorders.utils.CurrencyCode;
import lombok.Value;

import java.math.BigDecimal;
import java.time.LocalDate;

@Value
public class ProcessFormatResponse {
  String format;
  String isbn;
  Supplier supplier;
  BigDecimal price;
  CurrencyCode currencyCode;
  LocalDate publicationDate;

  @Value
  public static class Supplier {
    String id;
    String code;
    String name;
  }
}

package au.com.peterpal.selecting.standingorders.standingorder.model;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

public class EmptySOSummary implements SOSummary {

  @Override
  public UUID getId() {
    return null;
  }

  @Override
  public String getNumber() {
    return "";
  }

  @Override
  public List<Term> getTerms() {
    return Collections.EMPTY_LIST;
  }
}

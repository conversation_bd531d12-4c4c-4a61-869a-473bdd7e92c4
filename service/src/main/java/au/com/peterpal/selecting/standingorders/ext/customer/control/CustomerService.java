package au.com.peterpal.selecting.standingorders.ext.customer.control;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.ext.branch.control.BranchRepository;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.Branch;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Log4j2
@Service
@RequiredArgsConstructor
public class CustomerService {

  private final CustomerRepository customerRepository;
  private final BranchRepository branchRepo;

  public Customer findById(CustomerId customerId) {
    return customerRepository
        .findById(customerId)
        .orElseThrow(() -> new ResourceNotFoundException(Customer.class, String.valueOf(customerId)));
  }

  public Customer findByCustomerCode(String customerCode) {
    return customerRepository.findByCode(customerCode)
        .orElseThrow(() -> new ResourceNotFoundException(
            String.format("Customer with code %s not found",
                customerCode == null ? "null" : customerCode.isEmpty() ? "empty" : customerCode)));
  }

  public void validateIfExist(CustomerId customerId) {
    if (!customerRepository.findById(customerId).isPresent())
      throw new ResourceNotFoundException(Customer.class, String.valueOf(customerId));
  }

  public List<Branch> getBranches(CustomerId customerId) {
    return customerRepository.findById(customerId)
      .map(c -> branchRepo.findByCustomer(c))
      .orElseThrow(() -> new ResourceNotFoundException(
          String.format("Customer %s not found", customerId)
      ));
  }

  public Branch getBranch(BranchId branchId) {
    return branchRepo.findById(branchId)
      .orElseThrow(() -> new ResourceNotFoundException(
          String.format("Branch %s not found", branchId)
      ));
  }

  public List<Customer> getAll() {
    return customerRepository.findAll();
  }

  public List<Customer> getAll(Sort sort) {
    return sort == null ? getAll() : customerRepository.findAll(sort);
  }
}

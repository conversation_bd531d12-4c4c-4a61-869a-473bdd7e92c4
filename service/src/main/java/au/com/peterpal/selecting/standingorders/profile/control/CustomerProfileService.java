package au.com.peterpal.selecting.standingorders.profile.control;

import au.com.peterpal.selecting.standingorders.allocationpreference.control.AllocationPreferenceRepository;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.ReleasePreferenceRepository;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerRepository;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.persistence.EntityNotFoundException;

@Service
@Log4j2
public class CustomerProfileService {
  private final CustomerRepository customerRepository;
  private final ReleasePreferenceRepository relPrefRepo;
  private final FundRepository fundRepository;
  private final AllocationPreferenceRepository allocPrefRepo;

  public CustomerProfileService(
      CustomerRepository customerRepository,
      ReleasePreferenceRepository relPrefRepo,
      FundRepository fundRepository,
      AllocationPreferenceRepository allocPrefRepo) {
    this.customerRepository = customerRepository;
    this.relPrefRepo = relPrefRepo;
    this.fundRepository = fundRepository;
    this.allocPrefRepo = allocPrefRepo;
  }

  public Customer getCustomerByCode(String customerCode) {
    log.debug(() -> String.format("Find customer by code: %s", customerCode));

    return customerRepository
        .findByCode(customerCode)
        .orElseThrow(
            () ->
                new EntityNotFoundException(String.format("Customer %s not found", customerCode)));
  }
}

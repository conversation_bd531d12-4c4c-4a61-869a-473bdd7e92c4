package au.com.peterpal.selecting.standingorders.ext.itemtype.model;

import au.com.peterpal.common.ddd.ids.EntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;

@Embeddable
public class ItemTypeId extends EntityId {

  public static ItemTypeId of(@NotEmpty String id) {
    return new ItemTypeId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public ItemTypeId(@NotEmpty String id) {
    super(id);
  }

  public ItemTypeId() {}

  @Override
  public @NotEmpty String getId() {
    return super.getId();
  }
}

package au.com.peterpal.selecting.standingorders.allocation.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.SmallFormatPaperbackRule;
import au.com.peterpal.selecting.standingorders.ext.fund.dto.FundInfo;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Value;
import lombok.With;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class ReleaseInfo {

  @NotNull
  @NonNull
  private ReleaseId releaseId;

  @NotNull
  @NonNull
  private AllocationId allocationId;

  ReleasePreferenceId preferenceId;
  private ReleaseType releaseType;
  private ActionType actionType;
  private AssignmentRule initialAssignmentRule;
  private SmallFormatPaperbackRule smallFormatPaperbackRule;
  private FundInfo fund;
  private FundInfo hardbackfund;
  private FundInfo paperbackfund;
  private Integer quantity;
  private Integer hardbackQuantity;
  private Integer paperbackQuantity;
}

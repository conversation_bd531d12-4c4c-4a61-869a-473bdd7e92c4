package au.com.peterpal.selecting.standingorders.config;

import au.com.peterpal.selecting.standingorders.products.control.ProductService;
import au.com.peterpal.selecting.standingorders.titles.control.ProductMatchingBL;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductMatchInfo;
import javax.jms.ConnectionFactory;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.context.IntegrationContextUtils;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.dsl.channel.MessageChannels;
import org.springframework.integration.handler.LoggingHandler;
import org.springframework.integration.jms.dsl.Jms;
import org.springframework.messaging.MessageChannel;

@Log4j2
@Configuration
public class ProductListener {
  private static final String NEW_PRODUCT_MSG =
      "Received new product message -- Payload: %s Headers: %s";
  private static final String UPDATE_PRODUCT_MSG =
      "Received update product message -- Payload: %s Headers: %s";

  @Value("${match.products.queue.name:new-products-to-match}")
  private String newProductsQueueName;

  @Value("${match.products.queue.name:updated-products-to-match}")
  private String productUpdateQueueName;

  private final ConnectionFactory connectionFactory;
  private final ProductMatchingBL productMatchingBL;
  private final ProductService productService;

  public ProductListener(
      ConnectionFactory connectionFactory,
      ProductMatchingBL productMatchingBL,
      ProductService productService) {
    this.connectionFactory = connectionFactory;
    this.productMatchingBL = productMatchingBL;
    this.productService = productService;
  }

  @Bean
  public MessageChannel productChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow newProductsJmsListener() {
    return IntegrationFlows.from(
            Jms.messageDrivenChannelAdapter(this.connectionFactory)
                .destination(newProductsQueueName))
        .log(
            msg ->
                String.format(
                    "Received product message -- Payload: %s Headers: %s",
                    msg.getPayload(), msg.getHeaders()))
        .channel(productChannel())
        .get();
  }

  @Bean
  public IntegrationFlow newProductsFlow() {
    return IntegrationFlows.from(productChannel())
        .log(msg -> String.format(NEW_PRODUCT_MSG, msg.getPayload(), msg.getHeaders()))
        .transform(Transformers.fromJson(ProductMatchInfo.class))
        .wireTap(productCatalogueFlow())
        .<ProductMatchInfo>handle(
            ((payload, headers) -> {
              productMatchingBL.matchCatalogueProductAdd(payload);
              return null;
            }))
        .get();
  }

  @Bean
  public MessageChannel productUpdateChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow productUpdateJmsListener() {
    return IntegrationFlows.from(
            Jms.messageDrivenChannelAdapter(this.connectionFactory)
                .destination(productUpdateQueueName)
                .errorChannel(IntegrationContextUtils.ERROR_CHANNEL_BEAN_NAME))
        .log(
            msg ->
                String.format(
                    "Received product message -- Payload: %s Headers: %s",
                    msg.getPayload(), msg.getHeaders()))
        .channel(productUpdateChannel())
        .get();
  }

  @Bean
  public IntegrationFlow productUpdateFlow() {
    return IntegrationFlows.from(productUpdateChannel())
        .log(msg -> String.format(UPDATE_PRODUCT_MSG, msg.getPayload(), msg.getHeaders()))
        .transform(Transformers.fromJson(ProductMatchInfo.class))
        .wireTap(productCatalogueFlow())
        .<ProductMatchInfo>handle(
            (payload, headers) -> {
              productMatchingBL.matchCatalogueProductUpdate(payload, "system");
              return null;
            })
        .log()
        .get();
  }

  @Bean
  public IntegrationFlow productCatalogueFlow() {
    return flow ->
        flow.<ProductMatchInfo>handle((p, h) -> productService.saveProduct(p))
            .log(LoggingHandler.Level.DEBUG, message -> message);
  }
}

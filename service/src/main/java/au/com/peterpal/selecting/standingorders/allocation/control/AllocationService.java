package au.com.peterpal.selecting.standingorders.allocation.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocation.commands.AllocationSearchResponse;
import au.com.peterpal.selecting.standingorders.allocation.commands.SearchAllocationsByStandingOrder;
import au.com.peterpal.selecting.standingorders.allocation.dto.AllocationInfo;
import au.com.peterpal.selecting.standingorders.allocation.dto.BulkUpdateAllocationRequest;
import au.com.peterpal.selecting.standingorders.allocation.dto.SearchAllocationRequest;
import au.com.peterpal.selecting.standingorders.allocation.dto.SearchAllocationResponse;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.ext.category.control.CategoryRepository;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.customerstandingorder.events.AllocationUpdated;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundRepository;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Log4j2
@Service
@RequiredArgsConstructor
public class AllocationService {

  private final AllocationRepository allocationRepository;
  private final FundRepository fundRepository;
  private final CategoryRepository categoryRepository;
  private final EventPublisher publisher;
  private final MessageChannel rematchAllocationOutboundChannel;

  public Allocation findById(AllocationId allocationId) {
    Affirm.of(allocationId).notNull("AllocationId must not be null");

    return allocationRepository
        .findById(allocationId)
        .orElseThrow(
            () -> new ResourceNotFoundException(Allocation.class, String.valueOf(allocationId)));
  }

  public List<Allocation> findAllByBaParentAllocationId(AllocationId parentAllocationId) {
    return allocationRepository.findAllByBaParentAllocationId(parentAllocationId);
  }

  public List<Allocation> saveAll(List<Allocation> allocationList) {
    return allocationRepository.saveAll(allocationList);
  }

  public Allocation save(Allocation allocation) {
    log.info("allocation: {}", allocation);
    return allocationRepository.saveAndFlush(allocation);
  }

  public void validateIfExist(AllocationId allocationId) {
    if (!allocationRepository.findById(allocationId).isPresent())
      throw new ResourceNotFoundException(Allocation.class, String.valueOf(allocationId));
  }

  public Allocation findByCsoIdAndAllocationPrefId(
      CustomerStandingOrderId customerStandingOrderId,
      AllocationPreferenceId allocationPreferenceId) {
    return allocationRepository
        .findByAllocationPreference_AllocationPreferenceIdAndCustomerStandingOrder_CustomerStandingOrderId(
            allocationPreferenceId, customerStandingOrderId);
  }

  public Page<Allocation> search(SearchAllocationsByStandingOrder searchRequest) {
    return allocationRepository.search(searchRequest);
  }

  public Page<AllocationInfo> search(SearchAllocationRequest searchRequest, Pageable pageRequest) {
    Page<AllocationSearchResponse> pageResult =
        allocationRepository.search(searchRequest, pageRequest);
    return pageResult.map(
        a -> {
          List<Allocation> bd =
              a.getCustomerStandingOrder().getAllocationsByCategoryCodes(a.getCategories(), true);
          AllocationInfo ai = AllocationInfo.from(a);
          if (!bd.isEmpty()) {
            ai.setBranchDistributed(true);
          }
          return ai;
        });
  }

  public List<SearchAllocationResponse.CustomerResponse> findCustomersWithAllocation() {
    return allocationRepository.findCustomersWithAllocation();
  }

  public List<SearchAllocationResponse.FundResponse> getCustomerFunds(String customerCode) {
    return fundRepository.findByCustomerCode(customerCode).stream()
        .map(
            fund -> {
              SearchAllocationResponse.FundResponse fundResponse =
                  new SearchAllocationResponse.FundResponse();
              fundResponse.setFundId(fund.getFundId());
              fundResponse.setCode(fund.getCode());
              return fundResponse;
            })
        .collect(Collectors.toList());
  }

  public boolean hasAtLeastOneAllocation(StandingOrderId soId) {
    Affirm.of(soId).notNull("Standing order id must not be null");
    return !allocationRepository.getAllocations(soId, 1).isEmpty();
  }

  public StandingOrderStatus getNewSOStatus(StandingOrderId soId) {
    StandingOrderStatus result = StandingOrderStatus.INACTIVE;
    if (hasAtLeastOneAllocation(soId)) {
      result = StandingOrderStatus.ACTIVE;
    }
    return result;
  }

  public boolean isAllAllocationPaused(List<StandingOrderId> standingOrderIds) {
    List<Allocation> allocations =
        allocationRepository.getAllocationsByStandingOrderIds(standingOrderIds);

    return allocations.stream()
        .map(Allocation::getStatus)
        .filter(status -> status != AllocationStatus.INACTIVE)
        .allMatch(allocationStatus -> allocationStatus.equals(AllocationStatus.PAUSED));
  }

  public boolean isAllAllocationInactive(List<StandingOrderId> standingOrderIds) {
    List<Allocation> allocations =
        allocationRepository.getAllocationsByStandingOrderIds(standingOrderIds);

    return allocations.stream()
        .map(Allocation::getStatus)
        .allMatch(allocationStatus -> allocationStatus.equals(AllocationStatus.INACTIVE));
  }

  public Allocation updateAllocationQtys(AllocationId baAllocationId) {
    Allocation alloc = findById(baAllocationId);
    Release release = alloc.getRelease(ReleaseType.INITIAL);
    List<Allocation> existingBranches =
        allocationRepository.findAllByBaParentAllocationIdAndStatus(
            baAllocationId, AllocationStatus.ACTIVE);
    if (AssignmentRule.FIRST_AVAILABLE.equals(release.getInitialAssignmentRule())) {
      Integer total =
          existingBranches.stream()
              .map(ai -> getInitialRelease(ai).getQuantity())
              .reduce(0, Integer::sum);
      release.setQuantity(total);
      release.setHardbackQuantity(0);
      release.setPaperbackQuantity(0);
    } else if (AssignmentRule.SPLIT.equals(release.getInitialAssignmentRule())) {
      Integer hbq =
          existingBranches.stream()
              .map(ai -> getInitialRelease(ai).getHardbackQuantity())
              .reduce(0, Integer::sum);
      Integer pbq =
          existingBranches.stream()
              .map(ai -> getInitialRelease(ai).getPaperbackQuantity())
              .reduce(0, Integer::sum);
      release.setQuantity(hbq + pbq);
      release.setHardbackQuantity(hbq);
      release.setPaperbackQuantity(pbq);
    } else {
      throw new BusinessException("Invalid assignment rule");
    }

    return save(alloc);
  }

  private static Release getInitialRelease(Allocation ai) {
    return ai.getReleases().stream()
        .filter(r -> r.getReleaseType().equals(ReleaseType.INITIAL))
        .findFirst()
        .orElseThrow(
            () ->
                new ResourceNotFoundException(
                    String.format(
                        "Allocation %s does not have release of type INITIAL",
                        ai.getAllocationId())));
  }

  @Transactional
  public List<Allocation> bulkUpdate(BulkUpdateAllocationRequest request, String username) {
    List<Allocation> allocations;
    if (CollectionUtils.isNotEmpty(request.getIncludedAllocationIds())) {
      allocations = allocationRepository.findAllById(request.getIncludedAllocationIds());
    } else {
      allocations =
          allocationRepository.searchAllocation(
              request.getSearchRequest(), request.getExcludedAllocationIds());
    }
    final Fund fund =
        Objects.nonNull(request.getFundId())
            ? fundRepository
                .findById(request.getFundId())
                .orElseThrow(
                    () -> new ResourceNotFoundException(Fund.class, request.getFundId().toString()))
            : null;
    final List<Category> categories =
        CollectionUtils.isNotEmpty(request.getCategories())
            ? categoryRepository.findByCodeIn(request.getCategories())
            : new ArrayList<>();

    List<AllocationUpdated> allocationUpdatedEvents =
        allocations.stream()
            .map(
                allocation -> {
                  if (Objects.nonNull(fund)) {
                    allocation.setFund(fund);
                  }
                  if (CollectionUtils.isNotEmpty(categories)) {
                    allocation.setCategories(categories);
                  }
                  if (StringUtils.isNotBlank(request.getNotes())) {
                    allocation.setNotes(request.getNotes());
                  }
                  if (StringUtils.isNotBlank(request.getCollectionCode())) {
                    allocation.setCollectionCode(request.getCollectionCode());
                  }
                  if (StringUtils.isNotBlank(request.getCustomerReference())) {
                    allocation.setCustomerReference(request.getCustomerReference());
                  }
                  if (StringUtils.isNotBlank(request.getDeliveryInstructions())) {
                    allocation.setDeliveryInstructions(request.getDeliveryInstructions());
                  }
                  if (Objects.nonNull(request.getStatus())) {
                    allocation.setStatus(request.getStatus());
                  }
                  if (StringUtils.isNotBlank(request.getAppendNotes())) {
                    allocation.setNotes(
                        String.format(
                            "%s\n%s",
                            StringUtils.defaultString(allocation.getNotes()),
                            request.getAppendNotes()));
                  }
                  if (StringUtils.isNotBlank(request.getAppendDeliveryInstructions())) {
                    allocation.setDeliveryInstructions(
                        String.format(
                            "%s\n%s",
                            StringUtils.defaultString(allocation.getDeliveryInstructions()),
                            request.getAppendDeliveryInstructions()));
                  }

                  CustomerStandingOrder customerStandingOrder =
                      allocation.getCustomerStandingOrder();

                  List<Allocation> branches =
                      customerStandingOrder.getAllocations(allocation.getCategories(), true);
                  boolean hasNoBranches = branches.isEmpty();

                  Release initialRelease = allocation.findRelease(ReleaseType.INITIAL).orElse(null);
                  boolean quantityUpdated = false;
                  if (Objects.nonNull(initialRelease) && hasNoBranches) {
                    if (Objects.nonNull(request.getHbQty())) {
                      initialRelease.setHardbackQuantity(
                          getQuantity(
                              initialRelease.getInitialAssignmentRule(),
                              request.getHbQty(),
                              initialRelease.getHardbackQuantity()));
                      quantityUpdated = true;
                    }
                    if (Objects.nonNull(request.getPbQty())) {
                      initialRelease.setPaperbackQuantity(
                          getQuantity(
                              initialRelease.getInitialAssignmentRule(),
                              request.getPbQty(),
                              initialRelease.getPaperbackQuantity()));
                      quantityUpdated = true;
                    }
                    if (quantityUpdated) {
                      initialRelease.setQuantity(
                          getReleaseQuantity(
                              initialRelease.getInitialAssignmentRule(),
                              initialRelease.getHardbackQuantity(),
                              initialRelease.getPaperbackQuantity(),
                              initialRelease.getQuantity()));
                    }
                  }
                  allocation.setDateUpdated(LocalDateTime.now());
                  allocation = save(allocation);
                  boolean performRematch =
                      request.getRematchAllocationAfterUpdate() && quantityUpdated;
                  return AllocationUpdated.from(
                      allocation, false, false, false, performRematch, username);
                })
            .collect(Collectors.toList());
    allocationUpdatedEvents.forEach(publisher::publishEvent);
    performRematch(allocationUpdatedEvents);
    List<AllocationId> allocationIds =
        allocations.stream().map(Allocation::getAllocationId).collect(Collectors.toList());

    log.info("Updated allocation for allocation ids: {} with request {}", allocationIds, request);
    return allocations;
  }

  void performRematch(List<AllocationUpdated> allocationUpdatedEvents) {
    allocationUpdatedEvents.stream()
        .filter(AllocationUpdated::isRematchAllocationAfterUpdate)
        .map(DomainEvent::getId)
        .map(AllocationId::of)
        .forEach(
            allocationId ->
                rematchAllocationOutboundChannel.send(
                    MessageBuilder.withPayload(allocationId).build()));
  }

  private Integer getQuantity(
      AssignmentRule assignmentRule, Integer quantity, Integer defaultValue) {
    if (Objects.isNull(assignmentRule)) {
      return defaultValue;
    }
    if (Objects.requireNonNull(assignmentRule) == AssignmentRule.SPLIT) {
      return Optional.ofNullable(quantity).orElse(defaultValue);
    }
    return 0;
  }

  private Integer getReleaseQuantity(
      AssignmentRule assignmentRule, Integer hbQuantity, Integer pbQuantity, Integer defaultValue) {
    if (Objects.isNull(assignmentRule)) {
      return defaultValue;
    }
    if (assignmentRule == AssignmentRule.SPLIT) {
      return Objects.nonNull(pbQuantity) && Objects.nonNull(hbQuantity)
          ? pbQuantity + hbQuantity
          : defaultValue;
    }
    return defaultValue;
  }
}

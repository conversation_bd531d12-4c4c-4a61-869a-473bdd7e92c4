package au.com.peterpal.selecting.standingorders.titles.entity;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class ProductAggregatedId extends UuidEntityId {

  public static ProductAggregatedId of(@NotEmpty UUID id) {
    return new ProductAggregatedId(id);
  }

  public static ProductAggregatedId of(@NotEmpty String id) {
    return new ProductAggregatedId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public ProductAggregatedId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public ProductAggregatedId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public ProductAggregatedId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

package au.com.peterpal.selecting.standingorders.admin.dto;

import au.com.peterpal.selecting.standingorders.ext.budget.entity.BudgetId;
import au.com.peterpal.selecting.standingorders.ext.budget.entity.FundType;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.*;

@With
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SearchBudgetDetail {
  @NotNull @NonNull private FundId fundId;
  private BudgetId budgetId;
  private String fundCode;
  private String fundName;
  private FundType fundType;
  @Builder.Default private BigDecimal budgetAmount = BigDecimal.ZERO;
  @Builder.Default private BigDecimal topUpAmount = BigDecimal.ZERO;
  @Builder.Default private BigDecimal onOrderAmount = BigDecimal.ZERO;
  @Builder.Default private BigDecimal invoicedAmount = BigDecimal.ZERO;
  private String customerCode;
  private CustomerId customerId;
}

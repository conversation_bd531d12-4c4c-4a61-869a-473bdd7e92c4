package au.com.peterpal.selecting.standingorders.allocationpreference.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AddAllocationPreference;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class AllocationPreferenceAdded extends DomainEvent {

  private List<Category> categories;

  private String customerReference;

  private String deliveryInstructions;

  private String notes;

  private Fund fund;

  private Customer customer;

  public static AllocationPreferenceAdded from(
      AddAllocationPreference cmd, Fund fund, Customer customer, List<Category> categories,
      String username) {

    return AllocationPreferenceAdded.builder()
        .id(UUID.randomUUID())
        .customer(customer)
        .customerReference(cmd.getCustomerReference())
        .deliveryInstructions(cmd.getDeliveryInstructions())
        .notes(cmd.getNotes())
        .categories(categories)
        .fund(fund)
        .username(username)
        .build();
  }
}

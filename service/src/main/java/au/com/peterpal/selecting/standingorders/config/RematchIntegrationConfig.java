package au.com.peterpal.selecting.standingorders.config;

import au.com.peterpal.selecting.standingorders.titles.control.ProductMatchingBL;
import javax.jms.ConnectionFactory;

import au.com.peterpal.selecting.standingorders.titles.dto.RematchStandingOrderProductRequest;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.context.IntegrationContextUtils;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.dsl.channel.MessageChannels;
import org.springframework.integration.jms.dsl.Jms;
import org.springframework.messaging.MessageChannel;

@Log4j2
@Configuration
public class RematchIntegrationConfig {
  private static final String NEW_REMATCH_STANDING_ORDER_MSG =
      "Received new standing order rematch message -- Payload: %s Headers: %s";

  @Value("${rematch.standing-order-products.queue.name:rematch-standing-order-product}")
  private String rematchStandingOrderProduct;

  private final ConnectionFactory connectionFactory;
  private final ProductMatchingBL productMatchingBL;

  public RematchIntegrationConfig(
      ConnectionFactory connectionFactory, ProductMatchingBL productMatchingBL) {
    this.connectionFactory = connectionFactory;
    this.productMatchingBL = productMatchingBL;
  }

  @Bean("rematchStandingOrderOutboundChannel")
  public MessageChannel rematchStandingOrderOutboundChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow rematchStandingOrderOutboundFlow() {
    return IntegrationFlows.from(rematchStandingOrderOutboundChannel())
        .transform(Transformers.toJson())
        .handle(Jms.outboundAdapter(connectionFactory).destination(rematchStandingOrderProduct))
        .get();
  }

  @Bean
  public IntegrationFlow rematchStandingOrderInboundFlow() {
    return IntegrationFlows.from(
            Jms.messageDrivenChannelAdapter(this.connectionFactory)
                .destination(rematchStandingOrderProduct)
                .errorChannel(IntegrationContextUtils.ERROR_CHANNEL_BEAN_NAME))
        .log(
            msg ->
                String.format(
                    "Received rematch SO product message -- Payload: %s Headers: %s",
                    msg.getPayload(), msg.getHeaders()))
        .log(
            msg ->
                String.format(NEW_REMATCH_STANDING_ORDER_MSG, msg.getPayload(), msg.getHeaders()))
        .transform(Transformers.fromJson(RematchStandingOrderProductRequest.class))
        .<RematchStandingOrderProductRequest>handle(
            ((payload, headers) -> {
              productMatchingBL.rematchStandingOrder(payload, "system");
              return null;
            }))
        .get();
  }
}

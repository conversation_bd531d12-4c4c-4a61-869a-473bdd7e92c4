package au.com.peterpal.selecting.standingorders.products.control;

import au.com.peterpal.selecting.standingorders.products.entity.Product;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductMatchInfo;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Log4j2
@Service
public class ProductService {
  private final ProductRepository productRepository;

  public List<Product> findAllProduct() {
    return productRepository.findAll();
  }

  public Product saveProduct(ProductMatchInfo productMatchInfo) {
    Product product = getProduct(
        productMatchInfo);
    return productRepository.saveAndFlush(product);
  }

  public void addProducts(List<ProductMatchInfo> productMatchInfos) {
    productRepository.saveAll(
        productMatchInfos.stream()
            .map(productMatchInfo -> getProduct(productMatchInfo))
            .collect(Collectors.toList()));
  }

  private Product getProduct(ProductMatchInfo productMatchInfo) {
    return
        productRepository
            .findByProductReference(productMatchInfo.getProductReference())
            .map(p -> p.withLastImportDate(LocalDateTime.now()))
            .orElse(Product.from(productMatchInfo.getProductReference()));
  }
}

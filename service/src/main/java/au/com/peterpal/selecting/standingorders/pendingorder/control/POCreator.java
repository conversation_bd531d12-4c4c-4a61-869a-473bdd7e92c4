package au.com.peterpal.selecting.standingorders.pendingorder.control;

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.CopyPendingOrderDto;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.POCreateInfo;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;

import java.util.List;

public interface POCreator {
  List<PendingOrder> create(POCreateInfo info);

  List<PendingOrder> copy(CopyPendingOrderDto request);
}

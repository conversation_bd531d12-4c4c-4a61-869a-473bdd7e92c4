package au.com.peterpal.selecting.standingorders.ext.budget.boundary.dto;

import au.com.peterpal.selecting.standingorders.ext.budget.entity.BudgetTargetId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import javax.validation.constraints.NotNull;
import lombok.*;

@Data
@NoArgsConstructor(force = true)
@AllArgsConstructor(staticName = "of")
@Setter(AccessLevel.PRIVATE)
@Builder
public class BudgetTargetMessage {
  @NotNull private BudgetTargetId budgetTargetId;

  @NotNull private CustomerId customerId;

  @NotNull
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDate startDate;

  @NotNull
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDate endDate;

  @NotNull private Float julTarget;

  @NotNull private Float augTarget;

  @NotNull private Float sepTarget;

  @NotNull private Float octTarget;

  @NotNull private Float novTarget;

  @NotNull private Float decTarget;

  @NotNull private Float janTarget;

  @NotNull private Float febTarget;

  @NotNull private Float marTarget;

  @NotNull private Float aprTarget;

  @NotNull private Float mayTarget;

  @NotNull private Float junTarget;
}

package au.com.peterpal.selecting.standingorders.allocationpreference.model;

/**
 * The type of release.
 */
public enum ReleaseType {

  INITIAL("Initial"),
  REISSUE("Reissue"),
  FIRST_SMALL_FORMAT_PAPERBACK("First Small-Format Paperback");

  private final String description;

  ReleaseType(String description) {
    this.description = description;
  }

  public String description() {
    return description;
  }
}

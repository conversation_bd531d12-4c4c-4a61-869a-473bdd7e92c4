package au.com.peterpal.selecting.standingorders.titles.dto;

import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedTermTuple;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Set;

@Data
@EqualsAndHashCode
@NoArgsConstructor
public class TitleSearchDetail {

  private TitleId titleId;
  private String title;
  private String subtitle;
  private String personName;
  private String series;
  private String imprint;
  private TitleStatus titleStatus;
  private String category;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDateTime dateAdded;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDate deferredDate;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDate processedDate;

  private Set<String> isbns;
  private Set<MatchedTermTuple> matchedTerms;
  private Set<TitleStandingOrderAllocationCategory> standingOrderAllocationCategories;
  private Set<Category> allocationCategories;
}

package au.com.peterpal.selecting.standingorders.admin.dto;

import java.time.LocalDate;
import java.util.List;

import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import lombok.*;
import org.springframework.data.domain.Sort;

@Data
@Builder
@ToString
@Setter
public class SearchCustomerBudgetRequest {
  String customerCode;
  String staff;
  String sortByKey;
  LocalDate budgetStartDate;
  LocalDate budgetEndDate;
  List<CategoryId> categoryIds;
  @Builder.Default Sort.Direction sortByDirection = Sort.Direction.ASC;
}

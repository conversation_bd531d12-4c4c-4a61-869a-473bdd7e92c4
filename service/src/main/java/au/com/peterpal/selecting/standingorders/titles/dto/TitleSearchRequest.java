package au.com.peterpal.selecting.standingorders.titles.dto;

import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import lombok.*;
import org.springframework.data.domain.Pageable;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
public class TitleSearchRequest {
  private List<TitleStatus> statuses;
  @Builder.Default
  @JsonIgnore
  private List<TitleStatus> excludedStatuses = Lists.newArrayList(TitleStatus.PAUSED);
  private List<String> categories;
  private String text;
  private LocalDate startDate;
  private LocalDate endDate;
  private TitleDateType dateType;
  @JsonIgnore
  private String sortByKey;
  @JsonIgnore
  private String sortByDirection;

  @JsonIgnore
  @NonNull @NotNull private Pageable pageRequest;
}

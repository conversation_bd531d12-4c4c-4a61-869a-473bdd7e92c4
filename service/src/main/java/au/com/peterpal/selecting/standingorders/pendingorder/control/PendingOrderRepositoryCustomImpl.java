package au.com.peterpal.selecting.standingorders.pendingorder.control;

import au.com.peterpal.selecting.standingorders.allocation.model.*;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.QCustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.ext.category.entity.QCategory;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.QCustomer;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.QFund;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.QSupplier;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.Supplier;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.PendingOrderStandingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.ProcessFormatResponse;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import au.com.peterpal.selecting.standingorders.pendingorder.model.QPendingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.QStandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import au.com.peterpal.selecting.standingorders.utils.CurrencyCode;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
public class PendingOrderRepositoryCustomImpl implements PendingOrderRepositoryCustom {

  @PersistenceContext private final EntityManager entityManager;

  private QPendingOrder pendingOrder = QPendingOrder.pendingOrder;
  private QSupplier supplier = QSupplier.supplier;
  private QFund fund = QFund.fund;
  private QCustomer customer = QCustomer.customer;

  public PendingOrderRepositoryCustomImpl(EntityManager entityManager) {
    this.entityManager = entityManager;
  }

  @Override
  public Page<PendingOrder> searchAllByCustomer(CustomerId customerId, Pageable pageRequest) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    JPAQuery<PendingOrder> query =
        factory
            .selectFrom(pendingOrder)
            .innerJoin(pendingOrder.supplier, supplier)
            .innerJoin(pendingOrder.fund, fund)
            .innerJoin(pendingOrder.customer, customer)
            .where(buildCondition(customerId));

    return new PageImpl<>(
        applyPagingAndSorting(query, pageRequest).fetch(), pageRequest, query.fetchCount());
  }

  @Override
  public List<PendingOrderStandingOrder> searchAllByTitle(
      TitleId titleId, PendingOrderStatus status, String categoryCode) {

    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    QRelease release = QRelease.release;
    QAllocation allocation = QAllocation.allocation;
    QCategory category = QCategory.category;
    QCustomerStandingOrder customerStandingOrder = QCustomerStandingOrder.customerStandingOrder;
    QStandingOrder standingOrder = QStandingOrder.standingOrder;

    BooleanBuilder condition = new BooleanBuilder();
    if (Objects.nonNull(status)) {
      condition.and(pendingOrder.orderStatus.eq(status));
    }
    if (Objects.nonNull(categoryCode)) {
      condition.and(pendingOrder.category.code.eq(categoryCode));
    }
    condition.and(pendingOrder.title.titleId.eq(titleId));

    JPAQuery<PendingOrderStandingOrder> query =
        factory
            .select(
                Projections.bean(
                    PendingOrderStandingOrder.class,
                    pendingOrder,
                    standingOrder.standingOrderId,
                    standingOrder.standingOrderNumber,
                    standingOrder.description.as("standingDescription")))
            .from(pendingOrder)
            .innerJoin(pendingOrder.release, release)
            .innerJoin(release.allocation, allocation)
            .leftJoin(allocation.customerStandingOrder, customerStandingOrder)
            .leftJoin(customerStandingOrder.standingOrder, standingOrder)
            .leftJoin(allocation.categories, category)
            .on(category.eq(pendingOrder.category))
            .where(condition);

    return query.fetch();
  }

  @Override
  public List<ProcessFormatResponse> findByTitleGroupByFormat(TitleId titleId) {
    JPAQuery<PendingOrder> query = getPendingOrder(titleId, null, null);
    return query
        .select(
            pendingOrder.format,
            pendingOrder.orderedProductReference,
            supplier,
            pendingOrder.price,
            pendingOrder.currencyCode,
            pendingOrder.publicationDate)
        .leftJoin(pendingOrder.supplier, supplier)
        .groupBy(
            pendingOrder.format,
            pendingOrder.orderedProductReference,
            supplier,
            pendingOrder.price,
            pendingOrder.currencyCode,
            pendingOrder.publicationDate)
        .fetch()
        .stream()
        .map(
            t ->
                mapToResponse(
                    Objects.requireNonNull(t.get(pendingOrder.format)),
                    t.get(pendingOrder.orderedProductReference),
                    t.get(supplier),
                    t.get(pendingOrder.price),
                    t.get(pendingOrder.currencyCode),
                    t.get(pendingOrder.publicationDate)))
        .collect(Collectors.toList());
  }

  @Override
  public List<PendingOrder> findByTitleIdAndStandingOrderId(
      TitleId titleId, List<StandingOrderId> standingOrderIds) {

    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    QRelease release = QRelease.release;
    QAllocation allocation = QAllocation.allocation;
    QCustomerStandingOrder customerStandingOrder = QCustomerStandingOrder.customerStandingOrder;
    QStandingOrder standingOrder = QStandingOrder.standingOrder;

    JPAQuery<PendingOrder> query =
        factory
            .selectFrom(pendingOrder)
            .innerJoin(pendingOrder.release, release)
            .innerJoin(release.allocation, allocation)
            .innerJoin(allocation.customerStandingOrder, customerStandingOrder)
            .innerJoin(customerStandingOrder.standingOrder, standingOrder)
            .where(
                standingOrder
                    .standingOrderId
                    .in(standingOrderIds)
                    .and(pendingOrder.title.titleId.eq(titleId)));

    return query.fetch();
  }

  @Override
  public List<PendingOrder> findAllByTitleIdAndFormatAndAllocationId(
      TitleId titleId, ReleaseFormat format, AllocationId allocationId) {

    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    QRelease release = QRelease.release;

    JPAQuery<PendingOrder> query =
        factory
            .selectFrom(pendingOrder)
            .innerJoin(pendingOrder.release, release)
            .where(
                pendingOrder
                    .title
                    .titleId
                    .eq(titleId)
                    .and(pendingOrder.format.eq(format))
                    .and(release.allocation.allocationId.eq(allocationId)));

    return query.fetch();
  }

  @Override
  public List<PendingOrder> findAllByTitleIdAllocationId(
      TitleId titleId, AllocationId allocationId, PendingOrderStatus status) {

    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    QRelease release = QRelease.release;
    QAllocation allocation = QAllocation.allocation;

    BooleanExpression expression =
        pendingOrder.title.titleId.eq(titleId).and(allocation.allocationId.eq(allocationId));
    if (Objects.nonNull(status)) {
      expression = expression.and(pendingOrder.orderStatus.eq(status));
    }
    JPAQuery<PendingOrder> query =
        factory
            .selectFrom(pendingOrder)
            .innerJoin(pendingOrder.release, release)
            .innerJoin(release.allocation, allocation)
            .where(expression);

    return query.fetch();
  }

  @Override
  public List<PendingOrder> findAllByTitleIdsAllocationId(
      List<TitleId> titleIds, AllocationId allocationId) {

    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    QRelease release = QRelease.release;
    QAllocation allocation = QAllocation.allocation;

    JPAQuery<PendingOrder> query =
        factory
            .selectFrom(pendingOrder)
            .innerJoin(pendingOrder.release, release)
            .innerJoin(release.allocation, allocation)
            .where(
                pendingOrder
                    .title
                    .titleId
                    .in(titleIds)
                    .and(allocation.allocationId.eq(allocationId)));
    return query.fetch();
  }

  private ProcessFormatResponse mapToResponse(
      ReleaseFormat format,
      String isbn,
      Supplier supplier,
      BigDecimal price,
      CurrencyCode currencyCode,
      LocalDate publicationDate) {
    ProcessFormatResponse.Supplier supplierResponse =
        Optional.ofNullable(supplier)
            .map(
                sup ->
                    new ProcessFormatResponse.Supplier(
                        sup.getSupplierId().toString(), sup.getCode(), sup.getName()))
            .orElse(null);
    return new ProcessFormatResponse(
        format.name(), isbn, supplierResponse, price, currencyCode, publicationDate);
  }

  private JPAQuery<PendingOrder> getPendingOrder(
      TitleId titleId, PendingOrderStatus status, String categoryCode) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    QRelease release = QRelease.release;
    QAllocation allocation = QAllocation.allocation;
    QCategory category = QCategory.category;

    BooleanBuilder condition = new BooleanBuilder();
    if (Objects.nonNull(status)) {
      condition.and(pendingOrder.orderStatus.eq(status));
    }
    if (Objects.nonNull(categoryCode)) {
      condition.and(pendingOrder.category.code.eq(categoryCode));
    }
    condition.and(pendingOrder.title.titleId.eq(titleId));

    return factory
        .selectFrom(pendingOrder)
        .innerJoin(pendingOrder.release, release)
        .innerJoin(release.allocation, allocation)
        .leftJoin(allocation.categories, category)
        .on(category.eq(pendingOrder.category))
        .where(condition);
  }

  private JPAQuery<PendingOrder> applyPagingAndSorting(
      JPAQuery<PendingOrder> query, Pageable pageRequest) {
    if (Objects.nonNull(pageRequest)) {
      query.offset(pageRequest.getOffset()).limit(pageRequest.getPageSize());

      PathBuilder<PendingOrder> pathBuilder = new PathBuilder<>(PendingOrder.class, "pendingOrder");

      pageRequest
          .getSort()
          .forEach(
              order ->
                  query.orderBy(
                      new OrderSpecifier(
                          order.isAscending() ? Order.ASC : Order.DESC,
                          pathBuilder.get(order.getProperty()))));
    }
    return query;
  }

  private Predicate buildCondition(CustomerId customerId) {
    BooleanBuilder booleanBuilder = new BooleanBuilder();
    if (Objects.nonNull(customerId)) booleanBuilder.and(customer.customerId.eq(customerId));

    return booleanBuilder;
  }
}

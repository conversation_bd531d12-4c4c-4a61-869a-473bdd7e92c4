package au.com.peterpal.selecting.standingorders.allocationpreference.model;

public enum AssignmentRule {
  FIRST_AVAILABLE("First Available Format"),
  SPLIT("Always Split HB:PB");

  private final String text;

  AssignmentRule(String text) {
    this.text = text;
  }

  public static AssignmentRule valueAt(int i) {
    if (i < 0 || i > values().length -1) {
      throw new IllegalStateException(String.format("AssignmentRule of %d not found", i));
    }
    return values()[i];
  }

}

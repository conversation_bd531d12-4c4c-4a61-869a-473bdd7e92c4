package au.com.peterpal.selecting.standingorders.pendingorder.control;

import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PendingOrderRepository
    extends JpaRepository<PendingOrder, PendingOrderId>, PendingOrderRepositoryCustom {

  List<PendingOrder> findByOrderedProductReferenceAndTitleTitleId(String isbn, TitleId titleId);

  Optional<PendingOrder> findByPendingOrderId(PendingOrderId pendingOrderId);

  List<PendingOrder> findAllByPendingOrderIdIn(List<PendingOrderId> pendingOrderIds);

  List<PendingOrder> findByTitleTitleId(TitleId titleId);

  List<PendingOrder> findByTitleTitleIdAndOrderStatus(TitleId titleId, PendingOrderStatus status);
}

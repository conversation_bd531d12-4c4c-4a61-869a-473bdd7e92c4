package au.com.peterpal.selecting.standingorders.allocation.boundary;

import au.com.peterpal.selecting.standingorders.allocation.control.AllocationService;
import au.com.peterpal.selecting.standingorders.allocation.dto.*;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.customerstandingorder.control.CustomerStandingOrderBL;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.titles.control.RematchAllocationService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

@Log4j2
@RestController
@RequiredArgsConstructor
@SecurityRequirement(name = "BearerAuth")
@RequestMapping("/api/allocations")
public class AllocationController {

  private final AllocationService allocationService;
  private final CustomerStandingOrderBL customerStandingOrderBL;
  private final RematchAllocationService rematchAllocationService;

  @GetMapping("/search")
  public Page<AllocationInfo> search(
      @RequestParam(required = false) AllocationStatus status,
      @RequestParam(required = false) String customerCode,
      @RequestParam(required = false) List<String> category,
      @RequestParam(required = false) String fund,
      @RequestParam(required = false) String customerReference,
      @RequestParam(required = false) String description,
      @RequestParam(required = false) String term,
      @RequestParam(required = false) AllocationDateType dateType,
      @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate startDate,
      @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate endDate,
      @RequestParam(required = false) String keyword,
      @RequestParam(required = false) String sortByKey,
      @RequestParam(required = false) KeywordEntity keywordEntity,
      @RequestParam(required = false) String sortByDirection,
      @PageableDefault(
              direction = Sort.Direction.DESC,
              sort = "customerStandingOrder.standingOrder.description")
          Pageable pageRequest) {

    SearchAllocationRequest request =
        SearchAllocationRequest.builder()
            .status(status)
            .customerCode(customerCode)
            .categories(category)
            .fund(fund)
            .customerReference(customerReference)
            .description(description)
            .term(term)
            .dateType(dateType)
            .startDate(startDate)
            .endDate(endDate)
            .keyword(keyword)
            .keywordEntity(keywordEntity)
            .sortByKey(sortByKey)
            .sortByDirection(sortByDirection)
            .build();

    return allocationService.search(request, pageRequest);
  }

  @GetMapping("/customers")
  public List<SearchAllocationResponse.CustomerResponse> customers() {

    return allocationService.findCustomersWithAllocation();
  }

  @GetMapping("/customers/{customerCode}/funds")
  public List<SearchAllocationResponse.FundResponse> funds(@PathVariable String customerCode) {

    return allocationService.getCustomerFunds(customerCode);
  }

  @PutMapping(path = "bulk-update")
  public List<AllocationId> bulkUpdate(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestBody @Valid BulkUpdateAllocationRequest bulkUpdateAllocationRequest) {
    log.debug("Received request from user {}: {}", username, bulkUpdateAllocationRequest);
    List<Allocation> allocations = allocationService.bulkUpdate(bulkUpdateAllocationRequest, username);
    List<CustomerStandingOrder> customerStandingOrders = allocations.stream().map(Allocation::getCustomerStandingOrder).distinct().collect(Collectors.toList());
    customerStandingOrderBL.updateCustomerStandingOrderStatus(customerStandingOrders, username);

    return allocations.stream().map(Allocation::getAllocationId).collect(Collectors.toList());
  }

  @PostMapping(path = "rematch-with-titles/{allocationId}")
  public void rematchWithTitles(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable("allocationId") AllocationId allocationId) {
    rematchAllocationService.rematch(allocationId, username);
  }
}

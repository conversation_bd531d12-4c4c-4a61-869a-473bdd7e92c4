package au.com.peterpal.selecting.standingorders.products.boundary;

import au.com.peterpal.selecting.standingorders.products.control.ProductService;
import au.com.peterpal.selecting.standingorders.products.entity.Product;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductMatchInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

@Log4j2
@RestController
@RequiredArgsConstructor
@SecurityRequirement(name = "BearerAuth")
@RequestMapping("/api/products")
public class ProductController {

  private final ProductService productService;

  @PostMapping
  @Operation(summary = "Add one product info to product table")
  public Product add(@RequestBody @Valid ProductMatchInfo product) {
    return productService.saveProduct(product);
  }

  @PostMapping("/add-many")
  @Operation(summary = "Add many products info to product table")
  public void addMany(@RequestBody @Valid List<ProductMatchInfo> products) {
    productService.addProducts(products);
  }

}

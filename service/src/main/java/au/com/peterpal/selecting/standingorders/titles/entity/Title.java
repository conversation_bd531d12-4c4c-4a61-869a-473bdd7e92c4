package au.com.peterpal.selecting.standingorders.titles.entity;

import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.standingorder.dto.ProductInfo;
import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonType;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import lombok.*;
import org.apache.commons.lang3.BooleanUtils;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder(toBuilder = true)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "title")
public class Title {

  @ToString.Include @EqualsAndHashCode.Include @NonNull @EmbeddedId private TitleId titleId;

  @NonNull @NotEmpty private String title;

  private String subtitle;

  @NonNull @NotEmpty private String personName;

  private String series;

  private String imprint;

  @ManyToOne
  @JoinColumn(name = "category_id")
  private Category category;

  @Enumerated(EnumType.STRING)
  private TitleStatus titleStatus;

  @Enumerated(EnumType.STRING)
  private TitleType type;

  private LocalDate deferredDate;

  private LocalDate processedDate;

  private LocalDateTime dateAdded;

  private LocalDateTime dateModified;

  @OneToMany(mappedBy = "title", cascade = CascadeType.ALL, orphanRemoval = true)
  @JsonIgnore
  @Builder.Default
  private List<MatchedProduct> matchedProducts = new ArrayList<>();

  @OneToMany(mappedBy = "title", cascade = CascadeType.ALL, orphanRemoval = true)
  @JsonIgnore
  @Builder.Default
  private List<ProductAggregated> productAggregatedList = new ArrayList<>();

  @OneToMany(mappedBy = "title", cascade = CascadeType.ALL, orphanRemoval = true)
  @JsonIgnore
  @Builder.Default
  private List<StandingOrderAggregated> standingOrderAggregatedList = new ArrayList<>();

  @ManyToOne
  @JoinColumn(name = "rejectionReasonTypeId")
  private RejectionReasonType rejectionReasonType;

  private String rejectionOtherReason;
  @ToString.Include private String titleNumber;
  private Boolean manualDeferred;

  public void addMatchedProduct(MatchedProduct matchedProduct) {
    matchedProducts.add(matchedProduct);
  }

  public List<MatchedProduct> inactiveNoLongerMatchedProduct() {
    List<MatchedProduct> noLongerMatch =
        getActiveMatchProducts().stream()
            .filter(MatchedProduct::noMatchStandingOrders)
            .collect(Collectors.toList());

    for (MatchedProduct matchedProduct : noLongerMatch) {
      matchedProduct.setStatus(MatchedProductStatus.INACTIVE);
    }

    return noLongerMatch;
  }

  public void removeNoLongerMatchProductAggregate() {
    List<MatchedProductId> noLongerMatch =
        getActiveMatchProducts().stream()
            .filter(MatchedProduct::noMatchStandingOrders)
                .map(MatchedProduct::getMatchedProductId)
            .collect(Collectors.toList());

    productAggregatedList.stream()
            .filter(p -> noLongerMatch.contains(p.getMatchedProduct().getMatchedProductId()))
            .forEach(p -> p.setStatus(ProductAggregatedStatus.REJECTED));
  }

  public void addProductAggregated(ProductAggregated productAggregated) {
    productAggregatedList.add(productAggregated);
  }

  public void addStandingOrderAggregated(StandingOrderAggregated standingOrderAggregated) {
    standingOrderAggregatedList.add(standingOrderAggregated);
  }

  public void removeNoLongerMatchStandingOrderAggregate() {
    List<StandingOrderId> noLongerMatch =
        getMatchedProducts().stream()
            .flatMap(matchedProduct -> matchedProduct.getMatchedStandingOrders().stream())
            .filter(MatchedStandingOrder::isInactive)
            .map(MatchedStandingOrder::getStandingOrder)
            .map(StandingOrder::getStandingOrderId)
            .collect(Collectors.toList());

    getStandingOrderAggregatedList().stream()
        .filter(so -> noLongerMatch.contains(so.getStandingOrder().getStandingOrderId()))
        .forEach(so -> so.setStatus(StandingOrderAggregatedStatus.REJECTED));
  }

  public Optional<StandingOrderAggregated> findMatchedStandingOrder(
      StandingOrderId standingOrderId) {
    return standingOrderAggregatedList.stream()
        .filter(soa -> soa.getStandingOrder().getStandingOrderId().equals(standingOrderId))
        .findAny();
  }

  public Optional<MatchedProduct> findActiveMatchedProductByIsbn(String isbn) {
    return this.getActiveMatchProducts().stream().filter(tm -> tm.getIsbn().equals(isbn)).findAny();
  }

  public Optional<MatchedProduct> findAnyMatchedProductByIsbn(String isbn) {
    return this.getMatchedProducts().stream().filter(tm -> tm.getIsbn().equals(isbn)).findAny();
  }

  public boolean isNew() {
    return titleStatus == TitleStatus.NEW;
  }

  public boolean isPending() {
    return titleStatus == TitleStatus.PENDING;
  }

  public boolean isProcessed() {
    return titleStatus == TitleStatus.PROCESSED;
  }

  public boolean isDeferred() {
    return titleStatus == TitleStatus.DEFERRED;
  }

  public boolean isManuallyDeferred() {
    return titleStatus == TitleStatus.DEFERRED && BooleanUtils.toBoolean(manualDeferred);
  }

  public boolean isRejected() {
    return titleStatus == TitleStatus.REJECTED;
  }

  public boolean isPaused() {
    return titleStatus == TitleStatus.PAUSED;
  }

  public Optional<MatchedProduct> getMatchedProductWithEarliestPubDate() {
    return matchedProducts.stream()
        .min(Comparator.comparing(MatchedProduct::getPublicationDate, LocalDate::compareTo));
  }

  public boolean isTitlePendingOrProcessed() {
    return Lists.newArrayList(TitleStatus.PENDING, TitleStatus.PROCESSED)
        .contains(this.titleStatus);
  }

  public boolean isNotProcessedOrRejected() {
    return Lists.newArrayList(
            TitleStatus.NEW, TitleStatus.PENDING, TitleStatus.DEFERRED, TitleStatus.PAUSED)
        .contains(titleStatus);
  }

  @JsonIgnore
  public List<MatchedProduct> getActiveMatchProducts() {
    return getMatchedProducts().stream()
        .filter(MatchedProduct::isActive)
        .collect(Collectors.toList());
  }

  public List<ProductAggregated> getAcceptedProducts() {
    return getProductAggregatedList().stream()
        .filter(p -> p.getStatus() == ProductAggregatedStatus.ACCEPTED)
        .collect(Collectors.toList());
  }

  public List<StandingOrderAggregated> getAcceptedStandingOrders() {
    return getStandingOrderAggregatedList().stream()
        .filter(so -> so.getStatus() == StandingOrderAggregatedStatus.ACCEPTED)
        .collect(Collectors.toList());
  }

  @JsonIgnore
  public boolean isOriginal() {
    return type == TitleType.ORIGINAL;
  }

  @JsonIgnore
  public boolean isRelatedTitle() {
    return type == TitleType.RELATED;
  }

  public static Title copy(Title originalTitle, String titleNumber) {
    return Title.builder()
        .titleId(new TitleId())
        .title(originalTitle.getTitle())
        .subtitle(originalTitle.getSubtitle())
        .personName(originalTitle.getPersonName())
        .series(originalTitle.getSeries())
        .imprint(originalTitle.getImprint())
        .category(originalTitle.getCategory())
        .titleStatus(TitleStatus.PAUSED)
        .type(TitleType.RELATED)
        .dateAdded(LocalDateTime.now())
        .dateModified(LocalDateTime.now())
        .titleNumber(titleNumber)
        .build();
  }

  public List<StandingOrderId> getStandingOrderAggregatedIds() {
    return getStandingOrderAggregatedList().stream()
        .map(StandingOrderAggregated::getStandingOrder)
        .map(StandingOrder::getStandingOrderId)
        .collect(Collectors.toList());
  }

  public List<ProductInfo> getProductInfoFromAcceptedProducts() {
    return getAcceptedProducts().stream()
        .map(ProductAggregated::toProductInfo)
        .collect(Collectors.toList());
  }
}

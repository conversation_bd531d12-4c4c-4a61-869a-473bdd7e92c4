package au.com.peterpal.selecting.standingorders.allocationpreference.dto;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import javax.persistence.Entity;
import javax.persistence.Id;
import lombok.Data;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;
import org.hibernate.annotations.Synchronize;

@Entity
@Data
@Subselect(
    "select ap.id as id, min(c.code) as category from allocation_preference ap \n"
        + "inner join allocation_preference_categories apc  on ap.id =apc.allocation_preference_id  \n"
        + "inner join category c on apc.categories_id  = c.id \n"
        + "group by ap.id")
@Synchronize({"allocation_preference", "allocation_preference_categories", "category"})
@Immutable
public class AllocationPreferenceCategoryAgg {
  @Id private AllocationPreferenceId allocationPreferenceId;
  private String category;
}

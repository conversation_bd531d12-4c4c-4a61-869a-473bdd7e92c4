@startuml

/' diagram meta data start
config=CallConfiguration;
{
  "rootMethod": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController#categoryMappings(String,List\u003cString\u003e)",
  "projectClassification": {
    "searchMode": "OpenProject", // OpenProject, AllProjects
    "includedProjects": "",
    "pathEndKeywords": "*.impl",
    "isClientPath": "",
    "isClientName": "",
    "isTestPath": "",
    "isTestName": "",
    "isMappingPath": "",
    "isMappingName": "",
    "isDataAccessPath": "",
    "isDataAccessName": "",
    "isDataStructurePath": "",
    "isDataStructureName": "",
    "isInterfaceStructuresPath": "",
    "isInterfaceStructuresName": "",
    "isEntryPointPath": "",
    "isEntryPointName": ""
  },
  "graphRestriction": {
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "removeByInheritance": "", // inheritance/annotation based filtering is done in a second step
    "removeByAnnotation": "",
    "removeByClassPackage": "", // cleanup the graph after inheritance/annotation based filtering is done
    "removeByClassName": "",
    "cutMappings": false,
    "cutEnum": true,
    "cutTests": true,
    "cutClient": true,
    "cutDataAccess": true,
    "cutInterfaceStructures": true,
    "cutDataStructures": true,
    "cutGetterAndSetter": true,
    "cutConstructors": true
  },
  "graphTraversal": {
    "forwardDepth": 3,
    "backwardDepth": 3,
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "hideMappings": false,
    "hideDataStructures": false,
    "hidePrivateMethods": true,
    "hideInterfaceCalls": true, // indirection: implementation -> interface (is hidden) -> implementation
    "onlyShowApplicationEntryPoints": false // root node is included
  },
  "details": {
    "aggregation": "GroupByClass", // ByClass, GroupByClass, None
    "showMethodParametersTypes": false,
    "showMethodParametersNames": false,
    "showMethodReturnType": false,
    "showPackageLevels": 2,
    "showCallOrder": false,
    "edgeMode": "MethodsOnly", // TypesOnly, MethodsOnly, TypesAndMethods, MethodsAndDirectTypeUsage
    "showDetailedClassStructure": false
  },
  "rootClass": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController"
}
diagram meta data end '/



digraph g {
    rankdir="LR"
    splines=polyline


'nodes
subgraph cluster_98689 {
   	label=com
	labeljust=l
	fillcolor="#ececec"
	style=filled

   subgraph cluster_1300071644 {
   	label=peterpal
	labeljust=l
	fillcolor="#d8d8d8"
	style=filled

   subgraph cluster_125360093 {
   	label=CategoryService
	labeljust=l
	fillcolor=white
	style=filled

   CategoryService1335247380XXXsetCategoryMappings1839184593[
	label="+ setCategoryMappings()"
	style=filled
	fillcolor=white
	tooltip="CategoryService

&#10;  Replace current category mapping.&#10;  @param mappingList new category mappings&#10;  @return the new category mappings list&#10; "
	fontcolor=darkgreen
];
}

subgraph cluster_1774816029 {
   	label=StandingOrderController
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderController1189585491XXXcategoryMappings841244436[
	label="+ categoryMappings()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderController

null"
	penwidth=4
	fontcolor=darkgreen
];
}

subgraph cluster_1867065251 {
   	label=Affirm
	labeljust=l
	fillcolor=white
	style=filled

   Affirm1046088404XXXnotNull1808118735[
	label="+ notNull()"
	style=filled
	fillcolor=white
	tooltip="Affirm

&#10;  Affirm that the object is not {@code null}&#10;  \<pre\>&#10;  Validator.of(value).notNull(\"The value must not be null\");&#10;  \</pre\>&#10;  @param message&#10; "
	fontcolor=darkgreen
];

Affirm1046088404XXXof1939501217[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="Affirm

null"
	fontcolor=darkgreen
];
}

subgraph cluster_2069698174 {
   	label=CategoryMapping
	labeljust=l
	fillcolor=white
	style=filled

   CategoryMapping843015776XXXof1845361644[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="CategoryMapping

null"
	fontcolor=darkgreen
];
}
}
}

'edges
CategoryService1335247380XXXsetCategoryMappings1839184593 -> Affirm1046088404XXXnotNull1808118735;
CategoryService1335247380XXXsetCategoryMappings1839184593 -> Affirm1046088404XXXof1939501217;
CategoryService1335247380XXXsetCategoryMappings1839184593 -> CategoryMapping843015776XXXof1845361644;
StandingOrderController1189585491XXXcategoryMappings841244436 -> CategoryService1335247380XXXsetCategoryMappings1839184593;

}
@enduml

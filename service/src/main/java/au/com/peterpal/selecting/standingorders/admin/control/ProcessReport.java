package au.com.peterpal.selecting.standingorders.admin.control;

import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;

@Data
@Log4j2
@NoArgsConstructor
public class ProcessReport {
  private Integer total;
  private Integer imported;
  private Integer failed;
  private Long totalTime;
  private List<String> errors = new ArrayList<>();
}

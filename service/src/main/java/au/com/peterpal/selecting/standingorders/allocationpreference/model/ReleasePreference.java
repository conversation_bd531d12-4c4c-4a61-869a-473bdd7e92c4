package au.com.peterpal.selecting.standingorders.allocationpreference.model;

import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AllocationPrefInfo.ReleasePrefInfo;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.ReleasePreferenceInfo;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Optional;
import lombok.*;

import javax.persistence.*;
import java.util.UUID;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "release_preference")
public class ReleasePreference {

  @EqualsAndHashCode.Include @NonNull @EmbeddedId private ReleasePreferenceId releasePreferenceId;

  @ManyToOne
  @JoinColumn(name = "allocationPreferenceId")
  @JsonIgnore
  private AllocationPreference allocationPreference;

  @Enumerated(EnumType.STRING)
  private ReleaseType releaseType;

  @Enumerated(EnumType.STRING)
  private ActionType actionType;

  @ManyToOne
  // @JoinColumn(name = "fundId")
  private Fund fund;

  @ManyToOne
  @JoinColumn(name = "hard_back_fund_id")
  private Fund hardbackfund;

  @ManyToOne
  @JoinColumn(name = "paper_back_fund_id")
  private Fund paperbackfund;

  @Enumerated(EnumType.STRING)
  private AssignmentRule initialAssignmentRule;

  @Enumerated(EnumType.STRING)
  private SmallFormatPaperbackRule smallFormatPaperbackRule;

  public static ReleasePreference from(
      AllocationPreference allocPref,
      Fund fund,
      Fund hbFund,
      Fund pbFund,
      ReleasePreferenceInfo info) {
    return ReleasePreference.builder()
        .releasePreferenceId(ReleasePreferenceId.of(UUID.randomUUID()))
        .actionType(ActionType.valueOf(info.getActionType()))
        .initialAssignmentRule(AssignmentRule.valueOf(info.getInitialAssignmentRule()))
        .paperbackfund(pbFund)
        .hardbackfund(hbFund)
        .releaseType(info.getReleaseType())
        .smallFormatPaperbackRule(SmallFormatPaperbackRule.valueOf(info.getSmallFormatPaperbackRule()))
        .allocationPreference(allocPref)
        .fund(fund)
        .build();
  }

  public static ReleasePreference from(
      ReleasePrefInfo ri, Fund fund, Fund hbFund, Fund pbFund, AllocationPreference allocPref) {
    return ReleasePreference.builder()
        .releasePreferenceId(ReleasePreferenceId.of(ri.getId()))
        .actionType(ri.getActionType())
        .releaseType(ri.getReleaseType())
        .fund(fund)
        .hardbackfund(hbFund)
        .paperbackfund(pbFund)
        .allocationPreference(allocPref)
        .build();
  }

  public Fund getFund(ReleaseFormat format) {
    if (format == null) {
      return null;
    }
    switch (format) {
      case HB:
        return Optional.ofNullable(hardbackfund).orElse(fund);
      case PB:
        return Optional.ofNullable(paperbackfund).orElse(fund);
      default:
        return fund;
    }
  }
}

package au.com.peterpal.selecting.standingorders.standingorder.dto;

import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonStatus;
import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonType;
import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonTypeId;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class RejectionReasonTypeInfo {

  @NonNull @NotNull
  private String id;

  @NonNull @NotNull private String name;

  @NonNull
  @NotNull
  private RejectionReasonStatus status;

  public static List<RejectionReasonTypeInfo> from(List<RejectionReasonType> rejectionReasonTypeList) {
    return Optional.ofNullable(rejectionReasonTypeList)
      .map(List::stream)
      .orElseGet(Stream::empty)
      .map(RejectionReasonTypeInfo::from)
      .filter(Objects::nonNull)
      .collect(Collectors.toList());
  }

  public static RejectionReasonTypeInfo from(RejectionReasonType rejectionReasonType) {
    Affirm.of(rejectionReasonType).notNull("rejectionReasonType must not be null");

    return RejectionReasonTypeInfo.builder()
        .id(rejectionReasonType.getRejectionReasonTypeId().getId())
        .name(rejectionReasonType.getName())
        .status(rejectionReasonType.getStatus())
        .build();
  }
}

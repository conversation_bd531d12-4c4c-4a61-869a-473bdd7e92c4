package au.com.peterpal.selecting.standingorders.pendingorder.dto;

import au.com.peterpal.selecting.standingorders.utils.CurrencyCode;
import lombok.Value;

import java.math.BigDecimal;
import java.time.LocalDate;

@Value
public class ProductDetailDto {
  String isbn;
  String format;
  String edition;
  String publisher;
  LocalDate publicationDate;
  BigDecimal price;
  CurrencyCode currencyCode;
  Supplier supplier;

  @Value
  public static class Supplier {
    String label;
    String value;
  }
}

package au.com.peterpal.selecting.standingorders.ext.stockitem.boundary.dto;

import au.com.peterpal.selecting.standingorders.ext.stockitem.entity.StockItemId;
import lombok.*;

import java.util.UUID;

@With
@Value
@Builder
@Setter(AccessLevel.PRIVATE)
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
public class StockItemMessage {
  private StockItemId stockItemId;
  private String itemTypeId;
  private String productReference;
  private String author;
  private String titlePrefix;
  private String titleWithoutPrefix;
  private String seriesTitle;
  private Integer seriesVolumeNumber;
  private Integer yearOfPublication;
  private String mediaCode;
  private String publisherName;
  private String format;
}

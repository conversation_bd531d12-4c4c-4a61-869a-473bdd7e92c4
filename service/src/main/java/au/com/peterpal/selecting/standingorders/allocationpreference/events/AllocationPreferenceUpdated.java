package au.com.peterpal.selecting.standingorders.allocationpreference.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class AllocationPreferenceUpdated extends DomainEvent {

  private List<CategoryId> categoryIds;

  private String customerReference;

  private String deliveryInstructions;

  private String notes;

  private FundId fundId;

  private CustomerId customerId;

  public static AllocationPreferenceUpdated from(
      AllocationPreference allocationPreference, String username) {

    Affirm.of(allocationPreference).notNull("AllocationPreference must not be null");
    Affirm.of(allocationPreference.getAllocationPreferenceId())
        .notNull("AllocationPreferenceId must not be null");

    return AllocationPreferenceUpdated.builder()
        .id(allocationPreference.getAllocationPreferenceId().getId())
        .customerReference(allocationPreference.getCustomerReference())
        .deliveryInstructions(allocationPreference.getDeliveryInstructions())
        .notes(allocationPreference.getNotes())
        .categoryIds(
            allocationPreference.getCategories().stream().map(e -> e.getCategoryId()).collect(
                Collectors.toList()))
        .fundId(
            Optional.ofNullable(allocationPreference.getFund())
                .map(f -> f.getFundId())
                .orElse(null))
        .customerId(
            Optional.ofNullable(allocationPreference.getCustomer())
                .map(c -> c.getCustomerId())
                .orElse(null))
        .username(username)
        .build();
  }
}

package au.com.peterpal.selecting.standingorders.customerstandingorder.control;

import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerStandingOrderRepository extends
    JpaRepository<CustomerStandingOrder, CustomerStandingOrderId> {

  Optional<CustomerStandingOrder> findByCustomerAndStandingOrder(Customer customer,
      StandingOrder standingOrder);

  List<CustomerStandingOrder> findByStandingOrderAndStandingOrderStandingOrderStatus(
      StandingOrder standingOrder, StandingOrderStatus status);

  List<CustomerStandingOrder> findAllByStandingOrderStandingOrderId(
      StandingOrderId standingOrderId);

  List<CustomerStandingOrder> findByCustomerAndCustomerStandingOrderStatus(Customer customer,
      StandingOrderStatus standingOrderStatus);
}

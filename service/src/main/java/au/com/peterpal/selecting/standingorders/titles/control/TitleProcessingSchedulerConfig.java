package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@RequiredArgsConstructor
@Configuration
@EnableScheduling
public class TitleProcessingSchedulerConfig {
  private final TitleRepository titleRepository;
  private final TitleBL titleBL;

  @Scheduled(cron = "${title.un-defer.scheduled.cron}")
  public void unDeferTitleScheduler() {
    log.debug("unDeferTitleScheduler is running ...");
    LocalDate now = LocalDate.now();
    List<Title> titles =
        titleRepository.findAllByTitleStatus(TitleStatus.DEFERRED).stream()
            .filter(
                title ->
                    title.getDeferredDate().isEqual(now) || title.getDeferredDate().isBefore(now))
            .map(title -> titleBL.unDeferTitle(title, "scheduler"))
            .collect(Collectors.toList());
    log.debug(String.format("unDeferTitleScheduler finished. title affected: %s", titles));
  }
}

package au.com.peterpal.selecting.standingorders.ext.customer.entity;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class CustomerId extends UuidEntityId {

  public static CustomerId of(@NotEmpty UUID id) {
    return new CustomerId(id);
  }

  public static CustomerId of(@NotEmpty String id) {
    return new CustomerId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public CustomerId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public CustomerId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public CustomerId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

package au.com.peterpal.selecting.standingorders.standingorder.model;

import au.com.peterpal.common.ddd.ids.EntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;

@Embeddable
public class RejectionReasonTypeId extends EntityId {

  public static RejectionReasonTypeId of(@NotEmpty String id) {
    return new RejectionReasonTypeId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public RejectionReasonTypeId(@NotEmpty String id) {
    super(id);
  }

  public RejectionReasonTypeId() {}

  @Override
  public @NotEmpty String getId() {
    return super.getId();
  }
}

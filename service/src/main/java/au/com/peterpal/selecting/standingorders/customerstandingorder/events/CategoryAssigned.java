package au.com.peterpal.selecting.standingorders.customerstandingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class CategoryAssigned extends DomainEvent {

  private Allocation allocation;

  List<CategoryId> categoryIds;

  public static CategoryAssigned from(Allocation allocation, List<Category> categories,
      String username) {

    return CategoryAssigned.builder()
        .id(allocation.getAllocationId().getId())
        .allocation(allocation)
        .categoryIds(categories.stream().map(Category::getCategoryId).collect(Collectors.toList()))
        .username(username)
        .build();
  }
}

package au.com.peterpal.selecting.standingorders.titles.boundary;

import au.com.peterpal.selecting.standingorders.titles.control.*;
import au.com.peterpal.selecting.standingorders.titles.dto.*;
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProductId;
import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Log4j2
@RestController
@RequiredArgsConstructor
@SecurityRequirement(name = "BearerAuth")
@RequestMapping("/api/titles")
public class TitleController {

  private final TitleBL titleBL;
  private final RelatedTitleBL relatedTitleBL;
  private final TitleService titleService;
  private final MatchAndMergeTitleService matchAndMergeTitleService;
  private final ProductMatchingBL productMatchingBL;

  @GetMapping("search")
  public TitleSearchResponse search(
      @RequestParam(required = false) TitleStatus status,
      @RequestParam(required = false) List<String> categories,
      @RequestParam(required = false) String text,
      @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate startDate,
      @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate endDate,
      @RequestParam(required = false) TitleDateType dateType,
      @RequestParam(required = false) String sortByKey,
      @RequestParam(required = false) String sortByDirection,
      @PageableDefault(direction = Sort.Direction.DESC, sort = "dateAdded") Pageable pageRequest) {
    return titleService.search(
        TitleSearchRequest.builder()
            .statuses(Optional.ofNullable(status).map(List::of).orElse(new ArrayList<>()))
            .categories(categories)
            .pageRequest(pageRequest)
            .text(StringUtils.trim(text))
            .startDate(startDate)
            .endDate(endDate)
            .dateType(dateType)
            .sortByKey(sortByKey)
            .sortByDirection(sortByDirection)
            .build());
  }

  @GetMapping("{id}")
  public TitleDetailResponse findById(@PathVariable UUID id) {
    return titleService.findTitleDetailById(TitleId.of(id));
  }

  @PostMapping("{titleId}/matched-products/update-format")
  public ResponseEntity<MatchedProductId> updateFormat(
      @PathVariable UUID titleId, @RequestBody @Valid UpdateMatchedProductFormatRequest request) {

    titleBL.updateMatchedProductFormat(TitleId.of(titleId), request);
    return ResponseEntity.ok(request.getMatchedProductId());
  }

  @PostMapping("{titleId}/merge")
  public ResponseEntity<TitleId> merge(@PathVariable UUID titleId) {
    Title title = matchAndMergeTitleService.merge(TitleId.of(titleId));
    return ResponseEntity.ok(title.getTitleId());
  }

  @PostMapping("{titleId}/re-match")
  public void reMatch(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable UUID titleId) {
    productMatchingBL.rematchTitle(TitleId.of(titleId), username);
  }

  @PostMapping("re-match")
  public void reMatchAll(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestParam(required = false) TitleStatus status,
      @RequestParam(required = false) List<String> categories,
      @RequestParam(required = false) String text) {
    if (status == TitleStatus.PROCESSED || status == TitleStatus.REJECTED) {
      throw new IllegalArgumentException("Invalid status. Expected NEW or PENDING or DEFERRED.");
    }
    productMatchingBL.rematchTitlesBySearchCategory(
        status, categories, StringUtils.trim(text), username);
  }

  @PostMapping("process")
  public ResponseEntity<TitleId> processTitle(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestBody @Valid ProcessTitle request) {
    titleBL.processTitle(request, username);
    return ResponseEntity.ok(request.getTitleId());
  }

  @PostMapping(path = "{titleId}/reject")
  public void reject(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable UUID titleId,
      @RequestBody @Valid RejectTitleRequest rejectTitleRequest) {

    titleBL.rejectTitle(TitleId.of(titleId), rejectTitleRequest, username);
  }

  @PostMapping(path = "{titleId}/update-category")
  public void reject(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable UUID titleId,
      @RequestBody @Valid String category) {

    titleService.updateCategory(TitleId.of(titleId), category, username);
  }

  @PostMapping(path = "{titleId}/defer")
  public void defer(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable UUID titleId,
      @RequestParam Integer monthsDeferred) {
    titleBL.deferTitle(TitleId.of(titleId), LocalDate.now().plusMonths(monthsDeferred), true, username);
  }

  @PostMapping(path = "{titleId}/un-defer")
  public void defer(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable UUID titleId) {
    titleBL.unDeferTitle(TitleId.of(titleId), username);
  }

  @PostMapping(path = "{titleId}/undo")
  public TitleId undo(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable UUID titleId) {
    return titleBL.undoTitle(TitleId.of(titleId), username).getTitleId();
  }

  @PostMapping(path = "{titleId}/create-related-title")
  public TitleId createRelatedTitleFrom(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable UUID titleId) {
    Title title =
        relatedTitleBL.handle(
            CreateRelatedTitleForPausedAllocation.from(TitleId.of(titleId)), username);
    return Optional.ofNullable(title).map(Title::getTitleId).orElse(null);
  }

  @PutMapping(path = "bulk-edit")
  public List<TitleId> bulkEdit(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestBody @Valid BulkUpdateTitleRequest bulkUpdateTitleRequest) {
    log.debug("Received request from user {}: {}", username, bulkUpdateTitleRequest);
    return titleBL.bulkEdit(bulkUpdateTitleRequest, username);
  }

  @PostMapping("mark-title-as-processed")
  public ResponseEntity<String> markTitleProcessed(@RequestBody @NotEmpty List<TitleId> titleIds) {
    titleIds.forEach(titleService::markTitleAsProcessed);
    return ResponseEntity.ok("OK");
  }
}

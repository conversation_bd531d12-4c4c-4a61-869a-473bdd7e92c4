package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProduct;
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProductId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface MatchedProductRepository extends JpaRepository<MatchedProduct, MatchedProductId> {
  Optional<MatchedProduct> findByTitleTitleIdAndMatchedProductId(
      TitleId titleId, MatchedProductId matchedProductId);

  Optional<MatchedProduct> findByTitleTitleIdAndIsbn(TitleId titleId, String isbn);
}

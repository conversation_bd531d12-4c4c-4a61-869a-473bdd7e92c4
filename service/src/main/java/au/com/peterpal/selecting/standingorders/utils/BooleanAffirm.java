package au.com.peterpal.selecting.standingorders.utils;

public class BooleanAffirm {

    private boolean expression;

    public static BooleanAffirm of(boolean expression) {
        return new BooleanAffirm(expression);
    }

    private BooleanAffirm(boolean expression) {
        this.expression = expression;
    }

    /**
     * Affirm a boolean expression, throwing an {@code IllegalStateException}
     * if the expression evaluates to {@code false}.
     * <p>Use {@link #isTrue} if you wish to throw an {@code IllegalArgumentException}
     * <pre class="code">
     *  BooleanAffirm.of(id == null).state("The id property must not already be initialized");
     * </pre>
     * @param message the exception message to use if the assertion fails
     * @throws IllegalStateException if {@code expression} is {@code false}
     */
    public void state(String message) {
        if (!expression) {
            throw new IllegalStateException(message);
        }
    }

    /**
     * Affirm a boolean expression, throwing an {@code IllegalArgumentException}
     * if the expression evaluates to {@code false}.
     * <pre class="code">
     *     BooleanAffirm.of(i &gt; 0).isTrue("The value must be greater than zero");
     * </pre>
     *
     * @param message the exception message to use if the assertion fails
     * @throws IllegalArgumentException if {@code expression} is {@code false}
     */
    public void isTrue(String message) {
        if (!expression) {
            throw new IllegalArgumentException(message);
        }
    }
}

package au.com.peterpal.selecting.standingorders.ext.supplier.boundary;

import au.com.peterpal.selecting.standingorders.ext.supplier.boundary.dto.SupplierStatus;
import au.com.peterpal.selecting.standingorders.ext.supplier.control.SupplierService;
import au.com.peterpal.selecting.standingorders.ext.supplier.dto.SupplierResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.ArrayList;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@RestController
@RequiredArgsConstructor
@SecurityRequirement(name = "BearerAuth")
@RequestMapping("/api/suppliers")
public class SupplierController {

  private final SupplierService service;

  @GetMapping  ("name")
  public List<SupplierResponse> findAllByName(
      @RequestParam String name,
      @PageableDefault(size = 20, direction = Sort.Direction.ASC, sort = "name")
      Pageable pageRequest) {
    return service.findAllActiveByName(name, pageRequest).stream()
        .map(SupplierResponse::from)
        .collect(Collectors.toList());
  }

  @GetMapping
  public List<SupplierResponse> findAll(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username) {
    return service.findAllActive().stream()
        .map(SupplierResponse::from)
        .collect(Collectors.toList());
  }
}

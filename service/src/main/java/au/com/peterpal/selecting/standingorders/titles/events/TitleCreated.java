package au.com.peterpal.selecting.standingorders.titles.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.catalog.model.ProductFormCode;
import au.com.peterpal.selecting.standingorders.catalog.model.ProductFormDetail;
import au.com.peterpal.selecting.standingorders.standingorder.events.MatchFound;
import au.com.peterpal.selecting.standingorders.standingorder.model.TermId;
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProductId;
import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import java.time.LocalDate;
import au.com.peterpal.selecting.standingorders.titles.entity.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

@Getter
@SuperBuilder
public class TitleCreated extends DomainEvent {

  private List<MatchedProductId> matchedProductIds;

  @NonNull @NotEmpty private String title;

  @NonNull @NotEmpty private String personName;

  private String series;

  private String imprint;
  private TitleType type;
  private TitleStatus status;

  private LocalDateTime dateAdded;

  private LocalDateTime dateModified;

  private MatchedStandingOrder matchedStandingOrder;

  private MatchedProduct matchedProduct;

  @Builder
  @Data
  public static class MatchedStandingOrder {

    private UUID soId;
    private TermId matchedTermId;
    private String number;
  }

  @Builder
  @Data
  public static class MatchedProduct {

    private Integer productId;
    List<String> subjectCodeList;
    private String subjectCode;
    private String note;
    private Integer height;
    private Integer width;
    private ProductFormCode formCode;
    private List<ProductFormDetail> formDetails;
    private String subtitle;
    private String matchedPersonName;
    private LocalDate publicationDate;
    private Integer editionNumber;
    private String editionStatement;
    private String audienceCodes;
    private String audienceDescription;
    private boolean deferred;

    @NotNull private String productReference;
  }

  public static TitleCreated from(Title title, MatchFound matchFound) {

    MatchedStandingOrder matchedStandingOrder =
        MatchedStandingOrder.builder()
            .soId(matchFound.getSoId())
            .matchedTermId(matchFound.getMatchedTermId())
            .number(matchFound.getNumber())
            .build();

    MatchedProduct matchedProduct =
        MatchedProduct.builder()
            .productId(matchFound.getProductId())
            .subjectCodeList(matchFound.getSubjectCodeList())
            .subjectCode(matchFound.getSubjectCode1())
            .note(matchFound.getNote())
            .height(matchFound.getHeight())
            .width(matchFound.getWidth())
            .formCode(matchFound.getFormCode())
            .formDetails(matchFound.getFormDetails())
            .subtitle(matchFound.getSubtitle())
            .matchedPersonName(matchFound.getMatchedPersonName())
            .publicationDate(matchFound.getPublicationDate())
            .editionNumber(matchFound.getEditionNumber())
            .editionStatement(matchFound.getEditionStatement())
            .audienceCodes(matchFound.getAudienceCodes())
            .audienceDescription(matchFound.getAudienceDescription())
            .productReference(matchFound.getProductReference())
            .build();

    return Optional.ofNullable(title)
        .map(
            t ->
                TitleCreated.builder()
                    .id(title.getTitleId().getId())
                    .matchedProductIds(
                        title.getMatchedProducts().stream()
                            .map(
                                au.com.peterpal.selecting.standingorders.titles.entity
                                        .MatchedProduct
                                    ::getMatchedProductId)
                            .collect(Collectors.toList()))
                    .title(title.getTitle())
                    .personName(title.getPersonName())
                    .series(title.getSeries())
                    .imprint(title.getImprint())
                    .dateAdded(LocalDateTime.now())
                    .dateModified(LocalDateTime.now())
                    .type(title.getType())
                    .status(title.getTitleStatus())
                    .username("system")
                    .matchedStandingOrder(matchedStandingOrder)
                    .matchedProduct(matchedProduct)
                    .build())
        .orElseThrow(() -> new IllegalArgumentException("Title must not be null"));
  }

  public static TitleCreated from(Title relatedTitle, String username) {
    return TitleCreated.builder()
        .id(relatedTitle.getTitleId().getId())
        .matchedProductIds(
            relatedTitle.getMatchedProducts().stream()
                .map(
                    au.com.peterpal.selecting.standingorders.titles.entity.MatchedProduct
                        ::getMatchedProductId)
                .collect(Collectors.toList()))
        .title(relatedTitle.getTitle())
        .personName(relatedTitle.getPersonName())
        .series(relatedTitle.getSeries())
        .imprint(relatedTitle.getImprint())
        .dateAdded(LocalDateTime.now())
        .dateModified(LocalDateTime.now())
        .type(relatedTitle.getType())
        .status(relatedTitle.getTitleStatus())
        .username(StringUtils.defaultString(username, "system"))
        .build();
  }
}

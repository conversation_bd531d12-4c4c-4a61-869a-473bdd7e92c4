package au.com.peterpal.selecting.standingorders.catalog.model;

/**
 * @docRoot
 * Based on Onix List 74 - ISO 639-2/B
 */
public enum LanguageCode {

	ENG ("eng", "English"),
	ENGFTHI ("engfthi", "English for the Hearing Impaired"),
	NEN ("eng", "Non-english"),
	AAR ("aar", "Afar"),
	ABK ("abk", "Abkhaz"),
	ACE ("ace", "Achinese"),
	ACH ("ach", "Acoli"),
	ADA ("ada", "Adangme"),
	ADY ("ady", "Adygei"),
	AFA ("afa", "Afroasiatic (Other)"),
	AFH ("afh", "Afrihili (Artificial language)"),
	AFR ("afr", "Afrikaans"),
	AIN ("ain", "Ainu"),
	AKA ("aka", "Akan"),
	AKK ("akk", "Akkadian"),
	ALB ("alb", "Albanian"),
	ALE ("ale", "Aleut"),
	ALG ("alg", "Algonquian (Other)"),
	ALT ("alt", "Southern Altai"),
	AMH ("amh", "Amharic"),
	ANG ("ang", "English, Old (ca. 450-1100)"),
	ANP ("anp", "Angika"),
	APA ("apa", "Apache languages"),
	ARA ("ara", "Arabic"),
	ARC ("arc", "Aramaic"),
	ARG ("arg", "Aragonese Spanish"),
	ARM ("arm", "Armenian"),
	ARN ("arn", "Mapudungun; Mapuche"),
	ARP ("arp", "Arapaho"),
	ART ("art", "Artificial (Other)"),
	ARW ("arw", "Arawak"),
	ASM ("asm", "Assamese"),
	AST ("ast", "Asturian; Bable"),
	ATH ("ath", "Athapascan (Other)"),
	AUS ("aus", "Australian languages"),
	AVA ("ava", "Avaric"),
	AVE ("ave", "Avestan"),
	AWA ("awa", "Awadhi"),
	AYM ("aym", "Aymara"),
	AZE ("aze", "Azerbaijani"),
	BAD ("bad", "Banda languages"),
	BAI ("bai", "Bamileke languages"),
	BAK ("bak", "Bashkir"),
	BAL ("bal", "Baluchi"),
	BAM ("bam", "Bambara"),
	BAN ("ban", "Balinese"),
	BAQ ("baq", "Basque"),
	BAS ("bas", "Basa"),
	BAT ("bat", "Baltic (Other)"),
	BEJ ("bej", "Beja"),
	BEL ("bel", "Belarusian"),
	BEM ("bem", "Bemba"),
	BEN ("ben", "Bengali"),
	BER ("ber", "Berber (Other)"),
	BHO ("bho", "Bhojpuri"),
	BIH ("bih", "Bihari"),
	BIK ("bik", "Bikol"),
	BIN ("bin", "Bini; Edo"),
	BIS ("bis", "Bislama"),
	BLA ("bla", "Siksika"),
	BNT ("bnt", "Bantu (Other)"),
	BOS ("bos", "Bosnian"),
	BRA ("bra", "Braj"),
	BRE ("bre", "Breton"),
	BTK ("btk", "Batak languages"),
	BUA ("bua", "Buriat"),
	BUG ("bug", "Bugis"),
	BUL ("bul", "Bulgarian"),
	BUR ("bur", "Burmese"),
	BYN ("byn", "Blin; Bilin"),
	CAD ("cad", "Caddo"),
	CAI ("cai", "Central American Indian (Other)"),
	CAR ("car", "Galibi Carib"),
	CAT ("cat", "Catalan"),
	CAU ("cau", "Caucasian (Other)"),
	CEB ("ceb", "Cebuano"),
	CEL ("cel", "Celtic (Other)"),
	CHA ("cha", "Chamorro"),
	CHB ("chb", "Chibcha"),
	CHE ("che", "Chechen"),
	CHG ("chg", "Chagatai"),
	CHI ("chi", "Chinese"),
	CHK ("chk", "Truk"),
	CHM ("chm", "Mari"),
	CHN ("chn", "Chinook jargon"),
	CHO ("cho", "Choctaw"),
	CHP ("chp", "Chipewyan"),
	CHR ("chr", "Cherokee"),
	CHU ("chu", "Church Slavic; Old Slavonic; Church Slavonic; Old Bulgarian; Old Church Slavonic"),
	CHV ("chv", "Chuvash"),
	CHY ("chy", "Cheyenne"),
	CMC ("cmc", "Chamic languages"),
	COP ("cop", "Coptic"),
	COR ("cor", "Cornish"),
	COS ("cos", "Corsican"),
	CPE ("cpe", "Creoles and Pidgins, English-based (Other)"),
	CPF ("cpf", "Creoles and Pidgins, French-based (Other)"),
	CPP ("cpp", "Creoles and Pidgins, Portuguese-based (Other)"),
	CRE ("cre", "Cree"),
	CRH ("crh", "Crimean Turkish; Crimean Tatar"),
	CRP ("crp", "Creoles and Pidgins (Other)"),
	CSB ("csb", "Kashubian"),
	CUS ("cus", "Cushitic (Other)"),
	CZE ("cze", "Czech"),
	DAK ("dak", "Dakota"),
	DAN ("dan", "Danish"),
	DAR ("dar", "Dargwa"),
	DAY ("day", "Land Dayak languages"),
	DEL ("del", "Delaware"),
	DEN ("den", "Slave"),
	DGR ("dgr", "Dogrib"),
	DIN ("din", "Dinka"),
	DIV ("div", "Divehi; Dhivehi; Maldivian"),
	DOI ("doi", "Dogri"),
	DRA ("dra", "Dravidian (Other)"),
	DSB ("dsb", "Lower Sorbian"),
	DUA ("dua", "Duala"),
	DUM ("dum", "Dutch, Middle (ca. 1050-1350)"),
	DUT ("dut", "Dutch; Flemish"),
	DYU ("dyu", "Dyula"),
	DZO ("dzo", "Dzongkha"),
	EFI ("efi", "Efik"),
	EGY ("egy", "Egyptian"),
	EKA ("eka", "Ekajuk"),
	ELX ("elx", "Elamite"),
	ENM ("enm", "English, Middle (1100-1500)"),
	EPO ("epo", "Esperanto"),
	EST ("est", "Estonian"),
	EWE ("ewe", "Ewe"),
	EWO ("ewo", "Ewondo"),
	FAN ("fan", "Fang"),
	FAO ("fao", "Faroese"),
	FAT ("fat", "Fanti"),
	FIJ ("fij", "Fijian"),
	FIL ("fil", "Filipino; Pilipino"),
	FIN ("fin", "Finnish"),
	FIU ("fiu", "Finno-Ugrian (Other)"),
	FON ("fon", "Fon"),
	FRE ("fre", "French"),
	FRM ("frm", "French, Middle (ca. 1400-1600)"),
	FRO ("fro", "French, Old (ca. 842-1400)"),
	FRR ("frr", "Northern Frisian"),
	FRS ("frs", "Eastern Frisian"),
	FRY ("fry", "Western Frisian"),
	FUL ("ful", "Fula"),
	FUR ("fur", "Friulian"),
	GAA ("gaa", "Gã"),
	GAY ("gay", "Gayo"),
	GBA ("gba", "Gbaya"),
	GEM ("gem", "Germanic (Other)"),
	GEO ("geo", "Georgian"),
	GER ("ger", "German"),
	GEZ ("gez", "Ethiopic"),
	GIL ("gil", "Gilbertese"),
	GLA ("gla", "Scottish Gaelic"),
	GLE ("gle", "Irish"),
	GLG ("glg", "Galician"),
	GLV ("glv", "Manx"),
	GMH ("gmh", "German, Middle High (ca. 1050-1500)"),
	GOH ("goh", "German, Old High (ca. 750-1050)"),
	GON ("gon", "Gondi"),
	GOR ("gor", "Gorontalo"),
	GOT ("got", "Gothic"),
	GRB ("grb", "Grebo"),
	GRC ("grc", "Greek, Ancient (to 1453)"),
	GRE ("gre", "Greek, Modern (1453-)"),
	GRN ("grn", "Guarani"),
	GSW ("gsw", "Swiss German; Alemannic"),
	GUJ ("guj", "Gujarati"),
	GWI ("gwi", "Gwich'in"),
	HAI ("hai", "Haida"),
	HAT ("hat", "Haitian French Creole"),
	HAU ("hau", "Hausa"),
	HAW ("haw", "Hawaiian"),
	HEB ("heb", "Hebrew"),
	HER ("her", "Herero"),
	HIL ("hil", "Hiligaynon"),
	HIM ("him", "Himachali"),
	HIN ("hin", "Hindi"),
	HIT ("hit", "Hittite"),
	HMN ("hmn", "Hmong"),
	HMO ("hmo", "Hiri Motu"),
	HSB ("hsb", "Upper Sorbian"),
	HUN ("hun", "Hungarian"),
	HUP ("hup", "Hupa"),
	IBA ("iba", "Iban"),
	IBO ("ibo", "Igbo"),
	ICE ("ice", "Icelandic"),
	IDO ("ido", "Ido"),
	III ("iii", "Sichuan Yi"),
	IJO ("ijo", "Ijo languages"),
	IKU ("iku", "Inuktitut"),
	ILE ("ile", "Interlingue"),
	ILO ("ilo", "Iloko"),
	INA ("ina", "Interlingua (International Auxiliary Language Association)"),
	INC ("inc", "Indic (Other)"),
	IND ("ind", "Indonesian"),
	INE ("ine", "Indo-European (Other)"),
	INH ("inh", "Ingush"),
	IPK ("ipk", "Inupiaq"),
	IRA ("ira", "Iranian (Other)"),
	IRO ("iro", "Iroquoian (Other)"),
	ITA ("ita", "Italian"),
	JAV ("jav", "Javanese"),
	JBO ("jbo", "Lojban"),
	JPN ("jpn", "Japanese"),
	JPR ("jpr", "Judeo-Persian"),
	JRB ("jrb", "Judeo-Arabic"),
	KAA ("kaa", "Kara-Kalpak"),
	KAB ("kab", "Kabyle"),
	KAC ("kac", "Kachin; Jingpho"),
	KAL ("kal", "Kalâtdlisut"),
	KAM ("kam", "Kamba"),
	KAN ("kan", "Kannada"),
	KAR ("kar", "Karen languages"),
	KAS ("kas", "Kashmiri"),
	KAU ("kau", "Kanuri"),
	KAW ("kaw", "Kawi"),
	KAZ ("kaz", "Kazakh"),
	KBD ("kbd", "Kabardian"),
	KHA ("kha", "Khasi"),
	KHI ("khi", "Khoisan (Other)"),
	KHM ("khm", "Central Khmer"),
	KHO ("kho", "Khotanese"),
	KIK ("kik", "Kikuyu; Gikuyu"),
	KIN ("kin", "Kinyarwanda"),
	KIR ("kir", "Kirghiz; Kyrgyz"),
	KMB ("kmb", "Kimbundu"),
	KOK ("kok", "Konkani"),
	KOM ("kom", "Komi"),
	KON ("kon", "Kongo"),
	KOR ("kor", "Korean"),
	KOS ("kos", "Kusaie"),
	KPE ("kpe", "Kpelle"),
	KRC ("krc", "Karachay-Balkar"),
	KRL ("krl", "Karelian"),
	KRO ("kro", "Kru languages"),
	KRU ("kru", "Kurukh"),
	KUA ("kua", "Kuanyama"),
	KUM ("kum", "Kumyk"),
	KUR ("kur", "Kurdish"),
	KUT ("kut", "Kutenai"),
	LAD ("lad", "Ladino"),
	LAH ("lah", "Lahnda"),
	LAM ("lam", "Lamba"),
	LAO ("lao", "Lao"),
	LAT ("lat", "Latin"),
	LAV ("lav", "Latvian"),
	LEZ ("lez", "Lezgian"),
	LIM ("lim", "Limburgish"),
	LIN ("lin", "Lingala"),
	LIT ("lit", "Lithuanian"),
	LOL ("lol", "Mongo-Nkundu"),
	LOZ ("loz", "Lozi"),
	LTZ ("ltz", "Luxembourgish; Letzeburgesch"),
	LUA ("lua", "Luba-Lulua"),
	LUB ("lub", "Luba-Katanga"),
	LUG ("lug", "Ganda"),
	LUI ("lui", "Luiseño"),
	LUN ("lun", "Lunda"),
	LUO ("luo", "Luo (Kenya and Tanzania)"),
	LUS ("lus", "Lushai"),
	MAC ("mac", "Macedonian"),
	MAD ("mad", "Madurese"),
	MAG ("mag", "Magahi"),
	MAH ("mah", "Marshall"),
	MAI ("mai", "Maithili"),
	MAK ("mak", "Makasar"),
	MAL ("mal", "Malayalam"),
	MAN ("man", "Mandingo"),
	MAO ("mao", "Maori"),
	MAP ("map", "Austronesian (Other)"),
	MAR ("mar", "Marathi"),
	MAS ("mas", "Masai"),
	MAY ("may", "Malay"),
	MDF ("mdf", "Moksha"),
	MDR ("mdr", "Mandar"),
	MEN ("men", "Mende"),
	MGA ("mga", "Irish, Middle (ca. 1100-1550)"),
	MIC ("mic", "Mi'kmaq; Micmac"),
	MIN ("min", "Minangkabau"),
	MIS ("mis", "Miscellaneous languages"),
	MKH ("mkh", "Mon-Khmer (Other)"),
	MLG ("mlg", "Malagasy"),
	MLT ("mlt", "Maltese"),
	MNC ("mnc", "Manchu"),
	MNI ("mni", "Manipuri"),
	MNO ("mno", "Manobo languages"),
	MOH ("moh", "Mohawk"),
	MOL ("mol", "Moldavian"),
	MON ("mon", "Mongolian"),
	MOS ("mos", "Mooré"),
	MUL ("mul", "Multiple languages"),
	MUN ("mun", "Munda (Other)"),
	MUS ("mus", "Creek"),
	MWL ("mwl", "Mirandese"),
	MWR ("mwr", "Marwari"),
	MYN ("myn", "Mayan languages"),
	MYV ("myv", "Erzya"),
	NAH ("nah", "Nahuatl languages"),
	NAI ("nai", "North American Indian (Other)"),
	NAP ("nap", "Neapolitan"),
	NAU ("nau", "Nauru"),
	NAV ("nav", "Navajo"),
	NBL ("nbl", "Ndebele, South"),
	NDE ("nde", "Ndebele, North"),
	NDO ("ndo", "Ndonga"),
	NDS ("nds", "Low German; Low Saxon"),
	NEP ("nep", "Nepali"),
	NEW ("new", "Newari"),
	NIA ("nia", "Nias"),
	NIC ("nic", "Niger-Kordofanian (Other)"),
	NIU ("niu", "Niuean"),
	NNO ("nno", "Norwegian Nynorsk"),
	NOB ("nob", "Norwegian Bokmål"),
	NOG ("nog", "Nogai"),
	NON ("non", "Old Norse"),
	NOR ("nor", "Norwegian"),
	NQO ("nqo", "N'Ko"),
	NSO ("nso", "Pedi; Sepedi; Northern Sotho"),
	NUB ("nub", "Nubian languages"),
	NWC ("nwc", "Classical Newari; Old Newari; Classical Nepal Bhasa"),
	NYA ("nya", "Chichewa; Chewa; Nyanja"),
	NYM ("nym", "Nyamwezi"),
	NYN ("nyn", "Nyankole"),
	NYO ("nyo", "Nyoro"),
	NZI ("nzi", "Nzima"),
	OCI ("oci", "Occitan (post-1500); Provencal"),
	OJI ("oji", "Ojibwa"),
	ORI ("ori", "Oriya"),
	ORM ("orm", "Oromo"),
	OSA ("osa", "Osage"),
	OSS ("oss", "Ossetian; Ossetic"),
	OTA ("ota", "Turkish, Ottoman"),
	OTO ("oto", "Otomian languages"),
	PAA ("paa", "Papuan (Other)"),
	PAG ("pag", "Pangasinan"),
	PAL ("pal", "Pahlavi"),
	PAM ("pam", "Pampanga"),
	PAN ("pan", "Panjabi"),
	PAP ("pap", "Papiamento"),
	PAU ("pau", "Palauan"),
	PEO ("peo", "Old Persian (ca. 600-400 B.C.)"),
	PER ("per", "Persian"),
	PHI ("phi", "Philippine (Other)"),
	PHN ("phn", "Phoenician"),
	PLI ("pli", "Pali"),
	POL ("pol", "Polish"),
	PON ("pon", "Ponape"),
	POR ("por", "Portuguese"),
	PRA ("pra", "Prakrit languages"),
	PRO ("pro", "Provençal (to 1500)"),
	PUS ("pus", "Pushto"),
	QAR ("qar", "Aranés"),
	QAV ("qav", "Valencian"),
	QUE ("que", "Quechua"),
	RAJ ("raj", "Rajasthani"),
	RAP ("rap", "Rapanui"),
	RAR ("rar", "Rarotongan; Cook Islands Maori"),
	ROA ("roa", "Romance (Other)"),
	ROH ("roh", "Romansh"),
	ROM ("rom", "Romany"),
	RUM ("rum", "Romanian"),
	RUN ("run", "Rundi"),
	RUP ("rup", "Aromanian; Arumanian; Macedo-Romanian"),
	RUS ("rus", "Russian"),
	SAD ("sad", "Sandawe"),
	SAG ("sag", "Sango"),
	SAH ("sah", "Yakut"),
	SAI ("sai", "South American Indian (Other)"),
	SAL ("sal", "Salishan languages"),
	SAM ("sam", "Samaritan Aramaic"),
	SAN ("san", "Sanskrit"),
	SAS ("sas", "Sasak"),
	SAT ("sat", "Santali"),
	SCC ("scc", "Serbian"),
	SCN ("scn", "Sicilian"),
	SCO ("sco", "Scots"),
	SCR ("scr", "Croatian"),
	SEL ("sel", "Selkup"),
	SEM ("sem", "Semitic (Other)"),
	SGA ("sga", "Irish, Old (to 1100)"),
	SGN ("sgn", "Sign languages"),
	SHN ("shn", "Shan"),
	SID ("sid", "Sidamo"),
	SIN ("sin", "Sinhala; Sinhalese"),
	SIO ("sio", "Siouan (Other)"),
	SIT ("sit", "Sino-Tibetan (Other)"),
	SLA ("sla", "Slavic (Other)"),
	SLO ("slo", "Slovak"),
	SLV ("slv", "Slovenian"),
	SMA ("sma", "Southern Sami"),
	SME ("sme", "Northern Sami"),
	SMI ("smi", "Sami languages (other)"),
	SMJ ("smj", "Lule Sami"),
	SMN ("smn", "Inari Sami"),
	SMO ("smo", "Samoan"),
	SMS ("sms", "Skolt Sami"),
	SNA ("sna", "Shona"),
	SND ("snd", "Sindhi"),
	SNK ("snk", "Soninke"),
	SOG ("sog", "Sogdian"),
	SOM ("som", "Somali"),
	SON ("son", "Songhai languages"),
	SOT ("sot", "Sotho (aka Sesotho)"),
	SPA ("spa", "Spanish"),
	SRD ("srd", "Sardinian"),
	SRN ("srn", "Sranan Tongo"),
	SRR ("srr", "Serer"),
	SSA ("ssa", "Nilo-Saharan (Other)"),
	SSW ("ssw", "Swazi (aka Swati, siSwati)"),
	SUK ("suk", "Sukuma"),
	SUN ("sun", "Sundanese"),
	SUS ("sus", "Susu"),
	SUX ("sux", "Sumerian"),
	SWA ("swa", "Swahili"),
	SWE ("swe", "Swedish"),
	SYR ("syr", "Syriac"),
	TAH ("tah", "Tahitian"),
	TAI ("tai", "Tai (Other)"),
	TAM ("tam", "Tamil"),
	TAT ("tat", "Tatar"),
	TEL ("tel", "Telugu"),
	TEM ("tem", "Temne"),
	TER ("ter", "Terena"),
	TET ("tet", "Tetum"),
	TGK ("tgk", "Tajik"),
	TGL ("tgl", "Tagalog"),
	THA ("tha", "Thai"),
	TIB ("tib", "Tibetan"),
	TIG ("tig", "Tigré"),
	TIR ("tir", "Tigrinya"),
	TIV ("tiv", "Tiv"),
	TKL ("tkl", "Tokelauan"),
	TLH ("tlh", "Klingon; tlhIngan-Hol"),
	TLI ("tli", "Tlingit"),
	TMH ("tmh", "Tamashek"),
	TOG ("tog", "Tonga (Nyasa)"),
	TON ("ton", "Tongan"),
	TPI ("tpi", "Tok Pisin"),
	TSI ("tsi", "Tsimshian"),
	TSN ("tsn", "Tswana (aka Setswana)"),
	TSO ("tso", "Tsonga"),
	TUK ("tuk", "Turkmen"),
	TUM ("tum", "Tumbuka"),
	TUP ("tup", "Tupi languages"),
	TUR ("tur", "Turkish"),
	TUT ("tut", "Altaic (Other)"),
	TVL ("tvl", "Tuvaluan"),
	TWI ("twi", "Twi"),
	TYV ("tyv", "Tuvinian"),
	UDM ("udm", "Udmurt"),
	UGA ("uga", "Ugaritic"),
	UIG ("uig", "Uighur; Uyghur"),
	UKR ("ukr", "Ukrainian"),
	UMB ("umb", "Umbundu"),
	UND ("und", "Undetermined"),
	URD ("urd", "Urdu"),
	UZB ("uzb", "Uzbek"),
	VAI ("vai", "Vai"),
	VEN ("ven", "Venda"),
	VIE ("vie", "Vietnamese"),
	VOL ("vol", "Volapük"),
	VOT ("vot", "Votic"),
	WAK ("wak", "Wakashan languages"),
	WAL ("wal", "Walamo"),
	WAR ("war", "Waray"),
	WAS ("was", "Washo"),
	WEL ("wel", "Welsh"),
	WEN ("wen", "Sorbian languages"),
	WLN ("wln", "Walloon"),
	WOL ("wol", "Wolof"),
	XAL ("xal", "Kalmyk"),
	XHO ("xho", "Xhosa"),
	YAO ("yao", "Yao"),
	YAP ("yap", "Yapese"),
	YID ("yid", "Yiddish"),
	YOR ("yor", "Yoruba"),
	YPK ("ypk", "Yupik languages"),
	ZAP ("zap", "Zapotec"),
	ZEN ("zen", "Zenaga"),
	ZHA ("zha", "Zhuang; Chuang"),
	ZND ("znd", "Zande languages"),
	ZUL ("zul", "Zulu"),
	ZUN ("zun", "Zuni"),
	ZZA ("zza", "Zaza; Dimili; Dimli; Kirdki; Kirmanjki; Zazaki");

	private final String code;
	private final String description;

	LanguageCode(String code, String description) {
		this.code = code;
		this.description = description;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public static LanguageCode mapOnixCode(String onixCode) {
		for (LanguageCode value : LanguageCode.values()) {
			if (value.code.equals(onixCode)) {
				return value;
			}
		}
		throw new IllegalArgumentException("Invalid " + LanguageCode.class.getSimpleName() + ": " + onixCode);
	}

	public static LanguageCode mapName(String languageName) {
		for (LanguageCode value : LanguageCode.values()) {
			if (value.description.equals(languageName)) {
				return value;
			}
		}
		throw new IllegalArgumentException("Invalid " + LanguageCode.class.getSimpleName() + ": " + languageName);
	}

	@Override
	public String toString() {
		return this.description;
	}
}

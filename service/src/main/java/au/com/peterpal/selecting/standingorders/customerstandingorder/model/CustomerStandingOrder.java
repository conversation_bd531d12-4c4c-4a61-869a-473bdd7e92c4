package au.com.peterpal.selecting.standingorders.customerstandingorder.model;

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.customerstandingorder.events.CustomerStandingOrderCreated;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.persistence.CascadeType;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.ToString;
import lombok.ToString.Include;
import lombok.With;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "customer_standing_order")
public class CustomerStandingOrder {

  @EqualsAndHashCode.Include
  @NonNull
  @EmbeddedId
  @Include
  private CustomerStandingOrderId customerStandingOrderId;

  @NonNull
  @Enumerated(EnumType.STRING)
  @Include
  private StandingOrderStatus customerStandingOrderStatus;

  @OneToMany(mappedBy = "customerStandingOrder", cascade = CascadeType.ALL, orphanRemoval = true)
  @JsonIgnore
  @Builder.Default
  private List<Allocation> allocations = new ArrayList<>();

  @ManyToOne
  @JoinColumn(name = "customerId")
  @Include
  private Customer customer;

  @ManyToOne
  @JoinColumn(name = "standingOrderId")
  @Include
  private StandingOrder standingOrder;

  public static CustomerStandingOrder from(
      CustomerStandingOrder customerStandingOrder, Customer customer, StandingOrder standingOrder) {

    Affirm.of(customerStandingOrder).notNull("CustomerStandingOrder must not be null");

    return CustomerStandingOrder.builder()
        .customerStandingOrderId(customerStandingOrder.getCustomerStandingOrderId())
        .customerStandingOrderStatus(customerStandingOrder.getCustomerStandingOrderStatus())
        // .orderIds(customerStandingOrder.getOrderIds())
        .allocations(customerStandingOrder.getAllocations())
        .customer(customer)
        .standingOrder(standingOrder)
        .build();
  }

  public CustomerStandingOrder addAllocation(Allocation allocation) {
    return Optional.ofNullable(allocation)
        .map(a -> {
          allocation.setCustomerStandingOrder(this);
          allocations.add(allocation);
          return this;
        })
        .orElseThrow(() -> new IllegalArgumentException("Allocation must not be null"));
  }

  public static CustomerStandingOrder from(CustomerStandingOrderCreated event) {
    CustomerStandingOrder cso =
        CustomerStandingOrder.builder()
            .customerStandingOrderId(CustomerStandingOrderId.of(event.getId()))
            .customerStandingOrderStatus(event.getCustomerStandingOrderStatus())
            .customer(event.getCustomer())
            .standingOrder(event.getStandingOrder())
            .build();

    Allocation.from(event.getAllocations()).forEach(cso::addAllocation);
    return cso;
  }

  /**
   * Given a category and a branch flag return allocations.
   * <p>
   * If the category string is blank it does not have effect. If the flag is true, branch
   * allocations are returned, else customer allocations are returned.
   * </p>
   *
   * @param categories a list of categories
   * @param branch   flag specifying if branch or customer allocations are to be selected
   * @return a list of allocations
   */
  public List<Allocation> getAllocations(List<Category> categories, boolean branch) {
    return allocations.stream()
        .filter(a -> {
          if (categories.isEmpty()) {
            return hasABranchIfRequired(branch, a);
          } else {
            return a.getCategories().stream().anyMatch(categories::contains)
                && hasABranchIfRequired(branch, a);
          }
        })
        .collect(Collectors.toList());
  }

  public List<Allocation> getAllocationsByCategoryCodes(List<String> categoryCodes, boolean branch) {
    return allocations.stream()
        .filter(a -> {
          if (categoryCodes.isEmpty()) {
            return hasABranchIfRequired(branch, a);
          } else {
            return a.getCategories().stream().map(Category::getCode).anyMatch(categoryCodes::contains)
                && hasABranchIfRequired(branch, a);
          }
        })
        .collect(Collectors.toList());
  }

  @JsonIgnore
  public boolean isAllAllocationInActive() {
    return allocations.stream()
        .filter(Allocation::isNotBranchAllocation)
        .allMatch(allocation -> allocation.getStatus() == AllocationStatus.INACTIVE);
  }
  @JsonIgnore
  public boolean containsPausedAllocation() {
    return allocations.stream()
        .filter(Allocation::isNotBranchAllocation)
        .anyMatch(allocation -> allocation.getStatus() == AllocationStatus.PAUSED);
  }

  private boolean hasABranchIfRequired(boolean branch, Allocation allocation) {
    return (branch && allocation.getBranch() != null) || (!branch && allocation.getBranch() == null);
  }
}

//package au.com.peterpal.selecting.standingorders.standingorder.model;
//
//import au.com.peterpal.common.ddd.ids.UuidEntityId;
//import com.fasterxml.jackson.annotation.JsonCreator;
//
//import javax.persistence.Embeddable;
//import javax.validation.constraints.NotEmpty;
//import java.util.UUID;
//
//@Embeddable
//public class TitleId extends UuidEntityId {
//
//  public static TitleId of(@NotEmpty UUID id) {
//    return new TitleId(id);
//  }
//
//  public static TitleId of(@NotEmpty String id) {
//    return new TitleId(id);
//  }
//
//  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
//  public TitleId(@NotEmpty UUID id) {
//    super(id);
//  }
//
//  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
//  public TitleId(@NotEmpty String id) {
//    super(UUID.fromString(id));
//  }
//
//  public TitleId() {}
//
//  @Override
//  public @NotEmpty UUID getId() {
//    return super.getId();
//  }
//}

package au.com.peterpal.selecting.standingorders.standingorder.dto;

import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StandingOrderCustomerResponse {
    private StandingOrderId standingOrderId;
    private Customer customer;

}

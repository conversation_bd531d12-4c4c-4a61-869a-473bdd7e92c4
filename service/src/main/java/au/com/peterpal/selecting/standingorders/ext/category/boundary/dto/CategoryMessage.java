package au.com.peterpal.selecting.standingorders.ext.category.boundary.dto;

import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus;
import javax.validation.constraints.NotNull;
import lombok.*;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@Builder
public class CategoryMessage {

  @NonNull @NotNull @ToString.Include @EqualsAndHashCode.Include private CategoryId categoryId;

  @NotNull @NonNull private String code;

  @NonNull @NotNull private CategoryStatus status;

  @NonNull @NotNull private String description;
}

package au.com.peterpal.selecting.standingorders.ext.stockitem.boundary;

import au.com.peterpal.selecting.standingorders.ext.stockitem.boundary.dto.StockItemMessage;
import au.com.peterpal.selecting.standingorders.ext.stockitem.control.StockItemSyncService;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.context.IntegrationContextUtils;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.dsl.channel.MessageChannels;
import org.springframework.integration.jms.dsl.Jms;
import org.springframework.messaging.MessageChannel;

import javax.jms.ConnectionFactory;

@Log4j2
@Configuration
@RequiredArgsConstructor
public class StockItemEventsSync {
  private static final String MESSAGE_TYPE = "message_type";

  private static final String STOCK_ITEM_CREATED = "stock-item-created";
  private static final String STOCK_ITEM_CHANGED = "stock-item-changed";

  private static final String CLIENT_ID = "standing-orders:stock-control";

  @Value("${events.stock-item.channel:stock-item-events-topic}")
  private String stockItemEventsChannelName;

  @Value("${events.stock-item.subscription:stock-item-events}")
  private String stockItemEventsSubscriptionName;

  private final ConnectionFactory connectionFactory;
  private final StockItemSyncService stockItemSync;

  @Bean
  public MessageChannel stockItemEventsRouter() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow stockItemEventsSyncListener() {
    return IntegrationFlows.from(
      Jms.messageDrivenChannelAdapter(connectionFactory)
          .destination(stockItemEventsChannelName)
          .configureListenerContainer(
              spec -> {
                spec.pubSubDomain(true);
                spec.durableSubscriptionName(stockItemEventsSubscriptionName);
                spec.recoveryInterval(60000L);
                spec.clientId(CLIENT_ID);
              })
          .errorChannel(IntegrationContextUtils.ERROR_CHANNEL_BEAN_NAME))
      .channel(stockItemEventsRouter())
      .get();
  }

  @Bean
  public IntegrationFlow stockItemEventsSyncRouter() {
    return IntegrationFlows.from(stockItemEventsRouter())
      .log(message -> "Stock Item Events received: " + message)
      .log(message -> "Routing event: " + message.getHeaders().get(MESSAGE_TYPE))
      .<String>route(
              "headers[" + MESSAGE_TYPE + "]",
              mapping -> mapping
                      .subFlowMapping(STOCK_ITEM_CREATED, syncStockItemCreated())
                      .subFlowMapping(STOCK_ITEM_CHANGED, syncStockItemChanged())
                      .defaultOutputToParentFlow())
      .get();
  }

  @Bean
  public IntegrationFlow syncStockItemCreated() {
    return flow -> flow.transform(Transformers.fromJson(StockItemMessage.class))
            .<StockItemMessage>handle((message, headers) -> stockItemSync.handle(message))
            .log(msg -> "StockItem Created synchronization completed.");
  }

  @Bean
  public IntegrationFlow syncStockItemChanged() {
    return flow -> flow.transform(Transformers.fromJson(StockItemMessage.class))
            .<StockItemMessage>handle((message, headers) -> stockItemSync.handle(message))
            .log(msg -> "StockItem Changed synchronization completed.");
  }
}

package au.com.peterpal.selecting.standingorders.ext.supplier.control;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import au.com.peterpal.common.utils.ext.SyncService;
import au.com.peterpal.selecting.standingorders.ext.supplier.boundary.dto.SupplierMessage;
import au.com.peterpal.selecting.standingorders.ext.supplier.boundary.dto.SupplierStatus;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.Supplier;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class SupplierSyncService extends
    SyncService<SupplierRepository, SupplierMessage, Supplier> {

  private SupplierRepository supplierRepo;

  public SupplierSyncService(SupplierRepository supplierRepository) {
    super(supplierRepository);
    supplierRepo = supplierRepository;
  }

  @Override
  protected UuidEntityId getId(SupplierMessage msg) {
    return msg.getSupplierId();
  }

  @Override
  protected SupplierMessage update(Supplier existing, SupplierMessage msg) {
    existing.setName(msg.getName());
    existing.setCode(msg.getCode());
    existing.setStatus(msg.getStatus());
    save(existing);
    return msg;
  }

  @Override
  protected SupplierMessage create(SupplierMessage msg) {
    if ( SupplierStatus.INACTIVE.equals(msg.getStatus())) {
      log.warn(() -> String.format("Supplier %s will not be created because is INACTIVE", msg.getCode()));
    } else {
      String str = "Supplier %s will not be created because already exists. msg %s";
      List<Supplier> list = supplierRepo.findAllByCode(msg.getCode());
      if (!list.isEmpty()) {
        log.warn(() -> String.format(str, msg.getCode(), msg));
      } else {
        save(Supplier.builder()
          .supplierId(msg.getSupplierId())
          .code(msg.getCode())
          .name(msg.getName())
          .status(msg.getStatus())
          .build());
      }
    }
    return msg;
  }
}

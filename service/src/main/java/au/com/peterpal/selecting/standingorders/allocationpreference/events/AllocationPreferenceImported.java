package au.com.peterpal.selecting.standingorders.allocationpreference.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AllocationPrefInfo;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import au.com.peterpal.selecting.standingorders.utils.StringAffirm;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Getter
@SuperBuilder
@ToString
public class AllocationPreferenceImported extends DomainEvent {
  private AllocationPrefInfo allocPref;

  public static AllocationPreferenceImported from(AllocationPrefInfo allocPref, String username) {
    StringAffirm.of(username).hasText("username must not be blank");
    Affirm.of(allocPref).notNull("allocation preference information must not be null");

    return AllocationPreferenceImported.builder()
        .id(UUID.randomUUID())
        .username(username)
        .allocPref(allocPref)
        .build();
  }
}

package au.com.peterpal.selecting.standingorders.gateways.clientweb.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.catalog.model.*;
import au.com.peterpal.selecting.standingorders.standingorder.model.AdHocProductId;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@With
@Value
@NoArgsConstructor(force = true)
@AllArgsConstructor
@Builder
public class CwProductInfo {
  @NotNull private Integer id;

  @NotNull private TitleInfo title;

  @Builder.Default private ProductFormCode formCode = ProductFormCode.UN;

  @NotNull private String productReference;

  private String bicSubjectCode1;

  private Integer height;
  private Integer width;
  private LocalDate publicationDate;

  private final String countryOfPublication;

  @NotNull private LanguageCode languageCode;

  private TitleInfo seriesTitle;
  private String primaryContributorNameInverted;
  private List<String> imprintNames;
  private List<String> subjectCodes;
  private List<ContributorInfo> contributors;
  private List<PublisherInfo> publishers;
  private List<ProductFormDetail> formDetails;
  private String editionStatement;
  private String audience;
  private String rrpPrice;

  @JsonInclude(JsonInclude.Include.NON_NULL)
  private AdHocProductId adHocProductId;

  private ReleaseFormat selectedAs;

  public String getAuthorsInverted() {
    return StringUtils.defaultString(
        primaryContributorNameInverted,
        Optional.ofNullable(contributors).orElse(Lists.newArrayList()).stream()
            .map(ContributorInfo::getPersonNameInverted)
            .findFirst()
            .orElse(null));
  }

  public String getPublisherName() {
    return CollectionUtils.isNotEmpty(publishers)
        ? publishers.stream().map(PublisherInfo::getPublisherName).findFirst().orElse(null)
        : null;
  }

  public String getSeries() {
    return Optional.ofNullable(seriesTitle).map(TitleInfo::getText).orElse(null);
  }

  public String getTitleWithoutPrefix() {
    return Optional.ofNullable(title).map(TitleInfo::getWithoutPrefix).orElse(null);
  }

  public String getPublicationDateFormatted() {
    return Optional.ofNullable(publicationDate)
        .map(pd -> pd.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
        .orElse(null);
  }

  public Integer getPublicationYear() {
    return Optional.ofNullable(publicationDate).map(LocalDate::getYear).orElse(null);
  }

  @Value
  @Builder
  @NoArgsConstructor(force = true)
  @AllArgsConstructor
  public static class TitleInfo {
    @Builder.Default private TitleTypeCode type = TitleTypeCode.TITLE;

    private String text;
    private String prefix;
    private String withoutPrefix;
    private String subtitle;
  }

  @Value
  @Builder
  @NoArgsConstructor(force = true)
  @AllArgsConstructor
  public static class ContributorInfo {
    private ContributorRoleCode role;
    private String personName;
    private String personNameInverted;
  }

  @Value
  @Builder
  @NoArgsConstructor(force = true)
  @AllArgsConstructor
  public static class PublisherInfo {
    @Builder.Default private PublishingRoleCode roleCode = PublishingRoleCode.PUB;

    private String publisherName;
  }
}

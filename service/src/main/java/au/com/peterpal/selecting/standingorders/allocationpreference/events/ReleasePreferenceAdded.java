package au.com.peterpal.selecting.standingorders.allocationpreference.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreference;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Setter
@Getter
@SuperBuilder
@ToString
public class ReleasePreferenceAdded extends DomainEvent {

  private ReleasePreference releasePreference;

  public static ReleasePreferenceAdded of(
      UUID id, ReleasePreference relPreference, String username) {

    return ReleasePreferenceAdded.builder()
        .id(id)
        .username(username)
        .releasePreference(relPreference)
        .build();
  }
}

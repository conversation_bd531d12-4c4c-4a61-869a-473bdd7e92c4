package au.com.peterpal.selecting.standingorders.pendingorder.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.order.control.OrderService;
import au.com.peterpal.selecting.standingorders.ext.order.dto.DuplicateOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.PendingOrderResponse;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.PendingOrderStandingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.ProcessFormatResponse;
import au.com.peterpal.selecting.standingorders.pendingorder.events.PendingOrderDeleted;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProduct;
import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import au.com.peterpal.selecting.standingorders.utils.Affirm;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Log4j2
@Service
@RequiredArgsConstructor
public class PendingOrderService {

  private final PendingOrderRepository pendingOrderRepository;
  private final OrderService orderService;
  private final EventPublisher eventPublisher;

  public PendingOrder findById(PendingOrderId pendingOrderId) {
    Affirm.of(pendingOrderId).notNull("PendingOrderId must not be null");

    return pendingOrderRepository
        .findByPendingOrderId(pendingOrderId)
        .orElseThrow(
            () ->
                new ResourceNotFoundException(PendingOrder.class, String.valueOf(pendingOrderId)));
  }

  public Page<PendingOrderResponse> searchAllByCustomerId(
      CustomerId customerId, Pageable pageRequest) {

    Page<PendingOrder> result = pendingOrderRepository.searchAllByCustomer(customerId, pageRequest);
    return result.map(PendingOrderResponse::from);
  }

  public List<PendingOrderResponse> searchAllByTitle(
      TitleId titleId, PendingOrderStatus status, String category) {
    List<PendingOrderStandingOrder> result =
        pendingOrderRepository.searchAllByTitle(titleId, status, category);
    Map<PendingOrderId, Set<PendingOrderResponse.StandingOrder>> standingOrderMap =
        result.stream()
            .collect(
                Collectors.groupingBy(
                    p -> p.getPendingOrder().getPendingOrderId(),
                    Collectors.mapping(
                        p ->
                            PendingOrderResponse.StandingOrder.builder()
                                .standingOrderId(p.getStandingOrderId())
                                .standingOrderNumber(p.getStandingOrderNumber())
                                .standingDescription(p.getStandingDescription())
                                .build(),
                        Collectors.toSet())));
    return new HashSet<>(result)
        .stream()
            .map(
                pendingOrderStandingOrder -> {
                  PendingOrder po = pendingOrderStandingOrder.getPendingOrder();
                  Title t = po.getTitle();
                  Optional<MatchedProduct> matchedProduct =
                      t.findActiveMatchedProductByIsbn(po.getOrderedProductReference());
                  DuplicateOrder duplicateOrder =
                      orderService.findDuplicateOrder(
                          po.getCustomer().getCustomerId(),
                          po.getOrderedProductReference(),
                          matchedProduct.map(MatchedProduct::getProductTitle).orElse(null),
                          matchedProduct.map(MatchedProduct::getSubtitle).orElse(null),
                          matchedProduct.map(MatchedProduct::getPersonName).orElse(null),
                          StringUtils.isNotEmpty(po.getOrderNumber())
                              ? Lists.newArrayList(po.getOrderNumber())
                              : null);
                  po.setOrderCount(duplicateOrder.getOrderCount());
                  po.setDuplicateOrder(duplicateOrder.getOrderCount() > 0 ? duplicateOrder : null);
                  Set<PendingOrderResponse.StandingOrder> standingOrders =
                      standingOrderMap.get(po.getPendingOrderId());
                  return PendingOrderResponse.from(po, standingOrders);
                })
            .collect(Collectors.toList());
  }

  public List<ProcessFormatResponse> findByTitleGroupByFormat(TitleId titleId) {
    return pendingOrderRepository.findByTitleGroupByFormat(titleId);
  }

  public List<PendingOrder> findByOrderedProductReferenceAndTitleTitleId(
      String isbn, TitleId titleId) {
    return pendingOrderRepository.findByOrderedProductReferenceAndTitleTitleId(isbn, titleId);
  }

  public List<PendingOrder> findByIds(List<PendingOrderId> pendingOrderIds) {
    return pendingOrderRepository.findAllByPendingOrderIdIn(pendingOrderIds);
  }

  public List<PendingOrder> findByTitleTitleIdAndOrderStatus(
      TitleId titleId, PendingOrderStatus status) {
    return pendingOrderRepository.findByTitleTitleIdAndOrderStatus(titleId, status);
  }

  public boolean noDuplicationByTitleFormatAndAllocation(
      TitleId titleId, ReleaseFormat format, AllocationId allocationId) {
    List<PendingOrder> existingPendingOrders =
        pendingOrderRepository.findAllByTitleIdAndFormatAndAllocationId(
            titleId, format, allocationId);
    return CollectionUtils.isEmpty(existingPendingOrders);
  }

  public List<PendingOrder> findAllValidPendingOrdersByTitle(TitleId titleId) {
    return findByTitle(titleId).stream().filter(po -> !po.isInvalid()).collect(Collectors.toList());
  }

  public List<PendingOrder> findByTitle(TitleId titleId) {
    return pendingOrderRepository.findByTitleTitleId(titleId);
  }

  @Transactional
  public PendingOrder save(PendingOrder pendingOrder) {
    return this.pendingOrderRepository.save(pendingOrder);
  }

  public List<PendingOrder> saveAll(List<PendingOrder> pendingOrders) {
    return pendingOrderRepository.saveAll(pendingOrders);
  }

  public List<PendingOrder> findByTitleIdAndStandingOrderId(
      TitleId titleId, List<StandingOrderId> standingOrderIds) {
    return pendingOrderRepository.findByTitleIdAndStandingOrderId(titleId, standingOrderIds);
  }

  public void deletePendingOrders(List<PendingOrder> pendingOrders) {
    List<PendingOrderDeleted> pendingOrderDeletedList =
        pendingOrders.stream()
            .map(po -> PendingOrderDeleted.from(po.getPendingOrderId(), "system"))
            .collect(Collectors.toList());
    pendingOrderRepository.deleteAll(pendingOrders);
    pendingOrderRepository.flush();
    pendingOrderDeletedList.forEach(eventPublisher::publishEvent);
  }

  public List<PendingOrder> findAllNewPendingOrderByTitleIdAllocationId(
      TitleId titleId, AllocationId allocationId) {
    return pendingOrderRepository.findAllByTitleIdAllocationId(titleId, allocationId, PendingOrderStatus.NEW);
  }

  public List<PendingOrder> findAllPendingOrderByTitleIdAllocationId(
      TitleId titleId, AllocationId allocationId) {
    return pendingOrderRepository.findAllByTitleIdAllocationId(titleId, allocationId, null);
  }

  public List<PendingOrder> findAllByTitleIdsAllocationId(
      List<TitleId> titleIds, AllocationId allocationId) {
    return pendingOrderRepository.findAllByTitleIdsAllocationId(titleIds, allocationId);
  }
}

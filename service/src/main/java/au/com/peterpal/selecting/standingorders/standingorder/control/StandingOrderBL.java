package au.com.peterpal.selecting.standingorders.standingorder.control;

import static org.springframework.util.StringUtils.hasText;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.audit.control.AuditDomainBL;
import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.customerstandingorder.control.CustomerStandingOrderRepository;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerService;
import au.com.peterpal.selecting.standingorders.standingorder.commands.CreateStandingOrderCmd;
import au.com.peterpal.selecting.standingorders.standingorder.dto.*;
import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderCreated;
import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderUpdated;
import au.com.peterpal.selecting.standingorders.standingorder.match.LegacyMatcher;
import au.com.peterpal.selecting.standingorders.standingorder.match.MatchResult;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductMatchInfo;
import au.com.peterpal.selecting.standingorders.standingorder.model.*;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import au.com.peterpal.selecting.standingorders.utils.StringAffirm;
import com.google.common.base.Enums;
import java.util.*;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Log4j2
@RequiredArgsConstructor
@Transactional
@Component
public class StandingOrderBL {

  @Value("${matcher.ignore-pub-date.before-months-ago:3}")
  private Integer minPublicationDateOffset;

  @Value("${matcher.ignore-pub-date.after-months-ahead:6}")
  private Integer maxPublicationDateOffset;

  @Value("#{'${match-service.restricted-publishers}'.split(';')}")
  private List<String> restrictedPublishers;

  @Value("${match-service.regex-remove-char:[()’']}")
  private String regexRemoveChar;

  @Value("${match-service.regex-replace-char-with-space:[.-]+}")
  private String regexReplaceCharWithSpace;

  private final StandingOrderRepository standingOrderRepository;
  private final StandingOrderNumberGenerator standingOrderNumberGenerator;
  private final CustomerService customerService;
  private final EventPublisher publisher;
  private final CustomerStandingOrderRepository customerStandingOrderRepository;
  private final AuditDomainBL auditDomainBL;
  private final CombinedTermRepository combinedTermRepository;

  public UUID handle(CreateStandingOrderCmd cmd) {
    return handle(cmd, false);
  }

  @Transactional
  public UUID handle(CreateStandingOrderCmd cmd, boolean testFlag) {
    validate(cmd);

    if (!hasText(cmd.getStandingOrderNumber())) {
      cmd.setStandingOrderNumber(standingOrderNumberGenerator.getNextItemNumber());
    }

    StandingOrderCreated event = StandingOrderCreated.fromCmd(cmd);

    if (!testFlag) {
      StandingOrder standingOrder = StandingOrder.from(event);
      createCombinedTerms(standingOrder.getTerms());
      StandingOrder saved = standingOrderRepository.save(standingOrder);
      event.setTerms(saved.getTerms());
      publish(event);
    }
    return event.getId();
  }

  private void createCombinedTerms(List<Term> terms) {
    terms.stream()
        .map(Term::getCombinedTerm)
        .map(CombinedTerm::getCombinedTermId)
        .distinct()
        .forEach(
            combinedTermId -> {
              Optional<CombinedTerm> existingCombinedTerm = combinedTermRepository.findById(combinedTermId);

              if (existingCombinedTerm.isEmpty()) {
                CombinedTerm newCombinedTerm = CombinedTerm.builder().combinedTermId(combinedTermId).build();
                combinedTermRepository.save(newCombinedTerm);
              }
            });
  }

  public StandingOrderCreated handleImport(CreateStandingOrderCmd cmd, boolean testFlag) {
    validate(cmd);

    if (!hasText(cmd.getStandingOrderNumber())) {
      cmd.setStandingOrderNumber(standingOrderNumberGenerator.getNextItemNumber());
    }
    return StandingOrderCreated.fromCmd(cmd);
  }

  public void persistImportData(List<StandingOrderCreated> StandingOrderCreatedList) {
    List<StandingOrder> standingOrderList =
        StandingOrderCreatedList.stream().map(StandingOrder::from).collect(Collectors.toList());

    log.trace("before saving the standing orders: " + standingOrderList.size());
    createCombinedTerms(
        standingOrderList.stream()
            .map(StandingOrder::getTerms)
            .flatMap(Collection::stream)
            .collect(Collectors.toList()));
    List<StandingOrder> standingOrdersSaved = standingOrderRepository.saveAll(standingOrderList);
    log.trace("after saving the standing orders: ");

    Map<String, StandingOrder> standingOrdersSavedMap =
        standingOrdersSaved.stream()
            .collect(Collectors.toMap(e -> e.getStandingOrderNumber(), e -> e));

    List<DomainEvent> standingOrderCreatedList =
        StandingOrderCreatedList.stream()
            .map(StandingOrderCreated::fromEvent)
            .map(
                e -> {
                  if (standingOrdersSavedMap.containsKey(e.getStandingOrderNumber())) {
                    e.setTerms(standingOrdersSavedMap.get(e.getStandingOrderNumber()).getTerms());
                  }
                  return e;
                })
            .map(e -> (DomainEvent) e)
            .collect(Collectors.toList());

    log.trace("before saving the standing order domain event: " + standingOrderList.size());
    auditDomainBL.handleCustom(standingOrderCreatedList);
    log.trace("after saving the standing order domain event: ");
  }

  public List<Allocation> getAllocations(Category category, List<StandingOrder> standingOrders) {
    List<Allocation> allocations = new ArrayList<>();
    standingOrders.forEach(
        standingOrder -> {
          List<CustomerStandingOrder> customerStandingOrders =
              customerStandingOrderRepository
                  .findByStandingOrderAndStandingOrderStandingOrderStatus(
                      standingOrder, StandingOrderStatus.ACTIVE);

          // interest only parent Allocation (exclude branch allocation)
          customerStandingOrders.forEach(
              cso ->
                  cso.getAllocations().stream()
                      .filter(a -> Objects.isNull(a.getBranch()))
                      .filter(a -> AllocationStatus.ACTIVE.equals(a.getStatus()))
                      .filter(allocation -> allocation.containsCategory(category))
                      .forEach(allocations::add));
        });
    return allocations;
  }

  public Page<StandingOrderResponse> handle(
      StandingOrderRequest searchRequest, Pageable pageRequest) {
    Affirm.of(searchRequest).notNull("SearchOrderRequest must not be null");
    if (Objects.nonNull(searchRequest.getCustomerId())) {
      customerService.validateIfExist(searchRequest.getCustomerId());
    }

    return standingOrderRepository.search(searchRequest, pageRequest);
  }

  public List<StandingOrderResponse> handle(StandingOrderRequest searchRequest) {
    List<StandingOrder> result = standingOrderRepository.searchStandingOrder(searchRequest);
    return result.stream().map(StandingOrderResponse::from).collect(Collectors.toList());
  }

  @Transactional
  public UUID handle(
      UpdateStandingOrderRequest request, StandingOrderId standingOrderId, String username) {
    Affirm.of(request).notNull("UpdateStandingOrderRequest must not ben null");
    Affirm.of(standingOrderId).notNull("StandingOrderId must not be null");

    Optional<StandingOrder> opt = standingOrderRepository.findById(standingOrderId);
    if (!opt.isPresent()) {
      throw new ResourceNotFoundException(StandingOrder.class, String.valueOf(standingOrderId));
    }

    if (!Enums.getIfPresent(StandingOrderStatus.class, request.getStatus()).isPresent()) {
      throw new ResourceNotFoundException("Invalid status received");
    }

    List<CustomerStandingOrder> customerStandingOrders =
        customerStandingOrderRepository.findAllByStandingOrderStandingOrderId(standingOrderId);
    boolean notContainsPausedAllocation =
        customerStandingOrders.stream().noneMatch(CustomerStandingOrder::containsPausedAllocation);

    StandingOrder standingOrder = opt.get();
    standingOrder =
        standingOrder.toBuilder()
            .description(request.getDescription())
            .notes(request.getNotes())
            .build();

    List<Term> termList = TermsRequest.from(request.getTerms());

    if (CollectionUtils.isNotEmpty(termList)) {
      Set<CombinedTermId> combinedTermIds =
          termList.stream()
              .filter(term -> term.getCombinedTerm() != null)
              .map(term -> term.getCombinedTerm().getCombinedTermId())
              .collect(Collectors.toSet());

      standingOrder.getTerms().stream()
          .filter(term -> term.getCombinedTerm() != null)
          .forEach(
              term -> {
                CombinedTerm combinedTerm = term.getCombinedTerm();
                CombinedTermId combinedTermId = combinedTerm.getCombinedTermId();

                // If the combinedTermId is not in the new termList, clear its terms
                if (!combinedTermIds.contains(combinedTermId)) {
                  combinedTerm.getTerms().clear();
                }

                // Remove terms that are no longer part of the new termList
                if (combinedTermIds.contains(combinedTermId)
                    && termList.stream().noneMatch(t -> t.getTermId().equals(term.getTermId()))) {
                  combinedTerm.removeTerm(term);
                }
              });
      standingOrder.getTerms().clear();
      standingOrder.addTerm(termList);
    }

    if (StandingOrderStatus.valueOf(request.getStatus()) == StandingOrderStatus.INACTIVE
        && notContainsPausedAllocation) {
      standingOrder.setStandingOrderStatus(StandingOrderStatus.INACTIVE);
      customerStandingOrders.forEach(
          cso -> {
            cso.setCustomerStandingOrderStatus(StandingOrderStatus.INACTIVE);
            cso.getAllocations()
                .forEach(allocation -> allocation.setStatus(AllocationStatus.INACTIVE));
          });
      customerStandingOrderRepository.saveAll(customerStandingOrders);
    }

    return updateStandingOrder(standingOrder, username);
  }

  public UUID updateStandingOrder(StandingOrder standingOrder, String username) {
    createCombinedTerms(standingOrder.getTerms());
    standingOrder = standingOrderRepository.save(standingOrder);
    StandingOrderUpdated standingOrderUpdated = StandingOrderUpdated.from(standingOrder, username);
    publisher.publishEvent(standingOrderUpdated);
    return standingOrderUpdated.getId();
  }

  public StandingOrder findByNumber(String number) {
    StringAffirm.of(number).hasText("Standing order number must not be blank");

    return standingOrderRepository
        .findByStandingOrderNumber(number)
        .orElseThrow(
            () ->
                new ResourceNotFoundException(
                    String.format("Standing order with number %s not found", number)));
  }

  public StandingOrder findById(StandingOrderId standingOrderId) {
    Affirm.of(standingOrderId).notNull("standingOrderId must not be null");
    return standingOrderRepository
        .findById(standingOrderId)
        .orElseThrow(
            () ->
                new ResourceNotFoundException(
                    StandingOrder.class, String.valueOf(standingOrderId)));
  }

  public MatchResult match(StandingOrderId soId, ProductMatchInfo product, boolean publish) {
    Optional.ofNullable(soId)
        .orElseThrow(() -> new IllegalArgumentException("soId must not be null"));
    Optional.ofNullable(product)
        .orElseThrow(() -> new IllegalArgumentException("product must not be null"));

    Optional<SOSummary> optSummary = standingOrderRepository.findSummaryById(soId);
    return optSummary
        .map(
            summary ->
                LegacyMatcher.of(
                        summary,
                        restrictedPublishers,
                        minPublicationDateOffset,
                        maxPublicationDateOffset,
                        regexRemoveChar,
                        regexReplaceCharWithSpace)
                    .match(product))
        .orElseThrow(
            () -> new BusinessException(String.format("SO summary not found for id %s", soId)));
  }

  public Map<String, StandingOrder> getAllStandingOrders() {
    return standingOrderRepository.findByStandingOrderStatus(StandingOrderStatus.ACTIVE).stream()
        .collect(Collectors.toMap(e -> e.getStandingOrderNumber(), e -> e));
  }

  private void validate(CreateStandingOrderCmd cmd) {
    Affirm.of(cmd).notNull("CreateStandingOrderCmd must not be null");

    if (cmd.getTerms().isEmpty()) {
      throw new BusinessException("A new standing order must have at least one term");
    }

    if (hasText(cmd.getStandingOrderNumber())) {
      Optional<StandingOrder> optional =
          standingOrderRepository.findByStandingOrderNumber(cmd.getStandingOrderNumber());
      if (optional.isPresent()) {
        throw new BusinessException(
            String.format(
                "Standing order number %s, already exists.", cmd.getStandingOrderNumber()));
      }
    }
  }

  private UUID publish(DomainEvent event) {
    return Optional.ofNullable(event)
        .map(
            e -> {
              publisher.publishEvent(event);
              return event.getId();
            })
        .orElseThrow(() -> new IllegalArgumentException("Can't publish a null event"));
  }

  public List<UUID> bulkUpdate(BulkUpdateStandingOrderRequest request, String username) {
    List<StandingOrderId> standingOrderIds;
    if (CollectionUtils.isNotEmpty(request.getIncludedStandingOrderIds())) {

      standingOrderIds = request.getIncludedStandingOrderIds();
    } else {
      standingOrderIds =
          standingOrderRepository.getStandingOrderIdsForBulkUpdate(
              request.getSearchRequest(), request.getExcludedStandingOrderIds());
    }

    Long noUpdated = standingOrderRepository.bulkUpdate(standingOrderIds, request);
    log.debug("Updated {} records to standing_order table", noUpdated);
    List<StandingOrder> updatedStandingOrders =
        standingOrderRepository.findAllById(standingOrderIds);
    updatedStandingOrders.forEach(
        standingOrder -> {
          StandingOrderUpdated standingOrderUpdated =
              StandingOrderUpdated.from(standingOrder, username);
          publisher.publishEvent(standingOrderUpdated);
        });
    List<UUID> updatedStandingOrderIds =
        updatedStandingOrders.stream()
            .map(standingOrder -> standingOrder.getStandingOrderId().getId())
            .collect(Collectors.toList());
    log.debug("Done for bulk update of standing order ids: {}", updatedStandingOrderIds);
    return updatedStandingOrderIds;
  }
}

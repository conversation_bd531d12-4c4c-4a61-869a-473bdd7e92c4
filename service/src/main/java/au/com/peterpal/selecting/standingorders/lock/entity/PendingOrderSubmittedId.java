package au.com.peterpal.selecting.standingorders.lock.entity;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.UUID;
import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;

@Embeddable
public class PendingOrderSubmittedId extends UuidEntityId {
  public static PendingOrderSubmittedId of(@NotEmpty UUID id) {
    return new PendingOrderSubmittedId(id);
  }

  public static PendingOrderSubmittedId of(@NotEmpty String id) {
    return new PendingOrderSubmittedId(UUID.fromString(id));
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public PendingOrderSubmittedId(@NotEmpty UUID id) {
    super(id);
  }

  public PendingOrderSubmittedId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

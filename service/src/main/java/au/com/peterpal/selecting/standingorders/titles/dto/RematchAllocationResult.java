package au.com.peterpal.selecting.standingorders.titles.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.pendingorder.model.BranchDistribution;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.*;
import org.apache.commons.compress.utils.Lists;

@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class RematchAllocationResult {
  Title title;
  List<PendingOrderDetail> pendingOrdersCreated;
  List<PendingOrderDetail> pendingOrdersUpdated;

  @Value
  @NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
  @AllArgsConstructor(staticName = "of")
  @Builder
  public static class PendingOrderDetail {
    PendingOrderId pendingOrderId;
    Integer quantity;
    ReleaseFormat format;
    Set<BranchDistribution> branchDistributions;
  }

  public static RematchAllocationResult from(
      Title rematchedTitle,
      List<PendingOrder> pendingOrdersCreated,
      List<PendingOrder> pendingOrdersUpdated) {

    return RematchAllocationResult.of(
        rematchedTitle,
        Optional.ofNullable(pendingOrdersCreated).orElse(Lists.newArrayList()).stream()
            .map(
                pendingOrder ->
                    PendingOrderDetail.builder()
                        .pendingOrderId(pendingOrder.getPendingOrderId())
                        .quantity(pendingOrder.getQuantity())
                        .format(pendingOrder.getFormat())
                        .branchDistributions(pendingOrder.getBranchDistributions())
                        .build())
            .collect(Collectors.toList()),
        Optional.ofNullable(pendingOrdersUpdated).orElse(Lists.newArrayList()).stream()
            .map(
                pendingOrder ->
                    PendingOrderDetail.builder()
                        .pendingOrderId(pendingOrder.getPendingOrderId())
                        .quantity(pendingOrder.getQuantity())
                        .format(pendingOrder.getFormat())
                        .branchDistributions(pendingOrder.getBranchDistributions())
                        .build())
            .collect(Collectors.toList()));
  }
}

package au.com.peterpal.selecting.standingorders.standingorder.boundary;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocation.commands.SearchAllocationsByStandingOrder;
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationBL;
import au.com.peterpal.selecting.standingorders.allocation.dto.AllocationInfo;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.ext.category.control.CategoryRepository;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus;
import au.com.peterpal.selecting.standingorders.customerstandingorder.control.CustomerStandingOrderBL;
import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.AllocationBranchInfo;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.ClientWebApiGateway;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.CwProductInfo;
import au.com.peterpal.selecting.standingorders.products.control.ProductRepository;
import au.com.peterpal.selecting.standingorders.standingorder.control.CategoryService;
import au.com.peterpal.selecting.standingorders.standingorder.control.RejectionReasonTypeService;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderBL;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderRepository;
import au.com.peterpal.selecting.standingorders.standingorder.dto.*;
import au.com.peterpal.selecting.standingorders.standingorder.events.MatchFound;
import au.com.peterpal.selecting.standingorders.standingorder.match.MatchResult;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductChangeListener;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductMatchInfo;
import au.com.peterpal.selecting.standingorders.standingorder.model.*;
import au.com.peterpal.selecting.standingorders.titles.control.MatchAndMergeTitleService;
import au.com.peterpal.selecting.standingorders.titles.dto.RematchStandingOrderProductRequest;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import au.com.peterpal.selecting.standingorders.utils.StringAffirm;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Log4j2
@RestController
@RequiredArgsConstructor
@SecurityRequirement(name = "BearerAuth")
@RequestMapping("/api/standing-orders")
public class StandingOrderController {

  private static final String RECEIVED_REQUEST_MSG = "Received request from user %s: %s";
  private static final String RECEIVED_REQUEST_OP_MSG = "Received request from user %s: %s %s";

  private final StandingOrderBL standingOrderBL;
  private final AllocationBL allocationBL;
  private final StandingOrderRepository standingOrderRepository;
  private final MessageChannel productChannel;
  private final CategoryService catService;
  private final CategoryRepository categoryRepository;

  private final ClientWebApiGateway clientWebApiGateway;

  private final CustomerStandingOrderBL customerStandingOrderBL;
  private final RejectionReasonTypeService rejectionReasonTypeService;
  private final ProductRepository productRepository;
  private final MessageChannel rematchStandingOrderOutboundChannel;
  private final MatchAndMergeTitleService matchAndMergeTitleService;

  @PostMapping
  public UUID createStandingOrder(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestBody @Valid CreateStandingOrderRequest request) {
    log.debug(String.format(RECEIVED_REQUEST_MSG, username, request));

    return standingOrderBL.handle(request.toCmd(username));
  }

  @GetMapping
  public List<StandingOrderDTO> findAll() {
    return StandingOrderDTO.from(standingOrderRepository.findAll());
  }

  @GetMapping("/{standingOrderId}")
  public StandingOrderDTO findById(@PathVariable String standingOrderId) {
    return StandingOrderDTO.from(standingOrderRepository
        .findById(StandingOrderId.of(standingOrderId))
        .orElseThrow(() -> new ResourceNotFoundException(StandingOrder.class, standingOrderId)));
  }

  @GetMapping("/search")
  public Page<StandingOrderResponse> search(
      @RequestParam(value = "status", required = false) StandingOrderStatus status,
      @RequestParam(value = "customerId", required = false) String customerId,
      @RequestParam(value = "standingOrderNumber", required = false) String standingOrderNumber,
      @RequestParam(value = "description", required = false) String description,
      @RequestParam(value = "term", required = false) String term,
      @RequestParam(value = "categories", required = false) List<String> categories,
      @RequestParam(value = "notes", required = false) String notes,
      @RequestParam(required = false) String sortByKey,
      @RequestParam(required = false) String sortByDirection,
      @PageableDefault(direction = Sort.Direction.ASC, sort = "standingOrderNumber")
      Pageable pageRequest) {

    StandingOrderRequest searchRequest =
        StandingOrderRequest.builder()
            .standingOrderStatus(status)
            .customerId(Optional.ofNullable(customerId).map(CustomerId::of).orElse(null))
            .description(description)
            .standingOrderNumber(standingOrderNumber)
            .term(term)
            .categories(categories)
            .notes(notes)
            .sortByKey(sortByKey)
            .sortByDirection(sortByDirection)
            .build();

    return standingOrderBL.handle(searchRequest, pageRequest);
  }

  @PostMapping("/term-searches")
  public List<StandingOrderResponse> search(@RequestParam(value = "term") String term) {
    return standingOrderBL.handle(StandingOrderRequest.builder().term(term).build());
  }

  @GetMapping("/{standingOrderId}/allocations/search")
  public Page<AllocationInfo> search(
      @PathVariable("standingOrderId") StandingOrderId standingOrderId,
      @RequestParam(required = false) AllocationStatus status,
      @RequestParam(required = false) String customerCode,
      @RequestParam(value = "categories", required = false) List<String> categories,
      @PageableDefault(
          sort = "allocation.customerStandingOrder.customer.code",
          direction = Sort.Direction.ASC)
      Pageable pageRequest) {

    SearchAllocationsByStandingOrder searchRequest =
        SearchAllocationsByStandingOrder.builder()
            .standingOrderId(standingOrderId)
            .allocationStatus(status)
            .categories(categories)
            .customerCode(customerCode)
            .pageRequest(pageRequest)
            .build();

    return allocationBL.handle(searchRequest);
  }

  @PutMapping("/{standingOrderId}")
  public UUID updateStandingOrder(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable StandingOrderId standingOrderId,
      @RequestBody @Valid UpdateStandingOrderRequest request) {

    log.debug(String.format(RECEIVED_REQUEST_OP_MSG, username, request, " . UPDATE MODE"));
    return standingOrderBL.handle(request, standingOrderId, username);
  }
  @PutMapping("/bulk-update")
  public List<UUID> bulkUpdateStandingOrders(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestBody @Valid BulkUpdateStandingOrderRequest request) {

    log.debug(String.format(RECEIVED_REQUEST_OP_MSG, username, request, " . UPDATE MODE"));
    return standingOrderBL.bulkUpdate(request, username);
  }
  @PostMapping("/{standingOrderId}/rematch")
  public void updateStandingOrder(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable StandingOrderId standingOrderId) {
    productRepository
        .findAllByLastImportDateBetween(LocalDateTime.now().minusMonths(3), LocalDateTime.now())
        .stream()
        .forEach(
            product ->
                rematchStandingOrderOutboundChannel.send(
                    MessageBuilder.withPayload(
                            RematchStandingOrderProductRequest.builder()
                                .standingOrderId(standingOrderId)
                                .productReference(product.getProductReference())
                                .build())
                        .build()));
  }

  @PostMapping("/{standingOrderId}/match")
  @Operation(
      summary =
          "Match product info to a standing order; No title will be generated unless publish is true")
  public MatchResult matchStandingOrder(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable StandingOrderId standingOrderId,
      @RequestParam(value = "publish", required = false, defaultValue = "false") boolean publish,
      @RequestBody @Valid ProductMatchInfo product) {

    String str = "Received request from user %s: %s, %s";
    log.debug(String.format(str, username, standingOrderId, product));

    StandingOrder standingOrder = standingOrderBL.findById(standingOrderId);
    MatchResult result = standingOrderBL.match(standingOrderId, product, publish);

    if (result.isMatched()) {
      log.info(
              String.format(
                      "Found match for standing order %s and product id %d",
                      standingOrder.getStandingOrderNumber(), product.getId()));
      if (publish) {
        MatchFound event =
                MatchFound.builder()
                        .id(UUID.randomUUID())
                        .username(username)
                        .soId(standingOrder.getStandingOrderId().getId())
                        .number(standingOrder.getStandingOrderNumber())
                        .productId(product.getId())
                        .subjectCode1(product.getBicSubjectCode1())
                        .subjectCodeList(product.getSubjectCodeList())
                        .note(result.getNote())
                        .productReference(product.getProductReference())
                        .formDetails(product.getFormDetails())
                        .width(product.getWidth())
                        .height(product.getHeight())
                        .title(product.getTitle().getWithoutPrefix())
                        .subtitle(product.getTitle().getSubtitle())
                        .series(product.getSeriesTitle() == null ? "" : product.getSeriesTitle().getText())
                        .matchedPersonName(ProductChangeListener.getPersonName(product, result))
                        .imprint(ProductChangeListener.getImprint(product))
                        .publicationDate(product.getPublicationDate())
                        .build();
        matchAndMergeTitleService.handle(event);
      }
    }

    return result;
  }

  @PostMapping("/products")
  @Operation(summary = "Apply a product message in JSON format")
  public void applyProduct(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestBody String request) {
    log.debug(String.format(RECEIVED_REQUEST_MSG, username, request));
    productChannel.send(MessageBuilder.withPayload(request).build());
  }

  @GetMapping("/categories/{subject}")
  @Operation(summary = "Return category information for a given subject code")
  public CategoryInfo getCategoryInfo(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable String subject) {
    log.trace(String.format(RECEIVED_REQUEST_MSG, username, subject));

    return catService.getCategory(subject);
  }

  @GetMapping("/categories")
  @Operation(summary = "Returns a list of mapped category codes in alphabetical order")
  public List<String> getCategories(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username) {
    log.trace(String.format("Received get categories request from %s", username));

    return categoryRepository.findAllByStatusOrderByCode(CategoryStatus.ACTIVE).stream()
        .map(Category::getCode)
        .collect(Collectors.toList());
  }

  @GetMapping("/products/find-by-isbn")
  @Operation(summary = "Returns a product by isbn")
  public CwProductInfo getProducts(@RequestParam String isbn) {
    return clientWebApiGateway.searchProductByIsbn(isbn);
  }


  @GetMapping("/{standingOrderId}/branch-allocations")
  public AllocationBranchInfo getBranchAllocations(
      @RequestHeader(value = "username", defaultValue = "") String username,
      @PathVariable UUID standingOrderId,
      @RequestParam UUID customerId,
      @RequestParam String category,
      @RequestParam UUID baParentId
  ) {
    Affirm.of(standingOrderId).notNull("Standing order id must not be null");
    Affirm.of(customerId).notNull("Customer id must not be null");
    StringAffirm.of(category).hasText("Category must have value");

    String msg = "Received get branch allocations for customer id, standing order id, category, baParentId: %s, %s, %s, %s";
    log.debug(String.format(msg, customerId, standingOrderId, category, baParentId));

    CustomerId ci = CustomerId.of(customerId);
    StandingOrderId soid = StandingOrderId.of(standingOrderId);
    AllocationId bapi = AllocationId.of(baParentId);
    return customerStandingOrderBL.getAllocationAndBranches(ci, soid, category, bapi, username);
  }

  @GetMapping("/{standingOrderId}/allocations/{allocationId}")
  public AllocationBranchInfo getBranchAllocations(
      @PathVariable UUID standingOrderId, @PathVariable UUID allocationId) {
    return customerStandingOrderBL.getAllocationWithBranches(
        StandingOrderId.of(standingOrderId), AllocationId.of(allocationId));
  }

  @GetMapping(path = "/rejection-reason/all")
  @Operation(summary = "Get all rejection reasons")
  public List<RejectionReasonTypeInfo> getAllRejectionReason() {
    log.trace(() -> "Get all rejection reasons");
    return RejectionReasonTypeInfo.from(
        rejectionReasonTypeService.findActiveAndDisplayable(Sort.by(Sort.Direction.ASC, "rejectionReasonTypeId")));
  }
}

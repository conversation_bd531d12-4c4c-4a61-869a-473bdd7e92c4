package au.com.peterpal.selecting.standingorders.standingorder.control;

import au.com.peterpal.selecting.standingorders.standingorder.dto.StandingOrderUpdatedMessage;
import au.com.peterpal.selecting.standingorders.standingorder.events.MatchFound;
import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderCreated;
import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderUpdated;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductChangeListener;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductMatchInfo;
import au.com.peterpal.selecting.standingorders.standingorder.model.SORematchSummary;
import au.com.peterpal.selecting.standingorders.standingorder.model.SOSummary;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.SubjectMapEntry;
import au.com.peterpal.selecting.standingorders.titles.control.MatchAndMergeTitleService;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Log4j2
@Service
@RequiredArgsConstructor
@Transactional
public class MatchingService {

  static final String PRODUCT_OBSERVERS_PROPERTY = "productObservers";

  private final StandingOrderRepository standingOrderRepository;
  private final SubjectMapRepository subjectMapRepository;
  private final MatchAndMergeTitleService matchAndMergeTitleService;

  CopyOnWriteMap<UUID, ProductChangeListener> observers = new CopyOnWriteMap<UUID, ProductChangeListener>();
  @Getter
  private long loadTime = 0;

  @Value("${matcher.ignore-pub-date.before-months-ago:3}")
  private Integer minPublicationDateOffset;

  @Value("${matcher.ignore-pub-date.after-months-ahead:6}")
  private Integer maxPublicationDateOffset;

  @Value("#{'${match-service.restricted-publishers}'.split(';')}")
  private List<String> restrictedPublishers;

  @Value("${match-service.regex-remove-char:[()’']}")
  private String regexRemoveChar;

  @Value("${match-service.regex-replace-char-with-space:[.-]+}")
  private String regexReplaceCharWithSpace;

  @Setter
  @Value("${skip-matching-engine-initialisation:false}")
  private Boolean developmentMode = Boolean.FALSE;

  @PostConstruct
  public void init() {
    if (!developmentMode) {

      long start = System.currentTimeMillis();
      List<SOSummary> summaryList = standingOrderRepository.findAllSummary();
      summaryList.forEach(this::addObserver);
      loadTime = System.currentTimeMillis() - start;
    }
  }

  public int observerMapSize() {
    return observers.size();
  }

  public Optional<SOSummary> findObserver(UUID id) {
    Affirm.of(id).notNull("Observer key must not be null");
    return Optional.ofNullable(observers.get(id)).map(ProductChangeListener::getSummary);
  }

  public int countObserverMap() {
    return observers.size();
  }

  /**
   * Returns the current map of BIC subjects to PPLS categories.
   *
   * @return a list of <@code>SubjectMapEntry</@code>
   */
  public List<SubjectMapEntry> subjectMap() {
    return subjectMapRepository.findAll();
  }

  public void deleteAllSubjectMapEntries() {
    subjectMapRepository.deleteAll();
  }

  public void deleteSubjectMapEntries(List<String> subjectCodes) {
    Optional.of(subjectCodes)
        .filter(l -> !l.isEmpty())
        .ifPresent(l -> l.forEach(subjectMapRepository::deleteById));
  }

  public List<SubjectMapEntry> addSubjectMapEntries(List<SubjectMapEntry> mapEntryList) {
    Optional.of(mapEntryList)
        .filter(l -> !l.isEmpty())
        .ifPresent(l -> l
            .forEach(e -> subjectMapRepository.getOne(e.getSubject()))
        );

    return subjectMapRepository.findAll();
  }

  public void matchAndHandle(ProductMatchInfo productInfo) {
    Affirm.of(productInfo).notNull("Product must not be null");
    this.match(productInfo).forEach(matchAndMergeTitleService::handle);
  }

  public List<MatchFound> match(ProductMatchInfo productInfo) {
    log.debug("Performing match for {}", productInfo.getProductReference());
    List<MatchFound> matchFounds =
        getObservers().values().stream()
            .map(productChangeListener -> productChangeListener.doMatching(productInfo))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    log.debug("match process for {} done", productInfo.getProductReference());
    return matchFounds;
  }

  public Optional<MatchFound> matchSingleStandingOrder(
      ProductMatchInfo productInfo, StandingOrderId standingOrderId) {
    MatchFound matchFound = null;
    Optional<ProductChangeListener> pcs =
        Optional.ofNullable(getObservers().get(standingOrderId.getId()));
    if (pcs.isPresent()) {
      log.debug(
          "Performing re-match for {} with {}", productInfo.getProductReference(), standingOrderId);
      matchFound = pcs.get().doMatching(productInfo);
      log.debug("re-match for {} with {} done", productInfo.getProductReference(), standingOrderId);
    } else {
      log.warn("no standing order found in the matching engine {}", standingOrderId);
    }
    return Optional.ofNullable(matchFound);
  }

  @EventListener
  public void on(final StandingOrderCreated standingOrderCreated) {
    Affirm.of(standingOrderCreated).notNull("StandingOrderCreated must not be null");

    standingOrderRepository
        .findSummaryById(new StandingOrderId(standingOrderCreated.getId()))
        .filter(s -> !observers.containsKey(s.getId()))
        .ifPresent(this::addObserver);
  }

  @EventListener
  public void on(final StandingOrderUpdated standingOrderUpdated) {
    Affirm.of(standingOrderUpdated).notNull("StandingOrderUpdated must not be null");
    Affirm.of(standingOrderUpdated.getId())
        .notNull(String.format("Standing order id must not be null: %s", standingOrderUpdated));

    if (observers.containsKey(standingOrderUpdated.getId())) {
      deleteObserver(standingOrderUpdated.getId());
    }

    standingOrderRepository
        .findSummaryById(new StandingOrderId(standingOrderUpdated.getId()))
        .ifPresent(this::addObserver);
  }

  public StandingOrderUpdatedMessage updateObserversFromOtherInstances(
      final StandingOrderUpdatedMessage standingOrderUpdatedMessage) {
    Affirm.of(standingOrderUpdatedMessage)
        .notNull("standingOrderUpdatedMessage from other instance must not be null");
    Affirm.of(standingOrderUpdatedMessage.getId())
        .notNull(
            String.format(
                "Standing order id from other instance must not be null: %s",
                standingOrderUpdatedMessage));

    if (observers.containsKey(standingOrderUpdatedMessage.getId())) {
      deleteObserver(standingOrderUpdatedMessage.getId());
    }

    if (standingOrderUpdatedMessage.getStatus().equals("ACTIVE")) {
      addObserver(SORematchSummary.from(standingOrderUpdatedMessage));
    }
    return standingOrderUpdatedMessage;
  }

  public CopyOnWriteMap<UUID, ProductChangeListener> getObservers() {
    return observers;
  }

  private void addObserver(SOSummary summary) {
    UUID id = summary.getId();
    ProductChangeListener obs =
        ProductChangeListener.of(
            summary,
            restrictedPublishers,
            minPublicationDateOffset,
            maxPublicationDateOffset,
            regexRemoveChar,
            regexReplaceCharWithSpace);
    observers.put(id, obs);
  }

  private void deleteObserver(UUID id) {
    ProductChangeListener obs = observers.get(id);
    observers.remove(id);
  }

  // This class is to avoid concurrent modification exceptions when updates to the standing orders
  // are received from the ui when the matching process is in operation.
  public class CopyOnWriteMap<K, V> {

    private volatile Map<K, V> internalMap = Collections.unmodifiableMap(new HashMap<>());

    public void put(K key, V value) {
      Map<K, V> newMap = new HashMap<>(internalMap);
      newMap.put(key, value);
      internalMap = newMap;
    }

    public V get(K key) {
      return internalMap.get(key);
    }

    public boolean containsKey(K key) {
      return internalMap.containsKey(key);
    }

    public void remove(K key) {
      Map<K, V> newMap = new HashMap<>(internalMap);
      newMap.remove(key);
      internalMap = newMap;
    }

    public int size() {
      return internalMap.size();
    }

    public Collection<V> values() {
      return internalMap.values();
    }

    public Map<K, V> getSnapshot() {
      return Collections.unmodifiableMap(internalMap);
    }
  }
}

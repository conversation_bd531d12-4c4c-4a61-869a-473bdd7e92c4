package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocation.model.QAllocation;
import au.com.peterpal.selecting.standingorders.allocation.model.QRelease;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.QCustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.ext.category.entity.QCategory;
import au.com.peterpal.selecting.standingorders.ext.order.boundary.dto.OrderStatus;
import au.com.peterpal.selecting.standingorders.ext.order.entity.QOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.QPendingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.QStandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import au.com.peterpal.selecting.standingorders.titles.dto.*;
import au.com.peterpal.selecting.standingorders.titles.entity.*;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Repository
@RequiredArgsConstructor
public class TitleRepositoryCustomImpl implements TitleRepositoryCustom {
  public static final String DESC = "desc";
  @PersistenceContext private final EntityManager entityManager;

  private final QTitle title = QTitle.title1;
  private final QPendingOrder pendingOrder = QPendingOrder.pendingOrder;
  private final QOrder order = QOrder.order;
  private final QRelease release = QRelease.release;
  private final QMatchedProduct matchedProduct = QMatchedProduct.matchedProduct;
  private final QMatchedStandingOrder matchedStandingOrder =
      QMatchedStandingOrder.matchedStandingOrder;
  private final QCategory category = QCategory.category;
  private final QStandingOrderAggregated standingOrderAggregated =
      QStandingOrderAggregated.standingOrderAggregated;
  private final QStandingOrder standingOrder = QStandingOrder.standingOrder;

  private QCustomerStandingOrder customerStandingOrder =
      QCustomerStandingOrder.customerStandingOrder;
  private QAllocation allocation = QAllocation.allocation;

  private QTitleAllocationCategoryAgg titleAllocationCategoryAgg =
      QTitleAllocationCategoryAgg.titleAllocationCategoryAgg;

  @Override
  public TitleSearchResponse search(TitleSearchRequest searchRequest) {
    PageImpl<TitleSearchDetail> searchDetail = searchDetail(searchRequest);
    List<TitleSearchStatusCount> statusCounts = searchStatusCounts(searchRequest);
    return TitleSearchResponse.of(searchDetail, statusCounts);
  }

  private List<TitleSearchStatusCount> searchStatusCounts(TitleSearchRequest searchRequest) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);

    JPAQuery<TitleSearchStatusCount> query =
        factory
            .select(
                Projections.bean(
                    TitleSearchStatusCount.class,
                    title.titleStatus.as("titleStatus"),
                    title.countDistinct().as("count")))
            .from(title)
            .leftJoin(title.category, category)
            .leftJoin(title.matchedProducts, matchedProduct)
            .where(buildConditionForSearch(searchRequest))
            .groupBy(title.titleStatus);
    return query.fetch();
  }

  private PageImpl<TitleSearchDetail> searchDetail(TitleSearchRequest searchRequest) {
    Pageable pageable = searchRequest.getPageRequest();
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);

    JPAQuery<TitleSearchDetail> query =
        factory
            .select(
                Projections.bean(
                    TitleSearchDetail.class,
                    title.titleId,
                    title.title,
                    title.subtitle,
                    title.personName,
                    title.series,
                    title.imprint,
                    title.titleStatus,
                    category.code.as("category"),
                    title.dateAdded,
                    title.deferredDate,
                    title.processedDate))
            .distinct()
            .from(title)
            .leftJoin(title.category, category)
            .leftJoin(title.matchedProducts, matchedProduct);
    if ("allocationCategories".equalsIgnoreCase(searchRequest.getSortByKey())) {
      query = buildQueryForAllocationCategoriesSort(factory);
    }
    query = query.where(buildConditionForSearch(searchRequest));

    long total = query.fetchCount();
    query = addSortToQuery(searchRequest, query);
    List<TitleSearchDetail> titleSearchDetails = applyPagingAndSorting(query, pageable).fetch();

    Set<TitleId> titleIds =
        titleSearchDetails.stream().map(TitleSearchDetail::getTitleId).collect(Collectors.toSet());

    Map<TitleId, List<MatchedProduct>> mapOfMatchedProduct =
        fetchMatchedProducts(factory, titleIds);
    Map<TitleId, List<StandingOrderAggregated>> mapOfStandingOrderAggregated =
        fetchStandingOrderAggregatedMap(factory, titleIds);
    Map<TitleId, List<TitleStandingOrderAllocationCategory>> mapOfStandingOrderCategories =
        fetchStandingOrderCategories(titleIds);

    for (TitleSearchDetail titleSearchDetail : titleSearchDetails) {
      TitleId titleId = titleSearchDetail.getTitleId();
      Set<String> isbnSet =
          mapOfMatchedProduct.getOrDefault(titleId, Collections.emptyList()).stream()
              .map(MatchedProduct::getIsbn)
              .collect(Collectors.toSet());
      titleSearchDetail.setIsbns(isbnSet);

      Set<MatchedTermTuple> matchedTermTuples =
          mapOfStandingOrderAggregated.getOrDefault(titleId, Collections.emptyList()).stream()
              .map(StandingOrderAggregated::getMatchedTermTuples)
              .flatMap(Collection::stream)
              .collect(Collectors.toSet());
      titleSearchDetail.setMatchedTerms(matchedTermTuples);

      List<TitleStandingOrderAllocationCategory> standingOrderCategories =
          mapOfStandingOrderCategories.getOrDefault(titleId, Collections.emptyList());
      titleSearchDetail.setStandingOrderAllocationCategories(
          new HashSet<>(standingOrderCategories));

      titleSearchDetail.setAllocationCategories(
          standingOrderCategories.stream()
              .map(TitleStandingOrderAllocationCategory::getCategories)
              .flatMap(Collection::stream)
              .collect(Collectors.toSet()));
    }
    return new PageImpl<>(titleSearchDetails, pageable, total);
  }

  private JPAQuery<TitleSearchDetail> buildQueryForAllocationCategoriesSort(
      JPAQueryFactory factory) {
    JPAQuery<TitleSearchDetail> query;
    query =
        factory
            .select(
                Projections.bean(
                    TitleSearchDetail.class,
                    title.titleId,
                    title.title,
                    title.subtitle,
                    title.personName,
                    title.series,
                    title.imprint,
                    title.titleStatus,
                    category.code.as("category"),
                    title.dateAdded,
                    title.deferredDate,
                    title.processedDate,
                    titleAllocationCategoryAgg.allocationCategory))
            .distinct()
            .from(title)
            .leftJoin(title.category, category)
            .leftJoin(title.matchedProducts, matchedProduct)
            .leftJoin(titleAllocationCategoryAgg)
            .on(title.titleId.eq(titleAllocationCategoryAgg.titleId));
    return query;
  }

  private JPAQuery<TitleSearchDetail> addSortToQuery(
      TitleSearchRequest searchRequest, JPAQuery<TitleSearchDetail> query) {
    if (searchRequest.getSortByKey() != null) {
      if (searchRequest.getSortByKey().equalsIgnoreCase("titleStatus")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(title.titleStatus.desc());
        } else {
          query.orderBy(title.titleStatus.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("category")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(category.code.desc());
        } else {
          query.orderBy(category.code.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("title")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(title.title.desc());
        } else {
          query.orderBy(title.title.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("subtitle")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(title.subtitle.desc());
        } else {
          query.orderBy(title.subtitle.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("personName")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(title.personName.desc());
        } else {
          query.orderBy(title.personName.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("series")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(title.series.desc());
        } else {
          query.orderBy(title.series.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("imprint")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(title.imprint.desc());
        } else {
          query.orderBy(title.imprint.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("dateAdded")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(title.dateAdded.desc());
        } else {
          query.orderBy(title.dateAdded.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("deferredDate")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(title.deferredDate.desc());
        } else {
          query.orderBy(title.deferredDate.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("processedDate")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(title.processedDate.desc());
        } else {
          query.orderBy(title.processedDate.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("allocationCategories")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(titleAllocationCategoryAgg.allocationCategory.desc());
        } else {
          query.orderBy(titleAllocationCategoryAgg.allocationCategory.asc());
        }
      }
    }
    return query;
  }

  private Map<TitleId, List<TitleStandingOrderAllocationCategory>> fetchStandingOrderCategories(
      Set<TitleId> titleIds) {
    Map<StandingOrderId, TitleStandingOrderAllocationCategory> standingOrderMap =
        findStandingOrderCategories(titleIds);

    return standingOrderMap.values().stream()
        .collect(Collectors.groupingBy(TitleStandingOrderAllocationCategory::getTitleId));
  }

  public Map<StandingOrderId, TitleStandingOrderAllocationCategory> findStandingOrderCategories(
      Set<TitleId> titleIds) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    JPAQuery<TitleStandingOrderCategorySearchResponse> query =
        factory
            .select(
                Projections.bean(
                    TitleStandingOrderCategorySearchResponse.class,
                    title.titleId,
                    standingOrder.standingOrderId,
                    category))
            .from(title)
            .distinct()
            .innerJoin(title.standingOrderAggregatedList, standingOrderAggregated)
            .innerJoin(standingOrderAggregated.standingOrder, standingOrder)
            .innerJoin(customerStandingOrder)
            .on(
                customerStandingOrder.standingOrder.standingOrderId.eq(
                    standingOrder.standingOrderId))
            .innerJoin(customerStandingOrder.allocations, allocation)
            .innerJoin(allocation.categories, category)
            .where(
                title
                    .titleId
                    .in(titleIds)
                    .and(allocation.status.in(AllocationStatus.ACTIVE, AllocationStatus.PAUSED)));
    List<TitleStandingOrderCategorySearchResponse> responses = query.fetch();
    Map<StandingOrderId, TitleStandingOrderAllocationCategory> standingOrderMap = new HashMap<>();

    for (TitleStandingOrderCategorySearchResponse response : responses) {
      StandingOrderId standingOrderId = response.getStandingOrderId();
      TitleStandingOrderAllocationCategory titleStandingOrderAllocationCategory =
          standingOrderMap.computeIfAbsent(
              standingOrderId,
              k ->
                  TitleStandingOrderAllocationCategory.of(
                      response.getTitleId(), standingOrderId, new ArrayList<>()));
      titleStandingOrderAllocationCategory.getCategories().add(response.getCategory());
    }
    return standingOrderMap;
  }

  @Override
  public List<Title> findTitleStillOnOrderByStandingOrderIdAndType(
      StandingOrderId standingOrderId, TitleType titleType) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    JPAQuery<Title> query =
        factory
            .selectFrom(title)
            .innerJoin(pendingOrder)
            .on(pendingOrder.title.titleId.eq(title.titleId))
            .innerJoin(order)
            .on(
                pendingOrder
                    .orderNumber
                    .eq(order.orderNumber)
                    .and(order.customer.customerId.eq(pendingOrder.customer.customerId)))
            .innerJoin(pendingOrder.release, release)
            .innerJoin(release.allocation, allocation)
            .innerJoin(allocation.customerStandingOrder, customerStandingOrder)
            .where(
                title
                    .type
                    .eq(titleType)
                    .and(title.titleStatus.in(TitleStatus.PENDING, TitleStatus.PROCESSED))
                    .and(customerStandingOrder.standingOrder.standingOrderId.eq(standingOrderId))
                    .and(order.status.eq(OrderStatus.ON_ORDER)))
            .distinct();
    return query.fetch();
  }

  @Override
  public List<Title> searchTitle(TitleSearchRequest searchRequest, List<TitleId> excludedTitleIds) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);

    BooleanBuilder condition = buildConditionForSearch(searchRequest);
    if (CollectionUtils.isNotEmpty(excludedTitleIds)) {
      condition.and(title.titleId.notIn(excludedTitleIds));
    }

    JPAQuery<Title> query =
        factory
            .selectFrom(title)
            .distinct()
            .from(title)
            .leftJoin(title.category, category)
            .leftJoin(title.matchedProducts, matchedProduct)
            .where(condition);
    return query.fetch();
  }

  private Map<TitleId, List<StandingOrderAggregated>> fetchStandingOrderAggregatedMap(
      JPAQueryFactory factory, Set<TitleId> titleIds) {
    JPAQuery<StandingOrderAggregated> standingOrderAggregatedJPAQuery =
        factory
            .selectFrom(standingOrderAggregated)
            .where(standingOrderAggregated.title.titleId.in(titleIds));

    List<StandingOrderAggregated> standingOrderAggregatedList =
        standingOrderAggregatedJPAQuery.fetch();
    return standingOrderAggregatedList.stream().collect(groupingBy(s -> s.getTitle().getTitleId()));
  }

  private Map<TitleId, List<MatchedProduct>> fetchMatchedProducts(
      JPAQueryFactory factory, Set<TitleId> titleIds) {
    JPAQuery<MatchedProduct> matchedProductJPAQuery =
        factory.selectFrom(matchedProduct).where(matchedProduct.title.titleId.in(titleIds));
    List<MatchedProduct> matchedProducts = matchedProductJPAQuery.fetch();
    return matchedProducts.stream().collect(groupingBy(m -> m.getTitle().getTitleId()));
  }

  @Override
  public Page<Title> searchTitle(TitleSearchRequest searchRequest) {
    Pageable pageable = searchRequest.getPageRequest();
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);

    JPAQuery<Title> query =
        factory
            .select(title)
            .from(title)
            .leftJoin(title.category, category)
            .where(buildConditionForSearchTitle(searchRequest));
    query.offset(pageable.getOffset()).limit(pageable.getPageSize());
    return new PageImpl(query.fetch(), pageable, query.fetchCount());
  }

  @Override
  public List<Title> findTitleContainingIsbn(
      String isbn, TitleType type, MatchedProductStatus productStatus) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    BooleanExpression filter = matchedProduct.isbn.eq(isbn).and(title.type.eq(TitleType.ORIGINAL));
    if (Objects.nonNull(productStatus)) {
      filter = filter.and(matchedProduct.status.eq(productStatus));
    }
    return factory
        .selectFrom(title)
        .innerJoin(title.matchedProducts, matchedProduct)
        .where(filter)
        .fetch();
  }

  @Override
  public List<Title> findAllTitleByIsbnAndStandingOrderId(
      String isbn, StandingOrderId standingOrderId) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    return factory
        .selectFrom(title)
        .innerJoin(title.matchedProducts, matchedProduct)
        .innerJoin(matchedProduct.matchedStandingOrders, matchedStandingOrder)
        .where(
            matchedProduct
                .isbn
                .eq(isbn)
                .and(matchedProduct.status.eq(MatchedProductStatus.ACTIVE))
                .and(matchedStandingOrder.standingOrder.standingOrderId.eq(standingOrderId)))
        .fetch();
  }

  @Override
  public List<Title> findTitlesByStatusAndTypeAndAllocationId(
      List<TitleStatus> statuses,
      List<TitleType> types,
      AllocationId allocationId,
      boolean acceptedStandingOrderRequired) {

    QStandingOrderAggregated standingOrderAggregated =
        QStandingOrderAggregated.standingOrderAggregated;
    QStandingOrder standingOrder = QStandingOrder.standingOrder;
    QCustomerStandingOrder customerStandingOrder = QCustomerStandingOrder.customerStandingOrder;
    QAllocation allocation = QAllocation.allocation;

    JPAQueryFactory factory = new JPAQueryFactory(entityManager);

    BooleanExpression selectedStandingOrder =
        acceptedStandingOrderRequired
            ? standingOrderAggregated.status.eq(StandingOrderAggregatedStatus.ACCEPTED)
            : standingOrderAggregated.status.isNull();

    return factory
        .selectFrom(title)
        .innerJoin(title.standingOrderAggregatedList, standingOrderAggregated)
        .innerJoin(standingOrderAggregated.standingOrder, standingOrder)
        .innerJoin(customerStandingOrder)
        .on(customerStandingOrder.standingOrder.standingOrderId.eq(standingOrder.standingOrderId))
        .innerJoin(customerStandingOrder.allocations, allocation)
        .where(
            title
                .titleStatus
                .in(statuses)
                .and(
                    customerStandingOrder.customerStandingOrderStatus.eq(
                        StandingOrderStatus.ACTIVE))
                .and(selectedStandingOrder)
                .and(allocation.branch.isNull())
                .and(title.type.in(types))
                .and(allocation.allocationId.eq(allocationId)))
        .distinct()
        .fetch();
  }

  private Predicate buildConditionForSearchTitle(TitleSearchRequest searchRequest) {
    BooleanBuilder condition = new BooleanBuilder();

    if (CollectionUtils.isNotEmpty(searchRequest.getCategories())) {
      condition.and(new BooleanBuilder(category.code.in(searchRequest.getCategories())));
    }

    if (CollectionUtils.isNotEmpty(searchRequest.getStatuses())) {
      condition.and(new BooleanBuilder(title.titleStatus.in(searchRequest.getStatuses())));
    }

    if (StringUtils.isNotEmpty(searchRequest.getText())) {
      condition.and(
          new BooleanBuilder(title.title.likeIgnoreCase(likeExp(searchRequest.getText())))
              .or(title.series.likeIgnoreCase(likeExp(searchRequest.getText())))
              .or(title.personName.likeIgnoreCase(likeExp(searchRequest.getText())))
              .or(title.imprint.likeIgnoreCase(likeExp(searchRequest.getText()))));
    }

    if (CollectionUtils.isNotEmpty(searchRequest.getExcludedStatuses())) {
      condition.and(
          new BooleanBuilder(title.titleStatus.notIn(searchRequest.getExcludedStatuses())));
    }
    return condition;
  }

  private BooleanBuilder buildConditionForSearch(TitleSearchRequest searchRequest) {
    BooleanBuilder condition = new BooleanBuilder();

    if (CollectionUtils.isNotEmpty(searchRequest.getCategories())) {
      condition.and(new BooleanBuilder(category.code.in(searchRequest.getCategories())));
    }

    if (CollectionUtils.isNotEmpty(searchRequest.getStatuses())) {
      condition.and(new BooleanBuilder(title.titleStatus.in(searchRequest.getStatuses())));
    }

    if (StringUtils.isNotEmpty(searchRequest.getText())) {
      condition.and(
          new BooleanBuilder(title.title.likeIgnoreCase(likeExp(searchRequest.getText())))
              .or(title.series.likeIgnoreCase(likeExp(searchRequest.getText())))
              .or(title.personName.likeIgnoreCase(likeExp(searchRequest.getText())))
              .or(title.imprint.likeIgnoreCase(likeExp(searchRequest.getText())))
              .or(matchedProduct.isbn.likeIgnoreCase(likeExp(searchRequest.getText()))));
    }

    if (CollectionUtils.isNotEmpty(searchRequest.getExcludedStatuses())) {
      condition.and(
          new BooleanBuilder(title.titleStatus.notIn(searchRequest.getExcludedStatuses())));
    }

    return buildDateFilterCondition(searchRequest, condition);
  }

  private BooleanBuilder buildDateFilterCondition(
      TitleSearchRequest searchRequest, BooleanBuilder condition) {
    if (searchRequest.getDateType() == null) {
      return condition;
    }
    switch (searchRequest.getDateType()) {
      case ADDED:
        if (searchRequest.getStartDate() != null) {
          condition.and(
              new BooleanBuilder(
                  title.dateAdded.after(searchRequest.getStartDate().atStartOfDay())));
        }
        if (searchRequest.getEndDate() != null) {
          condition.and(
              new BooleanBuilder(
                  title.dateAdded.before(searchRequest.getEndDate().atTime(23, 59, 59))));
        }
        break;

      case DEFERRED:
        if (searchRequest.getStartDate() != null) {
          condition.and(new BooleanBuilder(title.deferredDate.after(searchRequest.getStartDate())));
        }
        if (searchRequest.getEndDate() != null) {
          condition.and(new BooleanBuilder(title.deferredDate.before(searchRequest.getEndDate())));
        }
        break;

      case PROCESSED:
        if (searchRequest.getStartDate() != null) {
          condition.and(
              new BooleanBuilder(title.processedDate.after(searchRequest.getStartDate())));
        }
        if (searchRequest.getEndDate() != null) {
          condition.and(new BooleanBuilder(title.processedDate.before(searchRequest.getEndDate())));
        }
        break;
    }
    return condition;
  }

  private JPAQuery<TitleSearchDetail> applyPagingAndSorting(
      JPAQuery<TitleSearchDetail> query, Pageable pageable) {
    if (Objects.nonNull(pageable)) {
      query.offset(pageable.getOffset()).limit(pageable.getPageSize());

      pageable
          .getSort()
          .forEach(
              orderBy ->
                  query.orderBy(
                      new OrderSpecifier(
                          orderBy.isAscending() ? Order.ASC : Order.DESC,
                          new PathBuilder<>(Title.class, "title1").get(orderBy.getProperty()))));
    }

    return query;
  }

  private StringExpression likeExp(String text) {
    return Expressions.asString("%").concat(text).concat("%");
  }
}

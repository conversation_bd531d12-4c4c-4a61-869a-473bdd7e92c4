package au.com.peterpal.selecting.standingorders.allocation.commands;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.With;
import org.springframework.data.domain.Pageable;

import java.util.List;

@With
@Data
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
public class SearchAllocationsByStandingOrder {

  @NotNull
  @NonNull
  private StandingOrderId standingOrderId;
  private String customerCode;
  private AllocationStatus allocationStatus;

  private List<String> categories;

  @NonNull
  @NotNull
  private Pageable pageRequest;
}

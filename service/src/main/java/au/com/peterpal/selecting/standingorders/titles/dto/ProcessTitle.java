package au.com.peterpal.selecting.standingorders.titles.dto;

import au.com.peterpal.selecting.standingorders.titles.entity.ProductAggregatedId;
import au.com.peterpal.selecting.standingorders.titles.entity.StandingOrderAggregatedId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
public class ProcessTitle {
  @NonNull @NotNull private TitleId titleId;

  @NonNull @NotEmpty private List<Product> products;

  @NonNull @NotEmpty private List<StandingOrderAggregatedId> standingOrderAggregatedIds;

  @Data
  public static class Product {
    private ProductAggregatedId productAggregatedId;
    private BigDecimal price;
    private String currencyCode;
  }
}

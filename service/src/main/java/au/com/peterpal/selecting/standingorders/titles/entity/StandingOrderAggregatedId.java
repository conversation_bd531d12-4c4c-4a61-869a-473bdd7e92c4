package au.com.peterpal.selecting.standingorders.titles.entity;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class StandingOrderAggregatedId extends UuidEntityId {

  public static StandingOrderAggregatedId of(@NotEmpty UUID id) {
    return new StandingOrderAggregatedId(id);
  }

  public static StandingOrderAggregatedId of(@NotEmpty String id) {
    return new StandingOrderAggregatedId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public StandingOrderAggregatedId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public StandingOrderAggregatedId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public StandingOrderAggregatedId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

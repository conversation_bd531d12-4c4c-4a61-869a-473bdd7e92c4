package au.com.peterpal.selecting.standingorders.standingorder.model;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.UUID;
import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;

@Embeddable
public class TermId extends UuidEntityId {

  public static TermId of(@NotEmpty UUID id) {
    return new TermId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public TermId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public TermId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public TermId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }

}

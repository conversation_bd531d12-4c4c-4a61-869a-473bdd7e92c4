package au.com.peterpal.selecting.standingorders.pendingorder.dto;

import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import io.swagger.annotations.ApiModel;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@With
@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
@ApiModel
public class UpdatePendingOrderRequest {

    @NotNull
    @NonNull
    private PendingOrderId pendingOrderId;

    private String customerReference;

    private String category;

    private String collectionCode;

    private String deliveryInstructions;

    private String notes;

    private FundId fundId;

    private Integer quantity;

    PendingOrderStatus status;
}

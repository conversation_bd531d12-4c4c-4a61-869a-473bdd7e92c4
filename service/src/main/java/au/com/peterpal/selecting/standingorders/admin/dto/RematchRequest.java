package au.com.peterpal.selecting.standingorders.admin.dto;

import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@With
@Value
@Builder
public class RematchRequest {
  @NotNull
  CustomerId customerId;
  List<StandingOrderId> standingOrderIds;
  LocalDate productPublicationDateFrom;
  LocalDate productPublicationDateTo;
}

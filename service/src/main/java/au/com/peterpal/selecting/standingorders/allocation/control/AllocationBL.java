package au.com.peterpal.selecting.standingorders.allocation.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocation.commands.SearchAllocationsByStandingOrder;
import au.com.peterpal.selecting.standingorders.allocation.dto.AllocationInfo;
import au.com.peterpal.selecting.standingorders.allocation.events.AllocationRemoved;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.customerstandingorder.events.AllocationUpdated;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerRepository;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderRepository;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.titles.control.TitleBL;
import au.com.peterpal.selecting.standingorders.utils.Affirm;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

@Log4j2
@RequiredArgsConstructor
@Component
public class AllocationBL {

  private final AllocationService allocationService;
  private final StandingOrderRepository standingOrderRepository;
  private final CustomerRepository customerRepository;
  private final TitleBL titleBL;
  private final EventPublisher eventPublisher;

  @EventListener
  public void handle(final AllocationUpdated allocationUpdated) {
    if (allocationUpdated.isActiveToPaused()) {
      // delete any pending orders on any PENDING titles that are for that allocation
      log.debug("allocation {} updated status Active to Paused", allocationUpdated.getId());
      titleBL.deletePendingOrdersDueToAllocationPaused(AllocationId.of(allocationUpdated.getId()));

    } else if (allocationUpdated.isPausedToActive()) {
      log.debug("allocation {} updated status Paused Active", allocationUpdated.getId());

      // transition all related titles to PENDING with pending orders for that allocation
      titleBL.transitionPausedTitlesDueAllocationMadeActive(
          AllocationId.of(allocationUpdated.getId()));

    } else if (allocationUpdated.isPausedToInactive()) {
      log.debug("allocation {} updated status Paused Inactive", allocationUpdated.getId());
      // transition all related titles to REJECTED with pending orders for that allocation
      titleBL.rejectPausedTitlesDueAllocationMadeInactive(
          AllocationId.of(allocationUpdated.getId()), allocationUpdated.getUsername());

    } else {
      log.trace("no allocation {}  status changes related to pause", allocationUpdated.getId());
    }
  }

  public Page<AllocationInfo> handle(SearchAllocationsByStandingOrder searchRequest) {

    log.debug(() -> "Searching allocations by standing order");
    validateSearchAllocationRequest(searchRequest);

    Page<Allocation> pageResult = allocationService.search(searchRequest);
    return pageResult.map(
        a -> {
          List<Allocation> bd =
              a.getCustomerStandingOrder().getAllocations(a.getCategories(), true);
          AllocationInfo ai = AllocationInfo.from(a);
          if (!bd.isEmpty()) {
            ai.setBranchDistributed(true);
          }
          return ai;
        });
  }

  public AllocationId handle(AllocationId allocationId, String username) {
    Affirm.of(allocationId).notNull("AllocationId must not be null");

    Allocation allocation = allocationService.findById(allocationId);
    allocation.setStatus(AllocationStatus.INACTIVE);
    allocation.setDateUpdated(LocalDateTime.now());

    allocationService.save(allocation);
    allocationService.updateAllocationQtys(allocation.getBaParent().getAllocationId());

    eventPublisher.publish(AllocationRemoved.from(allocation, username));
    return allocation.getAllocationId();
  }

  private void validateSearchAllocationRequest(SearchAllocationsByStandingOrder request) {
    Affirm.of(request).notNull("Search allocation request must not be null");
    checkStandingOrderIfExist(request.getStandingOrderId());
    checkCustomerIfExistByCode(request.getCustomerCode());
  }

  private void checkCustomerIfExistByCode(String customerCode) {
    if (Objects.nonNull(customerCode) && !customerRepository.findByCode(customerCode).isPresent()) {
      throw new ResourceNotFoundException(Customer.class, customerCode);
    }
  }

  private void checkStandingOrderIfExist(StandingOrderId standingOrderId) {
    Affirm.of(standingOrderId).notNull("StandingOrderId must not be null");
    if (!standingOrderRepository.findById(standingOrderId).isPresent()) {
      throw new ResourceNotFoundException(StandingOrder.class, String.valueOf(standingOrderId));
    }
  }
}

package au.com.peterpal.selecting.standingorders.pendingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.common.utils.StringAffirm;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.SupplierId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.BranchDistribution;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Set;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;
import java.util.Optional;

@Getter
@SuperBuilder
public class PendingOrderCreated extends DomainEvent {

  @NotNull
  private PendingOrderId pendingOrderId;

  @NotNull
  private ReleaseId releaseId;

  @NotNull
  private CustomerId customerId;

  private SupplierId supplierId;

  private String customerReference;

  private String orderedProductReference;

  private String category;

  private String deliveryInstructions;

  private String notes;

  @NotNull
  private FundId fundId;

  @NotNull
  private PendingOrderStatus status;

  private Integer quantity;

  @NotNull
  private ReleaseFormat format;

  private String collectionCode;

  private TitleId titleId;

  private BigDecimal price;

  private LocalDate publicationDate;

  private Set<BranchDistribution> branchDistributions;

  public static PendingOrderCreated from(PendingOrder pendingOrder, String username) {
    Optional<PendingOrderCreated> opPoc =
        Optional.ofNullable(pendingOrder)
            .map(
                po ->
                    PendingOrderCreated.builder()
                        .id(pendingOrder.getPendingOrderId().getId())
                        .status(pendingOrder.getOrderStatus())
                        .username(
                            StringAffirm.of(username).hasText()
                                ? username
                                : "standing-order-service")
                        .pendingOrderId(pendingOrder.getPendingOrderId())
                        .releaseId(pendingOrder.getRelease().getReleaseId())
                        .customerId(pendingOrder.getCustomer().getCustomerId())
                        .titleId(pendingOrder.getTitle().getTitleId())
                        .supplierId(
                            pendingOrder.getSupplier() == null
                                ? null
                                : pendingOrder.getSupplier().getSupplierId())
                        .customerReference(pendingOrder.getCustomerReference())
                        .orderedProductReference(pendingOrder.getOrderedProductReference())
                        .fundId(
                            pendingOrder.getFund() == null
                                ? null
                                : pendingOrder.getFund().getFundId())
                        .quantity(pendingOrder.getQuantity())
                        .format(pendingOrder.getFormat())
                        .collectionCode(pendingOrder.getCollectionCode())
                        .category(
                            Optional.ofNullable(pendingOrder.getCategory())
                                .map(Category::getCode)
                                .orElse(null))
                        .deliveryInstructions(pendingOrder.getDeliveryInstructions())
                        .notes(pendingOrder.getNotes())
                        .status(pendingOrder.getOrderStatus())
                        .branchDistributions(pendingOrder.getBranchDistributions())
                        .price(pendingOrder.getPrice())
                        .publicationDate(pendingOrder.getPublicationDate())
                        .build());
    return opPoc.orElseThrow(() -> new IllegalArgumentException("Can not create PendingOrderCreated event from null"));
  }
}

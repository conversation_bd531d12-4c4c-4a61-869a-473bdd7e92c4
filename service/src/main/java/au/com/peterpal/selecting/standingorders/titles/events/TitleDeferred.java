package au.com.peterpal.selecting.standingorders.titles.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.UUID;

@Setter
@Getter
@SuperBuilder
public class TitleDeferred extends DomainEvent {

  private LocalDateTime deferredDate;
  private Boolean manualDeferred;

  public static TitleDeferred from(UUID titleId, String username, LocalDateTime deferredDate) {

    return TitleDeferred.builder()
        .id(titleId)
        .deferredDate(deferredDate)
        .username(StringUtils.defaultString(username, "system"))
        .build();
  }
}

package au.com.peterpal.selecting.standingorders.allocationpreference.control;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceStatus;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AllocationPreferenceRepository
    extends JpaRepository<AllocationPreference, AllocationPreferenceId>,
        AllocationPreferenceRepositoryCustom {

  Optional<AllocationPreference> findByCustomer_CodeAndCategories_Code(
      String customerCode, String category);

  List<AllocationPreference> findAllByCustomer_CustomerId(CustomerId customerId);

  List<AllocationPreference> findByCustomer_CodeAndStatusAndCategories_CodeIn(
      String customerCode, AllocationPreferenceStatus status, List<String> code);
}

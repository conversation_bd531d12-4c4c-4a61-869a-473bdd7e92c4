package au.com.peterpal.selecting.standingorders.admin.control;

import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AllocationPrefInfo;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AllocationPrefInfo.ReleasePrefInfo;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.*;
import au.com.peterpal.selecting.standingorders.profile.control.CustomerProfileBL;

import au.com.peterpal.selecting.standingorders.utils.Affirm;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import lombok.extern.log4j.Log4j2;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.web.multipart.MultipartFile;

/**
 * Excel import agent for customer profiles.
 * <pre>
 *   SQL to select data to be imported:
 *
 *      select * from StandingOrderPref
 *      where categoryCode is not null and categoryCode <> ''
 *      order by customerCode
 *
 * </pre>
 */
@Log4j2
public class AllocPrefImportAgent extends ExcelImportAgent {

  private static int CUSTOMER_CODE_CELL_ID = 1;
  private static int CATEGORY_CELL_ID = 2;
  private static int FUND_CELL_ID = 3;
  private static int CUSTOMER_REF_CELL_ID = 4;
  private static int DELIVERY_INSTRUCTIONS_CELL_ID = 5;
  private static int NOTES_CELL_ID = 6;
  private static int INITIAL_ACTION_CELL_ID = 7;
  private static int INITIAL_ASSIGN_RULE_CELL_ID = 8;
  private static int INITIAL_SFPB_RULE_CELL_ID = 9;
  private static int INITIAL_QTY_TOTAL_CELL_ID = 10;
  private static int INITIAL_QTY_HB_CELL_ID = 11;
  private static int INITIAL_QTY_PB_CELL_ID = 12;
  private static int INITIAL_FUND_CODE_CELL_ID = 13;
  private static int INITIAL_HB_FUND_CODE_CELL_ID = 14;
  private static int INITIAL_PB_FUND_CODE_CELL_ID = 15;
  private static int FIRST_ACTION_CELL_ID = 16;
  private static int FIRST_QTY_TOTAL_CELL_ID = 17;
  private static int FIRST_FUND_CODE_CELL_ID = 18;
  private static int REISSUE_ACTION_CELL_ID = 19;
  private static int REISSUE_QTY_TOTAL_CELL_ID = 20;
  private static int REISSUE_FUND_CODE_CELL_ID = 21;

  private CustomerProfileBL service;
  private List<AllocationPrefInfo> allocInfoList = new ArrayList<>();

  public static final AllocPrefImportAgent of(MultipartFile file, CustomerProfileBL service) {
    return new AllocPrefImportAgent(file, service);
  }

  private AllocPrefImportAgent(MultipartFile file, CustomerProfileBL service) {
    super(file);
    Affirm.of(service).notNull("Standing order service must not be null");
    this.service = service;
  }

  @Override
  protected void doImport() {
      Sheet sheet = getFirstSheet();
      AtomicInteger importCount = new AtomicInteger();
      AtomicInteger errorCount = new AtomicInteger();
      for (Row row : sheet) {
        if (row.getRowNum() == 0) {
          continue;
        }
        AllocationPrefInfo allocInfo = null;
        try {
          allocInfo = loadAllocationInfo(row);
          allocInfo.setRelPrefInfo(initialReleasePrefs(row));
          allocInfoList.add(allocInfo);
        }  catch(Exception ex) {
          String rowStr = toString(row);
          log.warn(String.format("Ignoring line %s", rowStr), ex);
          report.getErrors().add(String.format("Ignoring line - %s : %s", rowStr, ex.getMessage()));
          errorCount.getAndIncrement();
        }
      }

      report.setTotal(allocInfoList.size());
      allocInfoList.forEach(a -> {
        try {
          service.handle(a, test, "lucy-so");
          importCount.getAndIncrement();
        } catch(Exception ex) {
          log.warn(String.format("Exception creating allocation preference from %s", a), ex);
          errorCount.getAndIncrement();
          report.getErrors().add(String.format("%s - %s", a.getCustomerCode(), ex.getMessage()));
        }
      });
      report.setImported(importCount.get());
      report.setFailed(errorCount.get());
  }

  private AllocationPrefInfo loadAllocationInfo(Row row) {
    return Optional.ofNullable(row)
        .map(r -> AllocationPrefInfo.builder()
            .id(UUID.randomUUID())
            .customerCode(getString(row.getCell(CUSTOMER_CODE_CELL_ID), "Customer code must not be blank"))
            .categories(List.of(getString(r.getCell(CATEGORY_CELL_ID)), "Category code must not be blank"))
            .fundCode(getString(r.getCell(FUND_CELL_ID)))
            .customerReference(getString(r.getCell(CUSTOMER_REF_CELL_ID)))
            .deliveryInstructions(getString(r.getCell(DELIVERY_INSTRUCTIONS_CELL_ID)))
            .notes(getString(r.getCell(NOTES_CELL_ID)))
            .build()
        )
        .orElseThrow(() -> new IllegalArgumentException("Can not create allocation preference from null row"));
  }

  private ReleasePrefInfo initialReleasePrefs(Row row) {
    return Optional.ofNullable(row)
        .map(r -> ReleasePrefInfo.builder()
            .id(UUID.randomUUID())
            .releaseType(ReleaseType.INITIAL)
            .actionType(ActionType.valueAt(getInt(r.getCell(INITIAL_ACTION_CELL_ID), "Initial release action type preference must not be null")))
            .fundCode(getString(r.getCell(INITIAL_FUND_CODE_CELL_ID)))
            .hardbackFundCode(getString(r.getCell(INITIAL_HB_FUND_CODE_CELL_ID)))
            .paperbackFundCode(getString(r.getCell(INITIAL_PB_FUND_CODE_CELL_ID)))
            .qtyTotal(getIntValue(r.getCell(INITIAL_QTY_TOTAL_CELL_ID)))
            .qtyHardback(getIntValue(r.getCell(INITIAL_QTY_HB_CELL_ID)))
            .qtyPaperback(getIntValue(r.getCell(INITIAL_QTY_PB_CELL_ID)))
            .assignmentRule(AssignmentRule.valueAt(getInt(r.getCell(INITIAL_ASSIGN_RULE_CELL_ID), "Assignment rule required") - 1))
            .smallFormatPaperbackRule(SmallFormatPaperbackRule.values()[getInt(r.getCell(INITIAL_SFPB_RULE_CELL_ID), "Small format paperback rule required")])
            .build()
        )
        .orElseThrow(() -> new IllegalArgumentException("Can not create initial release preference from null row"));
  }
}

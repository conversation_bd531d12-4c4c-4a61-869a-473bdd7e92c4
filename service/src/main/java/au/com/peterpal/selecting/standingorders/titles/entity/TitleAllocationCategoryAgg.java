package au.com.peterpal.selecting.standingorders.titles.entity;

import lombok.Data;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;
import org.hibernate.annotations.Synchronize;

import javax.persistence.Entity;
import javax.persistence.Id;

@Entity
@Data
@Subselect(
    "select t.id as id, min(c.code) as allocation_category from title t \n"
        + "inner join standing_order_aggregated soa on t.id =soa.title_id \n"
        + "inner join customer_standing_order cso on soa.standing_order_id = cso.standing_order_id \n"
        + "inner join allocation a on cso.id = a.customer_standing_order_id \n"
        + "inner join allocation_categories ac on a.id = ac.allocation_id \n"
        + "inner join category c on c.id = ac.categories_id \n"
        + "where a.status in ('ACTIVE', 'PAUSED')\n"
        + "group by t.id ")
@Synchronize({
  "title",
  "standing_order_aggregated",
  "customer_standing_order",
  "allocation",
  "allocation_categories",
  "category"
})
@Immutable
public class TitleAllocationCategoryAgg {
  @Id private TitleId titleId;
  private String allocationCategory;
}

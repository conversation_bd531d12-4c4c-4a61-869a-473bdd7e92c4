//package au.com.peterpal.selecting.standingorders.standingorder.commands;
//
//import au.com.peterpal.selecting.standingorders.standingorder.model.TitleId;
//import lombok.*;
//
//import javax.validation.constraints.NotNull;
//
//@Value
//@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
//@AllArgsConstructor(staticName = "of")
//@Builder
//public class ReleaseFirstSmallFormatPaperbackCmd {
//
//  @NotNull @NonNull private TitleId titleId;
//}

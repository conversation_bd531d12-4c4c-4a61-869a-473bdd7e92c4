package au.com.peterpal.selecting.standingorders.ext.itemtype.boundary.dto;

import au.com.peterpal.selecting.standingorders.ext.itemtype.model.ItemTypeId;
import au.com.peterpal.selecting.standingorders.ext.itemtype.model.ItemTypeStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import javax.validation.constraints.NotNull;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ItemTypeMessage {

  @EqualsAndHashCode.Include
  @ToString.Include
  @NonNull
  @NotNull
  private ItemTypeId itemTypeId;

  @NonNull
  @NotNull
  private String code;

  @NonNull
  @NotNull
  private String name;

  @NonNull
  @NotNull
  private ItemTypeStatus status;
}

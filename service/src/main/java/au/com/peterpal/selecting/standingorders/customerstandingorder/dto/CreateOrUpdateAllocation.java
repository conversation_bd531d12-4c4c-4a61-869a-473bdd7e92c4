package au.com.peterpal.selecting.standingorders.customerstandingorder.dto;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@With
@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class CreateOrUpdateAllocation {

  @NotNull @NonNull private UUID id;

  @NotNull @NonNull private AllocationPreference allocationPreference;

  private String customerReference;

  private String deliveryInstructions;

  private String notes;

  private List<Category> categories;

  private Fund fund;

  @Builder.Default private List<CreateRelease> releases = new ArrayList<>();

  public CreateOrUpdateAllocation addRelease(CreateRelease release) {
    return Optional.ofNullable(release)
        .map(
            r -> {
              releases.add(r);
              return CreateOrUpdateAllocation.this;
            })
        .orElseThrow(() -> new ResourceNotFoundException("CreateRelease should not be null"));
  }
}

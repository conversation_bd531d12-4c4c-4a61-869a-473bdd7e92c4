package au.com.peterpal.selecting.standingorders.titles.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.UUID;

@Setter
@Getter
@SuperBuilder
public class TitleUnDeferred extends DomainEvent {

  public static TitleUnDeferred from(UUID titleId, String username, LocalDateTime deferredDate) {
    return TitleUnDeferred.builder()
        .id(titleId)
        .username(StringUtils.defaultString(username, "system"))
        .build();
  }
}

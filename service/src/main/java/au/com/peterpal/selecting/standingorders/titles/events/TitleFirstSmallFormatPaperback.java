package au.com.peterpal.selecting.standingorders.titles.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@SuperBuilder
public class TitleFirstSmallFormatPaperback extends DomainEvent {

  private LocalDateTime deferredDate;

  private ReleaseType releaseType;

  public static TitleFirstSmallFormatPaperback from(UUID titleId, String username) {

    return TitleFirstSmallFormatPaperback.builder()
        .id(titleId)
        .deferredDate(LocalDateTime.now())
        .releaseType(ReleaseType.FIRST_SMALL_FORMAT_PAPERBACK)
        .username(username)
        .build();
  }
}

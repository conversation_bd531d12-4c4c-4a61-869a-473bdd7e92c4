package au.com.peterpal.selecting.standingorders.standingorder.dto;

import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderCreated;
import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderUpdated;
import au.com.peterpal.selecting.standingorders.standingorder.model.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

@NoArgsConstructor
@Data
@AllArgsConstructor(staticName = "of")
@Builder
public class StandingOrderUpdatedMessage {

  protected UUID id;
  private String description;

  private String notes;

  private String status;

  private String standingOrderNumber;

  private List<TermMessage> terms;

  @Builder
  @AllArgsConstructor(staticName = "of")
  @NoArgsConstructor
  @Data
  @With
  public static class TermMessage {

    private TermId termId;

    private TermType type;

    private OperationType operation;

    private String value;

    private CombinedTermId combinedTermId;
  }

  public static TermMessage getInstanceForTesting(Term term) {
    return
        TermMessage.builder()
            .termId(term.getTermId())
            .type(term.getType())
            .operation(term.getOperation())
            .combinedTermId(term.getCombinedTerm().getCombinedTermId())
            .value(term.getValue())
            .build();
  }

  public static Term to(TermMessage termMessage) {
    CombinedTerm combinedTerm =
        CombinedTerm.builder().combinedTermId(termMessage.getCombinedTermId()).build();
    return Term.builder()
            .termId(termMessage.getTermId())
            .type(termMessage.getType())
            .operation(termMessage.getOperation())
            .value(termMessage.getValue())
            .combinedTerm(combinedTerm)
            .build();
  }

  public static StandingOrderUpdatedMessage getInstanceForTesting(StandingOrderCreated so) {
    List<TermMessage> termMessages = so.getTerms().stream().map(e -> getInstanceForTesting(e))
        .collect(Collectors.toList());
    return StandingOrderUpdatedMessage.builder()
        .id(so.getId())
        .standingOrderNumber(so.getStandingOrderNumber())
        .description(so.getDescription())
        .status("ACTIVE")
        .notes(so.getNotes())
        .terms(termMessages)
        .build();
  }

  public static StandingOrderUpdatedMessage getInstanceForTesting(StandingOrderUpdated so) {

    List<TermMessage> termMessages =
        so.getTerms().stream().map(StandingOrderUpdatedMessage::getInstanceForTesting).collect(Collectors.toList());
    return StandingOrderUpdatedMessage.builder()
        .id(so.getId())
        .standingOrderNumber(so.getStandingOrderNumber())
        .description(so.getDescription())
        .status(so.getStatus())
        .notes(so.getNotes())
        .terms(termMessages)
        .build();
  }

  public static StandingOrderUpdatedMessage getInstanceForTesting() {

    CombinedTermId combinedTermId = CombinedTermId.of(UUID.randomUUID());
    Term t = Term.from("Takacs, Claire", TermType.PERSON_NAME, OperationType.STARTS_WITH,
        combinedTermId);
    TermMessage termMessage = StandingOrderUpdatedMessage.getInstanceForTesting(t);
    return StandingOrderUpdatedMessage.builder()
        .id(UUID.randomUUID())
        .standingOrderNumber("so.getStandingOrderNumber()")
        .description("so.getDescription()")
        .status("ACTIVE")
        .notes("so.getNotes()")
        .terms(Arrays.asList(termMessage))
        .build();
  }

  @JsonIgnore
  public List<Term> toTerms() {
    return terms.stream().map(e -> StandingOrderUpdatedMessage.to(e)).collect(Collectors.toList());
  }

}

package au.com.peterpal.selecting.standingorders.standingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.catalog.model.ProductFormCode;
import au.com.peterpal.selecting.standingorders.catalog.model.ProductFormDetail;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import javax.validation.constraints.NotNull;

import au.com.peterpal.selecting.standingorders.standingorder.model.TermId;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Getter
@ToString
@SuperBuilder
public class MatchFound extends DomainEvent {
  private UUID soId;
  private TermId matchedTermId;
  private String number;
  private Integer productId;
  List<String> subjectCodeList;
  private String subjectCode1;
  private String subjectCode2;
  private String subjectCode3;
  private String subjectCode4;
  private String subjectCode5;
  private String note;
  private Integer height;
  private Integer width;
  private ProductFormCode formCode;
  private List<ProductFormDetail> formDetails;
  private String title;
  private String subtitle;
  private String series;
  private String matchedPersonName;
  private String imprint;
  private LocalDate publicationDate;
  private Integer editionNumber;
  private String editionStatement;
  private String audienceCodes;
  private String audienceDescription;

  @NotNull
  private String productReference;
}

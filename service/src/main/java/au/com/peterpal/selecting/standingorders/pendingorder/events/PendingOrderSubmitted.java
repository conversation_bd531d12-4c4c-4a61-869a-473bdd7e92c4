package au.com.peterpal.selecting.standingorders.pendingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class PendingOrderSubmitted extends DomainEvent {

  public static PendingOrderSubmitted from(PendingOrderId pendingOrderId, String username) {

    return PendingOrderSubmitted.builder().id(pendingOrderId.getId()).username(username).build();
  }
}

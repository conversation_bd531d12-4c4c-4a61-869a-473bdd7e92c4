package au.com.peterpal.selecting.standingorders.catalog.model;

public enum DeweyEdition {

	DC21 ("DC21", "", ""),
	DC22 ("DC22", "", "");

	private final String code;
	private final String description;
	private final String notes;

	DeweyEdition(String code, String description, String notes) {
		this.code = code;
		this.description = description;
		this.notes = notes;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public String getNotes() {
		return notes;
	}

	public static DeweyEdition mapCode(String code) {
		for (DeweyEdition value : DeweyEdition.values()) {
			if (value.code.equals(code)) {
				return value;
			}
		}
		throw new IllegalArgumentException(code);
	}

	@Override
	public String toString() {
		return this.description;
	}
}

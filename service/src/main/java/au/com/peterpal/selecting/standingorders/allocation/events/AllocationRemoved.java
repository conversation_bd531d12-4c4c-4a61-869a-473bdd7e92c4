package au.com.peterpal.selecting.standingorders.allocation.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import java.util.List;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.Optional;

@Getter
@SuperBuilder
public class AllocationRemoved extends DomainEvent {

  private List<String> categories;
  private String customerReference;
  private String deliveryInstructions;
  private String notes;
  private FundId fundId;
  private CustomerStandingOrderId customerStandingOrderId;
  private AllocationPreferenceId allocationPreferenceId;

  public static AllocationRemoved from(Allocation allocation, String username) {
    Affirm.of(allocation).notNull("Allocation must not be null");

    return AllocationRemoved.builder()
        .username(username)
        .id(allocation.getAllocationId().getId())
        .categories(allocation.getCategoryCodes())
        .customerReference(allocation.getCustomerReference())
        .deliveryInstructions(allocation.getDeliveryInstructions())
        .notes(allocation.getNotes())
        .fundId(Optional.ofNullable(allocation.getFund()).map(Fund::getFundId).orElse(null))
        .customerStandingOrderId(
            Optional.ofNullable(allocation.getCustomerStandingOrder())
                .map(CustomerStandingOrder::getCustomerStandingOrderId)
                .orElse(null))
        .fundId(Optional.ofNullable(allocation.getFund()).map(Fund::getFundId).orElse(null))
        .build();
  }
}

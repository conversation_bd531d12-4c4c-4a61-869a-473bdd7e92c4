package au.com.peterpal.selecting.standingorders.gateways.clientweb.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.NonNull;
import lombok.ToString;
import lombok.Value;

import javax.validation.constraints.NotNull;

@Builder
@Value
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductSummaryInfo {
  private String isbn;
  @NotNull @NonNull private TitleInfo title;
  private String authors;
  private String publisher;
  private Integer numberOfPages;

  @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
  public ProductSummaryInfo(String isbn, TitleInfo title, String authors, String publisher, Integer numberOfPages) {
    this.isbn = isbn;
    this.title = title;
    this.authors = authors;
    this.publisher = publisher;
    this.numberOfPages = numberOfPages;
  }
}

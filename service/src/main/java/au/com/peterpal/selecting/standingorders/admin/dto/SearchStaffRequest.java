package au.com.peterpal.selecting.standingorders.admin.dto;

import au.com.peterpal.selecting.standingorders.ext.budget.entity.FundType;
import java.time.LocalDate;
import java.util.List;

import lombok.Builder;
import lombok.ToString;
import lombok.Value;

@Value
@Builder
@ToString
public class SearchStaffRequest {
  String customerCode;
  List<String> fundCodes;
  FundType fundType;
  LocalDate orderOrInvoicedDate;
}

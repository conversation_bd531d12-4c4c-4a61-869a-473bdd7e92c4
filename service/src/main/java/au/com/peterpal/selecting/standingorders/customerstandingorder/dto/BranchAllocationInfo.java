package au.com.peterpal.selecting.standingorders.customerstandingorder.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import java.util.List;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.With;

@With
@Data
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class BranchAllocationInfo {
  private UUID allocationId;
  private UUID branchId;
  private UUID customerStandingOrderId;
  private UUID standingOrderId;
  private UUID customerId;
  private UUID baParentId;
  private AllocationStatus status;
  private List<String> categories;
  private UUID fundId;
  private String notes;
  private List<ReleaseRequest> releases;
}

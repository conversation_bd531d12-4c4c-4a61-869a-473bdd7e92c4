package au.com.peterpal.selecting.standingorders.allocationpreference.boundary;

import au.com.peterpal.selecting.standingorders.allocationpreference.commands.AddReleasePreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.commands.RemoveReleasePreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.AllocationPreferenceBL;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.ReleasePreferenceBL;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.*;
import au.com.peterpal.selecting.standingorders.allocationpreference.events.ReleasePreferenceUpdated;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceStatus;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreferenceId;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Log4j2
@RestController
@SecurityRequirement(name = "BearerAuth")
@RequestMapping("/api/allocation-preferences")
public class AllocationPreferenceController {

  private static final String RECEIVED_REQUEST_MSG = "Received request from user %s: %s";

  private final AllocationPreferenceBL allocationPreferenceBL;
  private final ReleasePreferenceBL releasePreferenceBL;

  public AllocationPreferenceController(
      AllocationPreferenceBL allocationPreferenceBL, ReleasePreferenceBL releasePreferenceBL) {
    this.allocationPreferenceBL = allocationPreferenceBL;
    this.releasePreferenceBL = releasePreferenceBL;
  }

  @PostMapping
  public AllocationPreferenceId addAllocationPreference(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestBody @Valid AddAllocationPreference request) {

    log.debug(String.format(RECEIVED_REQUEST_MSG, username, request));
    return allocationPreferenceBL.handle(request, username);
  }

  @DeleteMapping("/{allocationPreferenceId}")
  public AllocationPreferenceId removeAllocationPreference(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable AllocationPreferenceId allocationPreferenceId) {

    log.debug(String.format(RECEIVED_REQUEST_MSG, username, allocationPreferenceId));

    RemoveAllocationPreference removeAllocation =
        RemoveAllocationPreference.of(allocationPreferenceId);

    return allocationPreferenceBL.handle(removeAllocation, username);
  }

  @PutMapping("/{allocationPreferenceId}")
  public AllocationPreferenceId updateAllocationPreference(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable AllocationPreferenceId allocationPreferenceId,
      @RequestBody @Valid UpdateAllocationPreference request) {

    log.debug(String.format(RECEIVED_REQUEST_MSG, username, request));
    return allocationPreferenceBL.handle(request, allocationPreferenceId, username);
  }

  @PostMapping("/{allocationPreferenceId}/release-preference")
  @ResponseStatus(HttpStatus.CREATED)
  public ReleasePreferenceId addReleasePreference(
      @PathVariable("allocationPreferenceId") AllocationPreferenceId allocationPreferenceId,
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestBody @Valid ReleasePreferenceInfo request) {

    AddReleasePreference addReleasePreference =
        AddReleasePreference.of(allocationPreferenceId, request);
    log.debug(String.format(RECEIVED_REQUEST_MSG, username, request));
    return releasePreferenceBL.handle(addReleasePreference, username);
  }

  @DeleteMapping("/release-preferences/{releasePreferenceId}")
  public ReleasePreferenceId removeReleasePreference(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable("releasePreferenceId") ReleasePreferenceId releasePreferenceId) {

    RemoveReleasePreference removeReleasePreference =
        RemoveReleasePreference.of(releasePreferenceId);

    log.debug(String.format(RECEIVED_REQUEST_MSG, username, removeReleasePreference));

    return releasePreferenceBL.handle(removeReleasePreference, username);
  }

  @PutMapping("/release-preferences/{releasePreferenceId}")
  public ReleasePreferenceUpdated updateReleasePreference(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @PathVariable ReleasePreferenceId releasePreferenceId,
      @RequestBody @Valid ReleasePreferenceInfo request) {

    log.debug(String.format(RECEIVED_REQUEST_MSG, username, request));
    request.validate();
    return releasePreferenceBL.handle(releasePreferenceId, request, username);
  }

  @GetMapping("/find-by-customer")
  public Page<AllocationPreferenceResponse> findAllocationPreferenceByCustomer(
      @RequestParam(value = "customerCode") String customerCode,
      @PageableDefault(sort = "customer.code", direction = Sort.Direction.ASC)
          Pageable pageRequest) {


    return allocationPreferenceBL.handle(AllocationPreferenceRequest.of(customerCode), pageRequest);
  }

  @GetMapping("/search")
  public List<AllocationPreferenceResponse> search(
      @RequestParam(value = "customerCode") String customerCode,
      @RequestParam(value = "allocationPreferenceStatus", required = false)
          AllocationPreferenceStatus allocationPreferenceStatus,
      @RequestParam(required = false) String sortByKey,
      @RequestParam(required = false) String sortByDirection) {

    return allocationPreferenceBL.handle(
        AllocationPreferenceSearchRequest.builder()
            .customerCode(customerCode)
            .allocationPreferenceStatus(allocationPreferenceStatus)
            .sortByKey(sortByKey)
            .sortByDirection(sortByDirection)
            .build());
  }

  @PutMapping("/bulk-update")
  public List<AllocationPreferenceId> bulkUpdate(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestBody @Valid BulkUpdateAllocationPreference request) {
    log.debug(String.format(RECEIVED_REQUEST_MSG, username, request));
    return allocationPreferenceBL.bulkUpdate(request, username);
  }
}

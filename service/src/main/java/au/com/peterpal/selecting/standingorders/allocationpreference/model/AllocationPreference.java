package au.com.peterpal.selecting.standingorders.allocationpreference.model;

import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.allocationpreference.events.AllocationPreferenceAdded;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.ToString;
import lombok.With;
import org.apache.commons.compress.utils.Lists;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.CascadeType;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "allocation_preference")
public class AllocationPreference {

  @EqualsAndHashCode.Include
  @NonNull
  @EmbeddedId
  private AllocationPreferenceId allocationPreferenceId;

  @NonNull
  @NotNull
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "customerId", nullable = false)
  private Customer customer;

  @Builder.Default
  @ManyToMany
  private List<Category> categories = new ArrayList<>();

  @NonNull
  @NotNull
  @Builder.Default
  @Enumerated(EnumType.STRING)
  private AllocationPreferenceStatus status = AllocationPreferenceStatus.ACTIVE;

  private String customerReference;

  private String deliveryInstructions;

  private String notes;

  @ManyToOne
  @JoinColumn(name = "fundId")
  private Fund fund;

  @Builder.Default
  @OneToMany(mappedBy = "allocationPreference")
  @Cascade(CascadeType.ALL)
  private List<ReleasePreference> releasePreferences = Lists.newArrayList();

//  public static AllocationPreference from(AllocationPrefInfo allocInfo, Fund fund) {
//    return Optional.ofNullable(allocInfo)
//        .map(a -> AllocationPreference.builder()
//            .allocationPreferenceId(AllocationPreferenceId.of(allocInfo.getId()))
//            .fund(fund)
//            .categories(allocInfo.getCategories())
//            .customerReference(allocInfo.getCustomerReference())
//            .deliveryInstructions(allocInfo.getDeliveryInstructions())
//            .notes(allocInfo.getNotes())
//            .build())
//        .orElse(null);
//  }

//  public void setCategories(List<Category> categories) {
//    this.categories = new HashSet<>(categories);
//  }
//  public List<Category> getCategories() {
//    return new ArrayList<Category>(categories);
//  }

  public static AllocationPreference from(UUID allocationPreferenceId) {

    return AllocationPreference.builder()
        .allocationPreferenceId(AllocationPreferenceId.of(allocationPreferenceId))
        .build();
  }

  public static AllocationPreference from(AllocationPreferenceAdded event) {

    AllocationPreference ap = AllocationPreference.builder()
        .allocationPreferenceId(AllocationPreferenceId.of(event.getId()))
        .fund(event.getFund())
        .categories(event.getCategories())
        .customerReference(event.getCustomerReference())
        .deliveryInstructions(event.getDeliveryInstructions())
        .notes(event.getNotes())
        .customer(event.getCustomer())
        .build();

    ap.addReleasePreference(ReleasePreference.builder()
        .releasePreferenceId(ReleasePreferenceId.of(UUID.randomUUID()))
        .releaseType(ReleaseType.INITIAL)
        .build()
    );
    return ap;
  }

  public AllocationPreference addReleasePreference(ReleasePreference relPref) {
    return Optional.ofNullable(relPref)
        .map(r -> {
          r.setAllocationPreference(this);
          releasePreferences.add(r);
          return this;
        })
        .orElseThrow(() -> new IllegalArgumentException("Release preference must not be null"));
  }

  public ReleasePreference getReleasePreference(ReleaseType releaseType) {
    return releasePreferences.stream()
        .filter(rt -> {
          ReleaseType rType =
              Optional.ofNullable(rt).map(ReleasePreference::getReleaseType).orElse(null);
          return Objects.equals(rType, releaseType);
        })
        .findFirst()
        .orElse(null);
  }

  public Fund selectFund(ReleaseType relType, ReleaseFormat format) {
    Optional.ofNullable(relType)
        .orElseThrow(() -> new IllegalArgumentException("ReleaseType must not be null"));
    Optional.ofNullable(format)
        .orElseThrow(() -> new IllegalArgumentException("Format must not be null"));

    Fund relPrefFund = Optional.ofNullable(getReleasePreference(relType))
        .map(r -> r.getFund(format))
        .orElse(null);
    return Optional.ofNullable(relPrefFund).orElse(this.fund);
  }
}

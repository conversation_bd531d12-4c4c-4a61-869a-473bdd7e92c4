package au.com.peterpal.selecting.standingorders.titles.dto;

import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus;
import au.com.peterpal.selecting.standingorders.titles.events.TitleProcessed;
import lombok.*;

import javax.validation.constraints.NotNull;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class CreateRelatedTitleForPausedAllocation {
  @NotNull @NonNull TitleId originalTitleId;
  TitleStatus relatedTitleStatus;

  public static CreateRelatedTitleForPausedAllocation from(TitleId titleId) {
    return CreateRelatedTitleForPausedAllocation.builder().originalTitleId(titleId).build();
  }
}

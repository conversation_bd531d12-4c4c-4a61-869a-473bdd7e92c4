package au.com.peterpal.selecting.standingorders.allocationpreference.dto;


import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import javax.validation.constraints.NotNull;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class AllocationPreferenceSearchRequest {

  @NotNull
  @NonNull
  String customerCode;
  AllocationPreferenceStatus allocationPreferenceStatus;
  @JsonIgnore
  String sortByKey;
  @JsonIgnore
  String sortByDirection;
}

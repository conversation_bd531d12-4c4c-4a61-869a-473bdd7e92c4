package au.com.peterpal.selecting.standingorders.allocationpreference.dto;

import java.util.List;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceStatus;
import lombok.*;

import javax.validation.constraints.NotNull;

@With
@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class UpdateAllocationPreference {

  @NotNull private List<String> category;

  private String customerReference;

  private String deliveryInstructions;

  private String notes;

  private String fundCode;

  private AllocationPreferenceStatus allocationPreferenceStatus;
}

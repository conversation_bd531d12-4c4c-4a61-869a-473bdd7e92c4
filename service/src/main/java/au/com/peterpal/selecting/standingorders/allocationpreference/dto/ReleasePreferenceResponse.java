package au.com.peterpal.selecting.standingorders.allocationpreference.dto;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.*;
import au.com.peterpal.selecting.standingorders.ext.fund.dto.FundInfo;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReleasePreferenceResponse {

  @NotNull @NonNull private ReleasePreferenceId releasePreferenceId;
  private ReleaseType releaseType;
  private ActionType actionType;
  private AssignmentRule assignmentRule;
  private SmallFormatPaperbackRule smallFormatPaperbackRule;
  @Builder.Default private Integer quantity = 0;
  @Builder.Default private Integer hbQuantity = 0;
  @Builder.Default private Integer pbQuantity = 0;

  private FundId fundId;
  private String fundCode;

  private FundId hbFundId;
  private String hbFundCode;

  private FundId pbFundId;
  private String pbFundCode;

  public static List<ReleasePreferenceResponse> from(
      List<ReleasePreference> releasePreferenceList) {
    Affirm.of(releasePreferenceList).notNull("ReleasePreferenceList must not be null");

    return releasePreferenceList.stream()
        .filter(rp -> ReleaseType.INITIAL.equals(rp.getReleaseType()))
        .map(ReleasePreferenceResponse::from)
        .collect(Collectors.toList());
  }

  public static ReleasePreferenceResponse from(ReleasePreference releasePreference) {
    Affirm.of(releasePreference).notNull("ReleasePreference must not be null");

    FundInfo fund =
        Optional.ofNullable(releasePreference.getFund()).map(FundInfo::from).orElse(null);
    FundInfo hbFund =
        Optional.ofNullable(releasePreference.getHardbackfund())
            .map(FundInfo::from)
            .orElse(null);
    FundInfo pbFund =
        Optional.ofNullable(releasePreference.getPaperbackfund())
            .map(FundInfo::from)
            .orElse(null);

    return ReleasePreferenceResponse.builder()
        .releasePreferenceId(releasePreference.getReleasePreferenceId())
        .actionType(releasePreference.getActionType())
        .assignmentRule(releasePreference.getInitialAssignmentRule())
        .releaseType(releasePreference.getReleaseType())
        .smallFormatPaperbackRule(releasePreference.getSmallFormatPaperbackRule())
        .fundId(Optional.ofNullable(fund).map(FundInfo::getFundId).orElse(null))
        .fundCode(Optional.ofNullable(fund).map(FundInfo::getCode).orElse(null))
        .hbFundId(Optional.ofNullable(hbFund).map(FundInfo::getFundId).orElse(null))
        .hbFundCode(Optional.ofNullable(hbFund).map(FundInfo::getCode).orElse(null))
        .pbFundId(Optional.ofNullable(pbFund).map(FundInfo::getFundId).orElse(null))
        .pbFundCode(Optional.ofNullable(pbFund).map(FundInfo::getCode).orElse(null))
        .build();
  }
}

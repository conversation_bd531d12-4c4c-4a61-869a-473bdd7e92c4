package au.com.peterpal.selecting.standingorders.customerstandingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocation.model.*;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.pendingorder.model.BranchDistribution;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.titles.dto.RematchAllocationResult;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class AllocationRematched extends DomainEvent {

  private AllocationId allocationId;
  private ReleaseQuantityUpdated releaseQuantityUpdated;
  private List<RematchResult> rematchResults;

  public static AllocationRematched from(
      Allocation allocation,
      List<RematchAllocationResult> rematchAllocationResults,
      String username) {
    Release release = allocation.getRelease(ReleaseType.INITIAL);
    List<RematchResult> rematchResults =
        rematchAllocationResults.stream()
            .map(
                r ->
                    RematchResult.builder()
                        .titleId(r.getTitle().getTitleId())
                        .pendingOrdersUpdated(
                            r.getPendingOrdersUpdated().stream()
                                .map(
                                    p ->
                                        PendingOrder.builder()
                                            .pendingOrderId(p.getPendingOrderId())
                                            .branchDistributions(p.getBranchDistributions())
                                            .quantity(p.getQuantity())
                                            .format(p.getFormat())
                                            .build())
                                .collect(Collectors.toList()))
                        .pendingOrdersCreated(
                            r.getPendingOrdersCreated().stream()
                                .map(
                                    p ->
                                        PendingOrder.builder()
                                            .pendingOrderId(p.getPendingOrderId())
                                            .branchDistributions(p.getBranchDistributions())
                                            .quantity(p.getQuantity())
                                            .format(p.getFormat())
                                            .build())
                                .collect(Collectors.toList()))
                        .build())
            .collect(Collectors.toList());

    return AllocationRematched.builder()
        .username(username)
        .id(allocation.getAllocationId().getId())
        .allocationId(allocation.getAllocationId())
        .rematchResults(rematchResults)
        .releaseQuantityUpdated(
            ReleaseQuantityUpdated.builder()
                .releaseId(release.getReleaseId())
                .quantity(release.getQuantity())
                .hardbackQuantity(release.getHardbackQuantity())
                .paperbackQuantity(release.getPaperbackQuantity())
                .build())
        .build();
  }

  @Builder
  @Getter
  static class RematchResult {
    TitleId titleId;
    List<PendingOrder> pendingOrdersCreated;
    List<PendingOrder> pendingOrdersUpdated;
  }

  @Builder
  @Getter
  public static class PendingOrder {
    PendingOrderId pendingOrderId;
    Integer quantity;
    ReleaseFormat format;
    Set<BranchDistribution> branchDistributions;
  }

  @Builder
  @Getter
  static class ReleaseQuantityUpdated {
    ReleaseId releaseId;
    Integer quantity;
    Integer hardbackQuantity;
    Integer paperbackQuantity;
  }
}

package au.com.peterpal.selecting.standingorders.gateways.clientweb.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.ToString;
import lombok.extern.log4j.Log4j2;

@Builder
@Data
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@Log4j2
public class ClientWebProductInfo {

  @NonNull
  public Integer id;

  @NonNull private ProductSummaryInfo productSummary;

  @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
  public ClientWebProductInfo(Integer id, ProductSummaryInfo productSummary) {
    this.id = id;
    this.productSummary = productSummary;
  }
}

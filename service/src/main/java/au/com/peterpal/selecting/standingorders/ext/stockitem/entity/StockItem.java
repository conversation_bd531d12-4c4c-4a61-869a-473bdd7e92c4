package au.com.peterpal.selecting.standingorders.ext.stockitem.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@RequiredArgsConstructor(staticName = "of")
@Builder
@With
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "invoicingProfile"})
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "stock_item")public class StockItem {
  @NonNull
  @EqualsAndHashCode.Include
  @ToString.Include
  @EmbeddedId
  private StockItemId stockItemId;

  @NotBlank
  private String productReference;

  private String author;

  private String titleWithoutPrefix;
}

package au.com.peterpal.selecting.standingorders.allocationpreference.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreference;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class ReleasePreferenceRemoved extends DomainEvent {

  public static ReleasePreferenceRemoved from(
      ReleasePreference releasePreference, String username) {
    return ReleasePreferenceRemoved.builder()
        .id(releasePreference.getReleasePreferenceId().getId())
        .username(username)
        .build();
  }
}

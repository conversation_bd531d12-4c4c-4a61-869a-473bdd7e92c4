package au.com.peterpal.selecting.standingorders.allocationpreference.control;

import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AllocationPreferenceResponse;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AllocationPreferenceSearchRequest;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.BulkUpdateAllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.QAllocationPreferenceCategoryAgg;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.QAllocationPreference;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.QCustomer;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
public class AllocationPreferenceRepositoryCustomImpl
    implements AllocationPreferenceRepositoryCustom {
  private static final String DESC = "desc";
  @PersistenceContext private final EntityManager entityManager;

  private QAllocationPreference allocationPreference = QAllocationPreference.allocationPreference;
  private QCustomer customer = QCustomer.customer;

  private QAllocationPreferenceCategoryAgg allocationPreferenceCategoryAgg = QAllocationPreferenceCategoryAgg.allocationPreferenceCategoryAgg;

  public AllocationPreferenceRepositoryCustomImpl(EntityManager entityManager) {
    this.entityManager = entityManager;
  }

  @Override
  public Page<AllocationPreference> searchAllByCustomer(String customerCode, Pageable pageRequest) {
    Affirm.of(customerCode).notNull("CustomerCode must not be null.");

    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    JPAQuery<AllocationPreference> query =
        factory
            .selectFrom(allocationPreference)
            .innerJoin(allocationPreference.customer, customer)
            .where(customer.code.eq(customerCode));

    return new PageImpl(
        applyPagingAndSorting(query, pageRequest).fetch(), pageRequest, query.fetchCount());
  }

  @Override
  public List<AllocationPreferenceResponse> search(
      AllocationPreferenceSearchRequest searchRequest) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    JPAQuery<AllocationPreference> query =
        factory
            .selectFrom(allocationPreference)
            .innerJoin(allocationPreference.customer, customer)
            .where(buildCondition(searchRequest));
    if ("categories".equalsIgnoreCase(searchRequest.getSortByKey())) {
      query = buildQueryForCategoriesSort(factory, searchRequest);
    }
    long total = query.fetchCount();
    query = addSortToQuery(searchRequest, query);
    return query.fetch().stream()
        .map(AllocationPreferenceResponse::from)
        .collect(Collectors.toList());
  }

  private JPAQuery<AllocationPreference> addSortToQuery(
      AllocationPreferenceSearchRequest searchRequest, JPAQuery<AllocationPreference> query) {
    if (searchRequest.getSortByKey() != null) {
      if (searchRequest.getSortByKey().equalsIgnoreCase("categories")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(allocationPreferenceCategoryAgg.category.desc());
        } else {
          query.orderBy(allocationPreferenceCategoryAgg.category.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("allocationPreferenceStatus")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(allocationPreference.status.desc());
        } else {
          query.orderBy(allocationPreference.status.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("customerReference")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(allocationPreference.customerReference.desc());
        } else {
          query.orderBy(allocationPreference.customerReference.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("deliveryInstructions")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(allocationPreference.deliveryInstructions.desc());
        } else {
          query.orderBy(allocationPreference.deliveryInstructions.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("notes")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(allocationPreference.notes.desc());
        } else {
          query.orderBy(allocationPreference.notes.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("fundCode")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(allocationPreference.fund.code.desc());
        } else {
          query.orderBy(allocationPreference.fund.code.asc());
        }
      }
    }
    return query;
  }

  private JPAQuery<AllocationPreference> buildQueryForCategoriesSort(
      JPAQueryFactory factory, AllocationPreferenceSearchRequest searchRequest) {
    return factory
        .selectFrom(allocationPreference)
        .innerJoin(allocationPreference.customer, customer)
        .leftJoin(allocationPreferenceCategoryAgg)
        .on(
            allocationPreference.allocationPreferenceId.eq(
                allocationPreferenceCategoryAgg.allocationPreferenceId))
        .where(buildCondition(searchRequest));
  }

  private BooleanBuilder buildCondition(AllocationPreferenceSearchRequest searchRequest) {
    BooleanBuilder condition = new BooleanBuilder();
    if (Objects.nonNull(searchRequest.getAllocationPreferenceStatus())) {
      condition.and(allocationPreference.status.eq(searchRequest.getAllocationPreferenceStatus()));
    }
    if (Objects.nonNull(searchRequest.getCustomerCode())) {
      condition.and(customer.code.eq(searchRequest.getCustomerCode()));
    }
    return condition;
  }

  private JPAQuery<AllocationPreference> applyPagingAndSorting(
      JPAQuery<AllocationPreference> query, Pageable pageRequest) {
    if (Objects.nonNull(pageRequest)) {
      query.offset(pageRequest.getOffset()).limit(pageRequest.getPageSize());

      PathBuilder<AllocationPreference> pathBuilder =
          new PathBuilder<>(AllocationPreference.class, "allocationPreference");

      pageRequest
          .getSort()
          .forEach(
              order ->
                  query.orderBy(
                      new OrderSpecifier(
                          order.isAscending() ? Order.ASC : Order.DESC,
                          pathBuilder.get(order.getProperty()))));
    }

    return query;
  }
}

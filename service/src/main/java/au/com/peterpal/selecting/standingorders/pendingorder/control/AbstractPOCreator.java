package au.com.peterpal.selecting.standingorders.pendingorder.control;

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.CopyPendingOrderDto;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.POCreateInfo;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

public abstract class AbstractPOCreator implements POCreator {
  @Override
  public List<PendingOrder> create(POCreateInfo info) {
    Optional.ofNullable(info)
        .orElseThrow(() -> new IllegalArgumentException("PO create info must not be null"));

    Allocation allocation = info.getAllocation();
    Release release = info.getRelease();

    // Validate that release belongs to allocation.
    Release r = allocation.getRelease(release.getReleaseType());
    if (!r.getReleaseId().getId().equals(release.getReleaseId().getId())) {
      throw new IllegalStateException(
          String.format("Release %s does not belong to allocation %s", release, allocation));
    }
    return createPOList(info);
  }

  /**
   * The logic for selecting the fund for a pending order is as follows:
   *  <ul>
   *    <li>Select the fund from the release</li>
   *    <li>If the fund is null, select from the allocation</li>
   *    <li>If the allocation fund is null, use the release preference</li>
   *    <li>If the release preference fund is null, then use the fund from the allocation preference</li>
   *  </ul>
   * @param format the release format - HB, PB,...
   * @param release the release
   * @param allocation the allocation
   * @param allocPref the allocation preference
   * @return fund
   */
  protected Fund getFund(ReleaseFormat format, Release release, Allocation allocation, AllocationPreference allocPref) {
    Fund relFund = Optional.ofNullable(release)
        .map(r -> r.getFund(format))
        .orElse(null);
    Fund allocFund = Optional.ofNullable(relFund).orElse(allocation.getFund());
    return Optional.ofNullable(allocFund).orElse(
        Optional.ofNullable(allocPref)
            .map(ap -> {
              if (release != null) {
                return ap.selectFund(release.getReleaseType(), format);
              }
              return null;
            })
            .orElse(null)
    );
  }

  protected abstract List<PendingOrder> createPOList(POCreateInfo info);

  protected String getCustomerRef(String custRef, AllocationPreference allocPref) {
    if (StringUtils.isBlank(custRef) && allocPref != null) {
      return allocPref.getCustomerReference();
    }
    return custRef;
  }

  protected String getDeliveryInst(String inst, AllocationPreference allocPref) {
    if (StringUtils.isBlank(inst) && allocPref != null) {
      return allocPref.getDeliveryInstructions();
    }
    return inst;
  }

  protected String getNotes(String notes, AllocationPreference allocPref) {
    if (StringUtils.isBlank(notes) && allocPref != null) {
      return allocPref.getNotes();
    }
    return notes;
  }

}

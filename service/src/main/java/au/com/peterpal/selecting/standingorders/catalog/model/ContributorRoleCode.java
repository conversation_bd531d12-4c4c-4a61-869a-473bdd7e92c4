package au.com.peterpal.selecting.standingorders.catalog.model;

import java.util.ArrayList;
import java.util.List;

/**
 * @docRoot
 * Based on Onix List 17
 */
public enum ContributorRoleCode {

  A01 ("A01", "By", "Author of a textual work."),
  A02 ("A02", "With", "With or as told to: ‘ghost’ author of a literary work."),
  A03 ("A03", "Screenplay by", "Writer of screenplay or script (film or video)."),
  A04 ("A04", "Libretto by", "Writer of libretto (opera): see also A31."),
  A05 ("A05", "Lyrics by", "Author of lyrics (song): see also A31."),
  A06 ("A06", "Composer", "Composer of music."),
  A07 ("A07", "Artist", "Visual artist when named as the primary creator of, eg, a book of reproductions of artworks."),
  A08 ("A08", "Photographer", "Photographer when named as the primary creator of, eg, a book of photographs."),
  A09 ("A09", "Created by", ""),
  A10 ("A10", "From an idea by", ""),
  A11 ("A11", "Designed by", ""),
  A12 ("A12", "Illustrated by", "Artist when named as the creator of artwork which illustrates a text, or the originator (sometimes ‘penciller’ for collaborative art) of the artwork of a graphic novel or comic book."),
  A13 ("A13", "Photographs by", "Photographer when named as the creator of photographs which illustrate a text."),
  A14 ("A14", "Text by", "Author of text which accompanies art reproductions or photographs, or which is part of a graphic novel or comic book."),
  A15 ("A15", "Preface by", "Author of preface."),
  A16 ("A16", "Prologue by", "Author of prologue."),
  A17 ("A17", "Summary by", "Author of summary."),
  A18 ("A18", "Supplement by", "Author of supplement."),
  A19 ("A19", "Afterword by", "Author of afterword."),
  A20 ("A20", "Notes by", "Author of notes or annotations: see also A29."),
  A21 ("A21", "Commentaries by", "Author of commentaries on the main text."),
  A22 ("A22", "Epilogue by", "Author of epilogue."),
  A23 ("A23", "Foreword by", "Author of foreword."),
  A24 ("A24", "Introduction by", "Author of introduction: see also A29."),
  A25 ("A25", "Footnotes by", "Author/compiler of footnotes."),
  A26 ("A26", "Memoir by", "Author of memoir accompanying main text."),
  A27 ("A27", "Experiments by", "Person who carried out experiments reported in the text."),
  A29 ("A29", "Introduction and notes by", "Author of introduction and notes: see also A20 and A24."),
  A30 ("A30", "Software written by", "Writer of computer programs ancillary to the text."),
  A31 ("A31", "Book and lyrics by", "Author of the textual content of a musical drama: see also A04 and A05."),
  A32 ("A32", "Contributions by", "Author of additional contributions to the text."),
  A33 ("A33", "Appendix by", "Author of appendix."),
  A34 ("A34", "Index by", "Compiler of index."),
  A35 ("A35", "Drawings by", ""),
  A36 ("A36", "Cover design or artwork by", "Use also for the cover artist of a graphic novel or comic book if named separately."),
  A37 ("A37", "Preliminary work by", "Responsible for preliminary work on which the work is based."),
  A38 ("A38", "Original author", "Author of the first edition (usually of a standard work) who is not an author of the current edition."),
  A39 ("A39", "Maps by", "Maps drawn or otherwise contributed by."),
  A40 ("A40", "Inked or colored by", "Use for secondary creators when separate persons are named as having respectively drawn and inked/colored/finished artwork, eg for a graphic novel or comic book. Use with A12 for ‘drawn by’. Use A40 for 'finished by', but prefer more specific codes A46 to A48 instead of A40 unless the more specific secondary roles are inappropriate, unclear or unavailable."),
  A41 ("A41", "Pop-ups by", "Designer of pop-ups in a pop-up book, who may be different from the illustrator."),
  A42 ("A42", "Continued by", "Use where a standard work is being continued by somebody other than the original author."),
  A43 ("A43", "Interviewer", ""),
  A44 ("A44", "Interviewee", ""),
  A45 ("A45", "Comic script by", "Writer of dialogue, captions in a comic book (following an outline by the primary writer)."),
  A46 ("A46", "Inker", "Renders final comic book line art based on work of the illustrator or penciller. Preferred to code A40."),
  A47 ("A47", "Colorist", "Provides comic book color art and effects. Preferred to code A40."),
  A48 ("A48", "Letterer", "Creates comic book text balloons and other text elements (where this is a distinct role from script writer and/or illustrator)."),
  A99 ("A99", "Other primary creator", "Other type of primary creator not specified above."),
  B01 ("B01", "Edited by", ""),
  B02 ("B02", "Revised by", ""),
  B03 ("B03", "Retold by", ""),
  B04 ("B04", "Abridged by", ""),
  B05 ("B05", "Adapted by", ""),
  B06 ("B06", "Translated by", ""),
  B07 ("B07", "As told by", ""),
  B08 ("B08", "Translated with commentary by", "This code applies where a translator has provided a commentary on issues relating to the translation. If the translator has also provided a commentary on the work itself, codes B06 and A21 should be used."),
  B09 ("B09", "Series edited by", "Name of a series editor when the product belongs to a series."),
  B10 ("B10", "Edited and translated by", ""),
  B11 ("B11", "Editor-in-chief", ""),
  B12 ("B12", "Guest editor", ""),
  B13 ("B13", "Volume editor", ""),
  B14 ("B14", "Editorial board member", ""),
  B15 ("B15", "Editorial coordination by", ""),
  B16 ("B16", "Managing editor", ""),
  B17 ("B17", "Founded by", "Usually the founder editor of a serial publication: Begruendet von."),
  B18 ("B18", "Prepared for publication by", ""),
  B19 ("B19", "Associate editor", ""),
  B20 ("B20", "Consultant editor", "Use also for ‘advisory editor’."),
  B21 ("B21", "General editor", ""),
  B22 ("B22", "Dramatized by", ""),
  B23 ("B23", "General rapporteur", "In Europe, an expert editor who takes responsibility for the legal content of a collaborative law volume."),
  B24 ("B24", "Literary editor", "An editor who is responsible for establishing the text used in an edition of a literary work, where this is recognised as a distinctive role (in Spain, ‘editor literario’)."),
  B25 ("B25", "Arranged by (music)", ""),
  B26 ("B26", "Technical editor", ""),
  B27 ("B27", "Thesis advisor or supervisor", ""),
  B28 ("B28", "Thesis examiner", ""),
  B99 ("B99", "Other adaptation by", "Other type of adaptation or editing not specified above."),
  C01 ("C01", "Compiled by", ""),
  C02 ("C02", "Selected by", ""),
  C99 ("C99", "Other compilation by", "Other type of compilation not specified above."),
  D01 ("D01", "Producer", ""),
  D02 ("D02", "Director", ""),
  D03 ("D03", "Conductor", "Conductor of a musical performance."),
  D99 ("D99", "Other direction by", "Other type of direction not specified above."),
  E01 ("E01", "Actor", ""),
  E02 ("E02", "Dancer", ""),
  E03 ("E03", "Narrator", ""),
  E04 ("E04", "Commentator", ""),
  E05 ("E05", "Vocal soloist", "Singer etc."),
  E06 ("E06", "Instrumental soloist", ""),
  E07 ("E07", "Read by", "Reader of recorded text, as in an audiobook."),
  E08 ("E08", "Performed by (orchestra, band, ensemble)", "Name of a musical group in a performing role."),
  E09 ("E09", "Speaker", "Of a speech, lecture etc."),
  E99 ("E99", "Performed by", "Other type of performer not specified above: use for a recorded performance which does not fit a category above, eg a performance by a stand-up comedian."),
  F01 ("F01", "Filmed/photographed by", "Cinematographer, etc."),
  F02 ("F02", "Editor (film or video)", ""),
  F99 ("F99", "Other recording by", "Other type of recording not specified above."),
  Z01 ("Z01", "Assisted by", "May be associated with any contributor role, and placement should therefore be controlled by contributor sequence numbering."),
  Z98 ("Z98", "(Various roles)", "For use ONLY with ‘et al’ or ‘Various’ within <UnnamedPersons>, where the roles of the multiple contributors vary."),
  Z99 ("Z99", "Other", "Other creative responsibility not falling within A to F above.");

  private final String code;
  private final String description;
  private final String notes;

  ContributorRoleCode(String code, String description, String notes) {
    this.code = code;
    this.description = description;
    this.notes = notes;
  }

  public String getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public String getNotes() {
    return notes;
  }

  public static ContributorRoleCode mapOnixCode(String onixCode) {
    for (ContributorRoleCode value : ContributorRoleCode.values()) {
      if (value.code.equals(onixCode)) {
        return value;
      }
    }
    throw new IllegalArgumentException("Invalid " + ContributorRoleCode.class.getSimpleName() + ": " + onixCode);
  }

  public static String[] getCodes() {
    List<String> codes = new ArrayList<>();
    for ( ContributorRoleCode code : ContributorRoleCode.values() ) {
      codes.add(code.getCode());
    }
    return codes.toArray(new String[codes.size()]);
  }

  public static String[] getDescriptions() {
    List<String> codes = new ArrayList<>();
    for ( ContributorRoleCode code : ContributorRoleCode.values() ) {
      codes.add(code.getDescription());
    }
    return codes.toArray(new String[codes.size()]);
  }

}

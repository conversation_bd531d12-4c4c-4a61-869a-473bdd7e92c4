package au.com.peterpal.selecting.standingorders.customerstandingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class CustomerStandingOrderUpdated extends DomainEvent {

  private CustomerStandingOrderId customerStandingOrderId;

  private StandingOrderStatus customerStandingOrderStatus;

  private CustomerId customerId;

  private StandingOrderId standingOrderId;

  public static CustomerStandingOrderUpdated from(
      CustomerId customerId,
      CustomerStandingOrder customerStandingOrder,
      StandingOrderId standingOrderId,
      String username) {

    return CustomerStandingOrderUpdated.builder()
        .id(customerStandingOrder.getCustomerStandingOrderId().getId())
        .customerStandingOrderId(customerStandingOrder.getCustomerStandingOrderId())
        .customerId(customerId)
        .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
        .standingOrderId(standingOrderId)
        .username(username)
        .build();
  }
}

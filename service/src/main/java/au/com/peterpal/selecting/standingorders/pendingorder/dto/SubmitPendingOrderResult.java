package au.com.peterpal.selecting.standingorders.pendingorder.dto;

import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.util.List;

@With
@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
@ApiModel
public class SubmitPendingOrderResult {
  private List<PendingOrder> cancelPendingOrders;
  private List<PendingOrder> acceptAndSubmitPendingOrders;
}

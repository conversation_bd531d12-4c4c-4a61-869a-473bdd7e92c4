package au.com.peterpal.selecting.standingorders.gateways.clientweb.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.ToString;
import lombok.Value;

@Builder
@Value
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TitleInfo {
  private String titleText;

  @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
  public TitleInfo(String titleText) {
    this.titleText = titleText;
  }
}

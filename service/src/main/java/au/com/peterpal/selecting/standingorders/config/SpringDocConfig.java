package au.com.peterpal.selecting.standingorders.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.context.annotation.Configuration;

@Configuration
@OpenAPIDefinition(
    info = @Info(title = "Standing Orders Service", version = "v1.0", description = "Standing Orders API Service"))
public class SpringDocConfig {}

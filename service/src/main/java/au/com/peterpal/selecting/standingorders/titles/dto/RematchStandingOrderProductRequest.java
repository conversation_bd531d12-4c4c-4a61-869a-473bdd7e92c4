package au.com.peterpal.selecting.standingorders.titles.dto;

import java.time.LocalDate;
import javax.validation.constraints.NotNull;

import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

@With
@Value
@NoArgsConstructor(force = true)
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RematchStandingOrderProductRequest {
  private StandingOrderId standingOrderId;
  private String productReference;
}

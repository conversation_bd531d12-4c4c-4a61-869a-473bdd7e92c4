package au.com.peterpal.selecting.standingorders.admin.control;

import au.com.peterpal.selecting.standingorders.admin.control.StdOrderImportAgent.SONumberConverter;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.SmallFormatPaperbackRule;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.customerstandingorder.control.CustomerStandingOrderBL;
import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.CSOInfo;
import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.CSOInfo.ReleaseInfo;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerService;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.standingorder.control.CategoryService;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderBL;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.utils.Affirm;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.web.multipart.MultipartFile;

/**
 * Excel import agent for customer standing order, allocation and release.
 *
 * <pre>
 *   SQL to select data to be imported:
 *
 *    SELECT
 *      soi.standingOrdNum, soi.customerCode, soi.categoryCode, soi.fundCode, soi.custRefNum,
 *      soi.deliveryInstructions, soi.notes, soi.initialReleaseAction, soi.initialReleaseAssignmentRule,
 *      soi.initialReleasePBSmallRule, soi.initialReleaseQtyTotal, soi.initialReleaseQtyHB,
 *      soi.initialReleaseQtyPB, soi.initialReleaseFundCode, soi.initialReleaseFundCodeHB,
 *      soi.initialReleaseFundCodePB, soi.firstPBSmallReleaseAction, soi.firstPBSmallReleaseQtyTotal,
 *      soi.firstPBSmallReleaseFundCode, soi.reissueAction, soi.reissueQtyTotal, soi.reissueFundCode
 *    FROM StandingOrderItem soi
 *    INNER JOIN StandingOrder so ON soi.standingOrdNum = so.standingOrdNum
 *    INNER join Cust cust on cust.customerCode = soi.customerCode
 *    WHERE soi.statusCode = 'ACTIVE'
 *    AND soi.categoryCode is not null
 *    AND soi.categoryCode <> ''
 *    AND so.standingOrdNum <> ''
 *    order by soi.standingOrdNum
 * </pre>
 */
@Log4j2
public class AllocationImportAgent extends ExcelImportAgent {

  private static int STANDING_ORDER_CELL_ID = 0;
  private static int CUSTOMER_CODE_CELL_ID = 1;
  private static int CATEGORY_CELL_ID = 2;
  private static int FUND_CELL_ID = 3;
  private static int CUSTOMER_REF_CELL_ID = 4;
  private static int DELIVERY_INSTRUCTIONS_CELL_ID = 5;
  private static int NOTES_CELL_ID = 6;
  private static int INITIAL_ACTION_CELL_ID = 7;
  private static int INITIAL_ASSIGN_RULE_CELL_ID = 8;
  private static int INITIAL_SFPB_RULE_CELL_ID = 9;
  private static int INITIAL_QTY_TOTAL_CELL_ID = 10;
  private static int INITIAL_QTY_HB_CELL_ID = 11;
  private static int INITIAL_QTY_PB_CELL_ID = 12;
  private static int INITIAL_FUND_CODE_CELL_ID = 13;
  private static int INITIAL_HB_FUND_CODE_CELL_ID = 14;
  private static int INITIAL_PB_FUND_CODE_CELL_ID = 15;
  private static int FIRST_ACTION_CELL_ID = 16;
  private static int FIRST_QTY_TOTAL_CELL_ID = 17;
  private static int FIRST_FUND_CODE_CELL_ID = 18;
  private static int REISSUE_ACTION_CELL_ID = 19;
  private static int REISSUE_QTY_TOTAL_CELL_ID = 20;
  private static int REISSUE_FUND_CODE_CELL_ID = 21;

  private final CustomerStandingOrderBL service;
  private final StandingOrderBL standingOrderBL;

  private final CustomerService customerService;
  private final CategoryService categoryService;

  private List<CSOInfo> csoInfoList = new ArrayList<>();

  public static final AllocationImportAgent of(MultipartFile file, CustomerStandingOrderBL service,
      StandingOrderBL standingOrderBL, CustomerService customerService,
      CategoryService categoryService) {
    return new AllocationImportAgent(file, service, standingOrderBL, customerService,
        categoryService);
  }

  private AllocationImportAgent(MultipartFile file, CustomerStandingOrderBL service,
      StandingOrderBL standingOrderBL, CustomerService customerService,
      CategoryService categoryService) {
    super(file);
    this.standingOrderBL = standingOrderBL;
    this.customerService = customerService;
    Affirm.of(service).notNull("Customer standing order service must not be null");
    this.service = service;
    this.categoryService = categoryService;
  }

  @Override
  protected void doImport() {
    Sheet sheet = getFirstSheet();

    Map<String, Category> categoryCache = categoryService.getCategoryCache();

    int importCount = 0;
    Integer errorCount = 0;
    log.info("Started importing rows");
    for (Row row : sheet) {
      if (row.getRowNum() == 0) {
        continue;
      }

      CSOInfo csoInfo;
      try {
        csoInfo = getCSOInfo(row);
        csoInfo.setReleaseInfo(initialRelease(row));
        csoInfoList.add(csoInfo);
      } catch (Exception ex) {
        String rowStr = toString(row);
        String msg = String.format("Ignoring line %s", rowStr);
        log.warn(msg, ex);
        errorCount++;
        report.getErrors().add(msg);
      }
    }
    log.info("Finish importing rows");

    Map<String, Customer> customerMap = customerService.getAll().stream()
        .sorted(Comparator.comparing(Customer::getCode))
        .collect(Collectors.toMap(e -> e.getCode(), e -> e));

    Map<String, StandingOrder> allStandingOrders = standingOrderBL.getAllStandingOrders();

    Map<String, Fund> fundMap = service.findAllFunds();
    for (Map.Entry<String, Fund> entry : fundMap.entrySet()) {
      String key = entry.getKey();
      Fund value = entry.getValue();
      log.debug("Key: " + key + ", Value: " + value.getCode());
    }

    report.setTotal(csoInfoList.size());

    AllocationImportAgentForCustomer allocationImportAgentForCustomer = new AllocationImportAgentForCustomer();

    Map<String, CustomerStandingOrder> csoList = new HashMap<>();
    log.info("Starting to create customer standing orders");
    List<Customer> customersSorted = customerMap.values().stream()
        .sorted(Comparator.comparing(Customer::getCode)).collect(Collectors.toList());
    for (Customer customer : customersSorted) {
      List<CSOInfo> customerCsoList = csoInfoList.stream()
          .filter(e -> e.getCustomerCode().equals(customer.getCode())).collect(Collectors.toList());

      if (customerCsoList.isEmpty()) {
        continue;
      }

      log.info("Starting to create customer standing orders for customer: " + customer.getCode());

      Map<String, CustomerStandingOrder> customerStandingOrderMap = service.findAllStandingOrderForACustomer(
          customer);

      allocationImportAgentForCustomer.handle(customerCsoList, allStandingOrders,
          customerStandingOrderMap, categoryCache, csoList,
          customer, test,
          "lucy-so", fundMap, errorCount, report);
      importCount = importCount + customerCsoList.size();
      log.debug(String.format("imported  %d records so far", importCount));
    }
    log.info("Finish creating customer standing orders");
    log.debug(String.format("before save %d records ", csoList.size()));
    if (!csoList.values().isEmpty()) {
      List<CustomerStandingOrder> list = new ArrayList<>(csoList.values());
      for (int i = 0; i < list.size(); i = i + service.getBatchSize()) {
        int toIndex = i + service.getBatchSize() > list.size() ? list.size() : i + service.getBatchSize();
        List<CustomerStandingOrder> slice = list.subList(i, toIndex);
        log.debug(
            String.format("Saving batch of %d to %d customer standing orders; total size %d", i, toIndex,
                list.size()));
        service.saveSubList(slice, "lucy-so");
      }
    }
    log.info(String.format("Customer standing orders saved"));
    report.setImported(importCount);
    report.setFailed(errorCount);
  }

  private CSOInfo getCSOInfo(Row row) {
    Affirm.of(row).notNull("Row must not be null");

    String soNumber = getString(row.getCell(STANDING_ORDER_CELL_ID),
        "Standing order number must not be null");
    Integer sonumber = SONumberConverter.of().convert(soNumber);
    soNumber = String.format("SO-%08d", sonumber.longValue());

    return CSOInfo.builder()
        .standingOrderNumber(soNumber)
        .customerCode(
            getString(row.getCell(CUSTOMER_CODE_CELL_ID), "Customer code must not be null"))
        .categories(List.of(getString(row.getCell(CATEGORY_CELL_ID))))
        .fundCode(getString(row.getCell(FUND_CELL_ID)))
        .customerReference(getString(row.getCell(CUSTOMER_REF_CELL_ID)))
        .deliveryInstructions(getString(row.getCell(DELIVERY_INSTRUCTIONS_CELL_ID)))
        .notes(getString(row.getCell(NOTES_CELL_ID)))
        .build();
  }

  private ReleaseInfo initialRelease(Row row) {
    return ReleaseInfo.builder()
        .id(UUID.randomUUID())
        .releaseType(ReleaseType.INITIAL)
        .actionType(ActionType.valueAt(getInt(row.getCell(INITIAL_ACTION_CELL_ID),
            "Release of type initial, action type must not be null")))
        .fundCode(getString(row.getCell(INITIAL_FUND_CODE_CELL_ID)))
        .hardbackFundCode(getString(row.getCell(INITIAL_HB_FUND_CODE_CELL_ID)))
        .paperbackFundCode(getString(row.getCell(INITIAL_PB_FUND_CODE_CELL_ID)))
        .qtyTotal(getIntValue(row.getCell(INITIAL_QTY_TOTAL_CELL_ID)))
        .qtyHardback(getIntValue(row.getCell(INITIAL_QTY_HB_CELL_ID)))
        .qtyPaperback(getIntValue(row.getCell(INITIAL_QTY_PB_CELL_ID)))
        .assignmentRule(AssignmentRule.valueAt(
            getInt(row.getCell(INITIAL_ASSIGN_RULE_CELL_ID), "Assignment rule required") - 1))
        .smallFormatPaperbackRule(
            SmallFormatPaperbackRule.values()[getInt(row.getCell(INITIAL_SFPB_RULE_CELL_ID),
                "Small format paperback rule required")])
        .build();
  }

}

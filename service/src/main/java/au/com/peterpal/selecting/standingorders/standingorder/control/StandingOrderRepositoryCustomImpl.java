package au.com.peterpal.selecting.standingorders.standingorder.control;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocation.model.QAllocation;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.QCustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.ext.category.entity.QCategory;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.QCustomer;
import au.com.peterpal.selecting.standingorders.standingorder.dto.*;
import au.com.peterpal.selecting.standingorders.standingorder.model.QStandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.QTerm;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.titles.entity.QStandingOrderAllocationCategoryAgg;
import au.com.peterpal.selecting.standingorders.titles.entity.QStandingOrderCustomerAgg;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import java.util.*;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
public class StandingOrderRepositoryCustomImpl implements StandingOrderRepositoryCustom {
  private static final String DESC = "desc";
  @PersistenceContext
  private final EntityManager entityManager;

  private QStandingOrder standingOrder = QStandingOrder.standingOrder;
  private QCustomer customer = QCustomer.customer;
  private QCustomerStandingOrder customerStandingOrder =
      QCustomerStandingOrder.customerStandingOrder;

  private QTerm term = QTerm.term;

  private QAllocation allocation = QAllocation.allocation;

  private QCategory category = QCategory.category;

  private QStandingOrderAllocationCategoryAgg standingOrderAllocationCategoryAgg = QStandingOrderAllocationCategoryAgg.standingOrderAllocationCategoryAgg;

  private QStandingOrderCustomerAgg standingOrderCustomerAgg = QStandingOrderCustomerAgg.standingOrderCustomerAgg;

  public StandingOrderRepositoryCustomImpl(EntityManager entityManager) {
    this.entityManager = entityManager;
  }

  private JPAQuery<StandingOrder> buildBaseSearchQuery(JPAQueryFactory factory) {
    return factory
        .selectFrom(standingOrder)
        .from(standingOrder)
        .distinct()
        .leftJoin(customerStandingOrder)
        .on(customerStandingOrder.standingOrder.eq(standingOrder))
        .leftJoin(customerStandingOrder.customer, customer)
        .leftJoin(term)
        .on(term.standingOrder.eq(standingOrder))
        .leftJoin(customerStandingOrder.allocations, allocation)
        .leftJoin(allocation.categories, category);
  }

  @Override
  public Page<StandingOrderResponse> search(StandingOrderRequest searchRequest, Pageable pageRequest) {
    Affirm.of(searchRequest).notNull("SearchRequest must not be null");
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    JPAQuery<StandingOrderResponse> query = buildBaseSearchQuery(factory)
        .select(
            Projections.bean(
                StandingOrderResponse.class,
                standingOrder.standingOrderId,
                standingOrder.standingOrderStatus.as("status"),
                standingOrder.standingOrderNumber,
                standingOrder.description,
                standingOrder.notes));

    if ("allocationCategories".equalsIgnoreCase(searchRequest.getSortByKey())) {
      query = buildQueryForAllocationCategoriesSort(factory);
    }

    if("customers".equalsIgnoreCase(searchRequest.getSortByKey())){
      query = buildQueryForCustomerSort(factory);
    }
    query = query.where(buildCondition(searchRequest));

    long total = query.fetchCount();
    query = addSortToQuery(searchRequest, query);
    List<StandingOrderResponse> responses = applyPagingAndSorting(query, pageRequest).fetch();

    Set<StandingOrderId> standingOrderIds = responses.stream().map(StandingOrderResponse::getStandingOrderId).collect(Collectors.toSet());
    Map<StandingOrderId, List<StandingOrderCustomerResponse>> mapOfCustomers = fetchStandingOrderCustomers(factory, standingOrderIds, searchRequest);
    Map<StandingOrderId, List<StandingOrderCategoryResponse>> mapOfCategories = fetchStandingOrderCategories(factory, standingOrderIds, searchRequest);

    responses.forEach(
        standingOrderResponse -> {
          standingOrderResponse.setCustomers(
              mapOfCustomers
                  .getOrDefault(standingOrderResponse.getStandingOrderId(), Collections.emptyList())
                  .stream()
                  .map(StandingOrderCustomerResponse::getCustomer)
                  .collect(Collectors.toSet()));
          standingOrderResponse.setAllocationCategories(
              mapOfCategories
                  .getOrDefault(standingOrderResponse.getStandingOrderId(), Collections.emptyList())
                  .stream()
                  .map(StandingOrderCategoryResponse::getCategory)
                  .collect(Collectors.toSet()));
        });
    return new PageImpl(responses, pageRequest, total);
  }

  private JPAQuery<StandingOrderResponse> buildQueryForCustomerSort(JPAQueryFactory factory) {
    return buildBaseSearchQuery(factory)
        .select(
            Projections.bean(
                StandingOrderResponse.class,
                standingOrder.standingOrderId,
                standingOrder.standingOrderStatus.as("status"),
                standingOrder.standingOrderNumber,
                standingOrder.description,
                standingOrder.notes,
                standingOrderCustomerAgg.customerCode))
        .leftJoin(standingOrderCustomerAgg)
        .on(standingOrder.standingOrderId.eq(standingOrderCustomerAgg.standingOrderId));
  }

  private JPAQuery<StandingOrderResponse> buildQueryForAllocationCategoriesSort(JPAQueryFactory factory) {
    return buildBaseSearchQuery(factory)
            .select(
                Projections.bean(
                    StandingOrderResponse.class,
                    standingOrder.standingOrderId,
                    standingOrder.standingOrderStatus.as("status"),
                    standingOrder.standingOrderNumber,
                    standingOrder.description,
                    standingOrder.notes,
                    standingOrderAllocationCategoryAgg.allocationCategory))
            .leftJoin(standingOrderAllocationCategoryAgg)
            .on(standingOrder.standingOrderId.eq(standingOrderAllocationCategoryAgg.standingOrderId));
  }

  private Map<StandingOrderId, List<StandingOrderCustomerResponse>> fetchStandingOrderCustomers(
      JPAQueryFactory factory,
      Set<StandingOrderId> standingOrderIds,
      StandingOrderRequest searchRequest) {

    BooleanBuilder condition =
        new BooleanBuilder().and(standingOrder.standingOrderId.in(standingOrderIds));

    if (Objects.nonNull(searchRequest.getCustomerId())) {
      condition.and(customer.customerId.eq(searchRequest.getCustomerId()));
    }

    List<StandingOrderCustomerResponse> standingOrderDetailsRespons =
        factory
            .select(
                Projections.bean(
                    StandingOrderCustomerResponse.class, standingOrder.standingOrderId, customer))
            .from(standingOrder)
            .innerJoin(customerStandingOrder)
            .on(customerStandingOrder.standingOrder.eq(standingOrder))
            .innerJoin(customerStandingOrder.customer, customer)
            .where(condition)
            .fetch();
    return standingOrderDetailsRespons.stream()
        .collect(Collectors.groupingBy(StandingOrderCustomerResponse::getStandingOrderId));
  }

  private Map<StandingOrderId, List<StandingOrderCategoryResponse>> fetchStandingOrderCategories(
      JPAQueryFactory factory,
      Set<StandingOrderId> standingOrderIds,
      StandingOrderRequest searchRequest) {

    BooleanBuilder condition =
        new BooleanBuilder()
            .and(standingOrder.standingOrderId.in(standingOrderIds))
            .and(allocation.status.in(AllocationStatus.ACTIVE, AllocationStatus.PAUSED));

    if (Objects.nonNull(searchRequest.getCustomerId())) {
      condition.and(customer.customerId.eq(searchRequest.getCustomerId()));
    }

    if (CollectionUtils.isNotEmpty(searchRequest.getCategories())) {
      condition.and(category.code.in(searchRequest.getCategories()));
    }

    List<StandingOrderCategoryResponse> response =
        factory
            .select(
                Projections.bean(
                    StandingOrderCategoryResponse.class, standingOrder.standingOrderId, category))
            .from(standingOrder)
            .innerJoin(customerStandingOrder)
            .on(customerStandingOrder.standingOrder.eq(standingOrder))
            .innerJoin(customerStandingOrder.customer, customer)
            .innerJoin(customerStandingOrder.allocations, allocation)
            .innerJoin(allocation.categories, category)
            .where(condition)
            .fetch();
    return response.stream()
        .collect(Collectors.groupingBy(StandingOrderCategoryResponse::getStandingOrderId));
  }

  private JPAQuery<StandingOrderResponse> addSortToQuery(StandingOrderRequest searchRequest, JPAQuery<StandingOrderResponse> query) {
    {
      if (searchRequest.getSortByKey() != null) {
        if (searchRequest.getSortByKey().equalsIgnoreCase("status")) {
          if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
            query.orderBy(standingOrder.standingOrderStatus.desc());
          } else {
            query.orderBy(standingOrder.standingOrderStatus.asc());
          }
        } else if (searchRequest.getSortByKey().equalsIgnoreCase("standingOrderNumber")) {
          if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
            query.orderBy(standingOrder.standingOrderNumber.desc());
          } else {
            query.orderBy(standingOrder.standingOrderNumber.asc());
          }
        } else if (searchRequest.getSortByKey().equalsIgnoreCase("description")) {
          if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
            query.orderBy(standingOrder.description.desc());
          } else {
            query.orderBy(standingOrder.description.asc());
          }
        } else if (searchRequest.getSortByKey().equalsIgnoreCase("notes")) {
          if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
            query.orderBy(standingOrder.notes.desc());
          } else {
            query.orderBy(standingOrder.notes.asc());
          }
        } else if (searchRequest.getSortByKey().equalsIgnoreCase("customers")) {
          if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
            query.orderBy(standingOrderCustomerAgg.customerCode.desc());
          } else {
            query.orderBy(standingOrderCustomerAgg.customerCode.asc());
          }
        } else if (searchRequest.getSortByKey().equalsIgnoreCase("allocationCategories")) {
          if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
            query.orderBy(standingOrderAllocationCategoryAgg.allocationCategory.desc());
          } else {
            query.orderBy(standingOrderAllocationCategoryAgg.allocationCategory.asc());
          }
        }
      }
      return query;
    }
  }

  private JPAQuery<StandingOrderResponse> applyPagingAndSorting(
      JPAQuery<StandingOrderResponse> query, Pageable pageable) {
    if (Objects.nonNull(pageable)) {
      query.offset(pageable.getOffset()).limit(pageable.getPageSize());

      pageable
          .getSort()
          .forEach(
              orderBy ->
                  query.orderBy(
                      new OrderSpecifier(
                          orderBy.isAscending() ? Order.ASC : Order.DESC,
                          new PathBuilder<>(StandingOrder.class, "standingOrder").get(orderBy.getProperty()))));
    }

    return query;
  }

  @Override
  public List<StandingOrder> searchStandingOrder(StandingOrderRequest searchRequest) {
    Affirm.of(searchRequest).notNull("SearchRequest must not be null");

    return searchStandingOrderJPAQuery(searchRequest).fetch();
  }

  @Override
  public List<StandingOrderId> getStandingOrderIdsForBulkUpdate(
      StandingOrderRequest searchRequest, List<StandingOrderId> excludedStandingOrderIds) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    BooleanBuilder condition = buildCondition(searchRequest);
    if (CollectionUtils.isNotEmpty(excludedStandingOrderIds)) {
      condition.and(standingOrder.standingOrderId.notIn(excludedStandingOrderIds));
    }
    return buildBaseSearchQuery(factory)
        .select(standingOrder.standingOrderId)
        .where(condition)
        .fetch();
  }

  @Override
  public Long bulkUpdate(
      List<StandingOrderId> standingOrderIds, BulkUpdateStandingOrderRequest request) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    JPAUpdateClause update = factory.update(standingOrder);
    if (StringUtils.isNotBlank(request.getUpdatedNotes())) {
      if (BooleanUtils.toBoolean(request.getAppendNotes())) {
        update.set(
            standingOrder.notes,
            standingOrder
                .notes
                .coalesce("")
                .asString()
                .concat(String.format("\n%s", request.getUpdatedNotes())));
      } else {
        update.set(standingOrder.notes, request.getUpdatedNotes());
      }
    }
    if (StringUtils.isNotBlank(request.getUpdatedDescription())) {
      update.set(standingOrder.description, request.getUpdatedDescription());
    }
    if (request.getUpdatedStatus() != null) {
      update.set(standingOrder.standingOrderStatus, request.getUpdatedStatus());
    }
    return update.where(standingOrder.standingOrderId.in(standingOrderIds)).execute();
  }

  private JPAQuery<StandingOrder> searchStandingOrderJPAQuery(StandingOrderRequest searchRequest) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    return factory
        .selectFrom(standingOrder)
        .distinct()
        .leftJoin(customerStandingOrder)
        .on(customerStandingOrder.standingOrder.eq(standingOrder))
        .leftJoin(customerStandingOrder.customer, customer)
        .leftJoin(term)
        .on(term.standingOrder.eq(standingOrder))
        .where(buildCondition(searchRequest));
  }

  private JPAQuery<StandingOrder> applyPaginationAndSorting(
      JPAQuery<StandingOrder> query, Pageable pageRequest) {

    if (Objects.nonNull(pageRequest)) {
      PathBuilder<StandingOrder> pathBuilder =
          new PathBuilder(StandingOrder.class, "standingOrder");
      query.offset(pageRequest.getOffset()).limit(pageRequest.getPageSize());

      pageRequest
          .getSort()
          .forEach(
              order ->
                  query.orderBy(
                      new OrderSpecifier(
                          order.isAscending() ? Order.ASC : Order.DESC,
                          pathBuilder.get(order.getProperty()))));
    }

    return query;
  }

  private BooleanBuilder buildCondition(StandingOrderRequest searchRequest) {
    BooleanBuilder condition = new BooleanBuilder();

    if (Objects.nonNull(searchRequest.getCustomerId())) {
      condition.and(customer.customerId.eq(searchRequest.getCustomerId()));
    }

    if (Objects.nonNull(searchRequest.getDescription())) {
      condition.and(
          standingOrder.description.toLowerCase().like(
              Expressions.asString("%").concat(searchRequest.getDescription().toLowerCase())
                  .concat("%")));
    }

    if (Objects.nonNull(searchRequest.getStandingOrderNumber())) {
      condition.and(
          standingOrder.standingOrderNumber.like(
              Expressions.asString("%")
                  .concat(searchRequest.getStandingOrderNumber().concat("%"))));
    }

    if (Objects.nonNull(searchRequest.getStandingOrderStatus())) {
      condition.and(standingOrder.standingOrderStatus.eq(searchRequest.getStandingOrderStatus()));
    }

    if (StringUtils.isNotBlank(searchRequest.getTerm())) {
      BooleanBuilder orTermCondition =
          new BooleanBuilder(
                  term.value.toLowerCase().contains(searchRequest.getTerm().toLowerCase()))
              .or(
                  term.value
                      .toLowerCase()
                      .contains(searchRequest.getTermWithAddedOrRemovedSpaceAfterComma()));
      condition.and(term.standingOrder.isNotNull()).and(orTermCondition);
    }

    if(StringUtils.isNotBlank(searchRequest.getNotes())){
      condition.and(standingOrder.notes.toLowerCase().like(
          Expressions.asString("%").concat(searchRequest.getNotes().toLowerCase())
              .concat("%")));
    }

    if(CollectionUtils.isNotEmpty(searchRequest.getCategories())){
      condition.and(category.code.in(searchRequest.getCategories()))
          .and(allocation.status.in(AllocationStatus.PAUSED, AllocationStatus.ACTIVE));
    }

    return condition;
  }
}

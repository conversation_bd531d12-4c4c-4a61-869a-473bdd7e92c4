package au.com.peterpal.selecting.standingorders.ext.supplier.control;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.ext.supplier.boundary.dto.SupplierStatus;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.Supplier;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.SupplierId;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Log4j2
@Service
public class SupplierService {

  private final SupplierRepository supplierRepository;

  public SupplierService(SupplierRepository supplierRepository) {
    this.supplierRepository = supplierRepository;
  }

  public Supplier findById(SupplierId supplierId) {
    return supplierRepository
        .findById(supplierId)
        .orElseThrow(() -> new ResourceNotFoundException(Supplier.class, supplierId.toString()));
  }

  public List<Supplier> findAllActiveByName(String name, Pageable pageRequest) {
    return supplierRepository.findAllByNameStartsWithIgnoreCaseAndStatus(name, SupplierStatus.ACTIVE, pageRequest);
  }

  public List<Supplier> findAllActive() {
    return supplierRepository.findAllActive();
  }
}

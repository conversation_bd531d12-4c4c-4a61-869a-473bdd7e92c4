package au.com.peterpal.selecting.standingorders.admin.control;

import au.com.peterpal.selecting.standingorders.customerstandingorder.control.CustomerStandingOrderBL;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerService;
import au.com.peterpal.selecting.standingorders.profile.control.CustomerProfileBL;
import au.com.peterpal.selecting.standingorders.standingorder.control.CategoryService;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderBL;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@Log4j2
@RequiredArgsConstructor
public class Importer {

  private final StandingOrderBL standingOrderService;
  private final CustomerProfileBL profileService;
  private final CustomerStandingOrderBL csoService;
  private final CustomerService customerService;
  private final CategoryService categoryService;

  public ProcessReport importStandingOrders(MultipartFile file, boolean testFlag) {
    return StdOrderImportAgent.of(file, standingOrderService)
        .importData(testFlag)
        .getReport();
  }

  public ProcessReport importAllocationPrefs(MultipartFile file, boolean testFlag) {
    return AllocPrefImportAgent.of(file, profileService)
        .importData(testFlag)
        .getReport();
  }

  public ProcessReport importAllocations(MultipartFile file, boolean testFlag) {
    return AllocationImportAgent.of(file, csoService, standingOrderService, customerService,
            categoryService)
        .importData(testFlag)
        .getReport();
  }
}

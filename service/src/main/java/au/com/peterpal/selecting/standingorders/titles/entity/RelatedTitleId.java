package au.com.peterpal.selecting.standingorders.titles.entity;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.UUID;
import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;

@Embeddable
public class RelatedTitleId extends UuidEntityId {

  public static RelatedTitleId of(@NotEmpty UUID id) {
    return new RelatedTitleId(id);
  }

  public static RelatedTitleId of(@NotEmpty String id) {
    return new RelatedTitleId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public RelatedTitleId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public RelatedTitleId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public RelatedTitleId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

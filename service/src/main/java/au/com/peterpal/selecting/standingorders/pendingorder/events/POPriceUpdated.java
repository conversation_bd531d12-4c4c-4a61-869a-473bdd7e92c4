package au.com.peterpal.selecting.standingorders.pendingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.utils.CurrencyCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

@Getter
@ToString
@SuperBuilder
public class POPriceUpdated extends DomainEvent {

  private BigDecimal price;
  private CurrencyCode currencyCode;
}

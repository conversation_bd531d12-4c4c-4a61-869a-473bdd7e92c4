package au.com.peterpal.selecting.standingorders.ext.supplier.entity;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class SupplierId extends UuidEntityId {

  public static SupplierId of(@NotEmpty UUID id) {
    return new SupplierId(id);
  }

  public static SupplierId of(@NotEmpty String id) {
    return new SupplierId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public SupplierId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public SupplierId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public SupplierId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

package au.com.peterpal.selecting.standingorders.allocationpreference.commands;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreferenceId;
import lombok.*;

import javax.validation.constraints.NotNull;

@With
@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class RemoveReleasePreference {

  @NonNull @NotNull private ReleasePreferenceId releasePreferenceId;
}

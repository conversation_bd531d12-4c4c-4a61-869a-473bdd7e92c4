package au.com.peterpal.selecting.standingorders.customerstandingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.*;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.Branch;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

@Getter
@SuperBuilder
public class AllocationUpdated extends DomainEvent {

  private CustomerStandingOrderId customerStandingOrderId;

  private StandingOrderId standingOrderId;

  private String customerReference;

  private String deliveryInstructions;

  private String notes;

  private List<ReleaseUpdated> releases;

  private String collectionCode;

  private AllocationPreferenceId allocationPreferenceId;

  private FundId fundId;

  private List<CategoryId> categoryIds;

  private AllocationStatus status;

  private BranchId branchId;
  private boolean isActiveToPaused;
  private boolean isPausedToActive;
  private boolean isPausedToInactive;
  private boolean rematchAllocationAfterUpdate;

  @Builder
  @Getter
  static class ReleaseUpdated {
    private ReleaseId releaseId;
    private ReleaseType releaseType;
    private ActionType actionType;
    private AssignmentRule initialAssignmentRule;
    private SmallFormatPaperbackRule smallFormatPaperbackRule;
    private FundId fundId;
    private FundId hardbackfundId;
    private FundId paperbackfundId;
    private Integer quantity;
    private Integer hardbackQuantity;
    private Integer paperbackQuantity;
  }

  public static AllocationUpdated from(
      Allocation allocation,
      boolean isActiveToPaused,
      boolean isPausedToActive,
      boolean isPausedToInactive,
      boolean rematchAllocationAfterUpdate,
      String username) {
    return Optional.ofNullable(allocation)
        .map(
            a ->
                AllocationUpdated.builder()
                    .id(a.getAllocationId().getId())
                    .username(StringUtils.isBlank(username) ? "system" : username)
                    .customerStandingOrderId(
                        a.getCustomerStandingOrder().getCustomerStandingOrderId())
                    .standingOrderId(
                        a.getCustomerStandingOrder().getStandingOrder().getStandingOrderId())
                    .customerReference(a.getCustomerReference())
                    .deliveryInstructions(a.getDeliveryInstructions())
                    .notes(a.getNotes())
                    .allocationPreferenceId(
                        Optional.ofNullable(a.getAllocationPreference())
                            .map(AllocationPreference::getAllocationPreferenceId)
                            .orElse(null))
                    .fundId(Optional.ofNullable(a.getFund()).map(Fund::getFundId).orElse(null))
                    .branchId(
                        Optional.ofNullable(a.getBranch()).map(Branch::getBranchId).orElse(null))
                    .categoryIds(
                        a.getCategories().stream()
                            .map(Category::getCategoryId)
                            .collect(Collectors.toList()))
                    .releases(
                        a.getReleases().stream()
                            .map(
                                e ->
                                    ReleaseUpdated.builder()
                                        .releaseId(e.getReleaseId())
                                        .releaseType(e.getReleaseType())
                                        .actionType(e.getActionType())
                                        .initialAssignmentRule(e.getInitialAssignmentRule())
                                        .smallFormatPaperbackRule(e.getSmallFormatPaperbackRule())
                                        .fundId(
                                            Optional.ofNullable(e.getFund())
                                                .map(Fund::getFundId)
                                                .orElse(null))
                                        .hardbackfundId(
                                            Optional.ofNullable(e.getHardbackfund())
                                                .map(Fund::getFundId)
                                                .orElse(null))
                                        .paperbackfundId(
                                            Optional.ofNullable(e.getPaperbackfund())
                                                .map(Fund::getFundId)
                                                .orElse(null))
                                        .quantity(e.getQuantity())
                                        .hardbackQuantity(e.getHardbackQuantity())
                                        .paperbackQuantity(e.getPaperbackQuantity())
                                        .build())
                            .collect(Collectors.toList()))
                    .status(a.getStatus())
                    .collectionCode(a.getCollectionCode())
                    .isActiveToPaused(isActiveToPaused)
                    .isPausedToActive(isPausedToActive)
                    .isPausedToInactive(isPausedToInactive)
                    .rematchAllocationAfterUpdate(rematchAllocationAfterUpdate)
                    .build())
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    "Can not create AllocationUpdated event from null allocation"));
  }
}

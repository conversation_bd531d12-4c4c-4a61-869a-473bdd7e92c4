package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleType;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface TitleRepository extends JpaRepository<Title, TitleId>, TitleRepositoryCustom {
  Optional<Title> findByTitleAndPersonNameAndSubtitleAndTypeAndTitleStatusIn(
      String title,
      String personName,
      String subtitle,
      TitleType titleType,
      List<TitleStatus> statuses);

  List<Title> findAllByTitleStatus(TitleStatus status);
}

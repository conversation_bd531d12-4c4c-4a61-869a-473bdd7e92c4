package au.com.peterpal.selecting.standingorders.standingorder.model;

import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderCreated;
import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderUpdated;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import java.util.ArrayList;
import java.util.Optional;
import lombok.*;

import javax.persistence.*;
import java.util.List;
import org.hibernate.annotations.BatchSize;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder(toBuilder = true)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(
    name = "standing_order",
    uniqueConstraints = {
      @UniqueConstraint(
          name = "standing_order_number_idx",
          columnNames = {"standingOrderNumber"})
    })
public class StandingOrder {

  @EqualsAndHashCode.Include @NonNull @EmbeddedId private StandingOrderId standingOrderId;

  @NonNull private String standingOrderNumber;

  @NonNull
  @Enumerated(EnumType.STRING)
  private StandingOrderStatus standingOrderStatus;

  private String description;

  private String notes;

  @OneToMany(mappedBy = "standingOrder", fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true)
  @Builder.Default
  @Setter(AccessLevel.NONE)
  @JsonManagedReference
  @BatchSize(size = 10000)
  private List<Term> terms = new ArrayList<>();

  public static StandingOrder from(StandingOrderCreated event) {
    StandingOrder so = StandingOrder.builder()
        .standingOrderId(new StandingOrderId(event.getId()))
        .standingOrderNumber(event.getStandingOrderNumber())
        .standingOrderStatus(StandingOrderStatus.ACTIVE)
        .description(event.getDescription())
        .notes(event.getNotes())
        .build();
    event.getTerms().forEach(t -> so.addTerm(t));
    return so;
  }

  public static StandingOrder from(StandingOrderUpdated event) {
    StandingOrder so =  StandingOrder.builder()
        .standingOrderId(new StandingOrderId(event.getId()))
        .standingOrderNumber(event.getStandingOrderNumber())
        .standingOrderStatus(StandingOrderStatus.valueOf(event.getStatus()))
        .description(event.getDescription())
        .notes(event.getNotes())
        .build();
    event.getTerms().forEach(t -> so.addTerm(t));
    return so;
  }

  public void addTerm(Term term) {
    Optional.ofNullable(term)
        .map(t -> {
          terms.add(t);
          t.setStandingOrder(this);
          return t;
        });
  }

  public void addTerm(List<Term> terms) {
    Optional.ofNullable(terms)
        .filter(ts -> !ts.isEmpty())
        .stream()
        .flatMap(List::stream)
        .forEach(t -> addTerm(t));
  }

  public void removeTerm(Term term) {
    Optional.ofNullable(term)
        .map(t -> {
          terms.remove(t);
          t.setStandingOrder(null);
          return t;
        });
  }

  public static StandingOrder search(
      String status, String standingOrderNumber, String description) {

    StandingOrder standingOrder = new StandingOrder();

    if (status != null) {
      standingOrder.setStandingOrderStatus(StandingOrderStatus.valueOf(status));
    }

    if (standingOrderNumber != null) {
      standingOrder.setStandingOrderNumber(standingOrderNumber);
    }

    if (description != null) {
      standingOrder.setDescription(description);
    }

    return standingOrder;
  }
}

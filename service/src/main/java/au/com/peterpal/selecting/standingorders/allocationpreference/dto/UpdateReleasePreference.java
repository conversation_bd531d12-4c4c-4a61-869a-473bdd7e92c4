package au.com.peterpal.selecting.standingorders.allocationpreference.dto;

import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.UUID;

@With
@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class UpdateReleasePreference {

  @NotNull private UUID releasePreferenceId;

  @NotNull private UUID allocationPreferenceId;

  private String releaseType;

  private String actionType;

  private UUID fundId;

  private UUID hardbackFundId;

  private UUID paperbackFundId;

  private String initialAssignmentRule;

  private String smallFormatPaperbackRule;

  @Builder.Default private Integer totalQuantity = 0;

  @Builder.Default private Integer hardbackQuantity = 0;

  @Builder.Default private Integer paperbackQuantity = 0;
}

package au.com.peterpal.selecting.standingorders.allocation.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.*;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.Optional;
import java.util.UUID;

@With
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AllocationSearchByStandingOrderResponse {

  @NonNull @NotNull private AllocationId allocationId;
  @NonNull @NotNull private UUID releaseId;
  private String customerCode;
  private List<String> categories;
  private AllocationStatus status;
  private ReleaseType releaseType;
  private ActionType actionType;

  private String fundCode;
  private String fundName;
  private Integer fundQuantity;

  private String hbFundCode;
  private String hbFundName;
  private Integer hbQuantity;

  private String pbFundCode;
  private String pbFundName;
  private Integer pbQuantity;

  private String customerReference;
  private String deliveryInstructions;
  private String notes;
  private AssignmentRule assignmentRule;
  private SmallFormatPaperbackRule smallFormatPaperbackRule;

  public static AllocationSearchByStandingOrderResponse from(Release release) {
    Affirm.of(release).notNull("Release must not be null");
    Affirm.of(release.getAllocation()).notNull("Allocation must not be null");
    Affirm.of(release.getAllocation().getCustomerStandingOrder())
        .notNull("CustomerStandingOrder must not be null");
    Affirm.of(release.getAllocation().getCustomerStandingOrder().getCustomer())
        .notNull("Customer must not be null");

    Allocation allocation = release.getAllocation();
    CustomerStandingOrder customerStandingOrder = allocation.getCustomerStandingOrder();
    Customer customer = customerStandingOrder.getCustomer();

    return AllocationSearchByStandingOrderResponse.builder()
        .allocationId(allocation.getAllocationId())
        .releaseId(release.getReleaseId().getId())
        .customerCode(customer.getCode())
        .categories(allocation.getCategoryCodes())
        .status(allocation.getStatus())
        .releaseType(release.getReleaseType())
        .actionType(release.getActionType())
        .fundCode(Optional.ofNullable(release.getFund()).map(Fund::getCode).orElse(null))
        .fundName(Optional.ofNullable(release.getFund()).map(Fund::getName).orElse(null))
        .fundQuantity(release.getQuantity())
        .hbFundCode(Optional.ofNullable(release.getHardbackfund()).map(Fund::getCode).orElse(null))
        .hbFundName(Optional.ofNullable(release.getHardbackfund()).map(Fund::getName).orElse(null))
        .hbQuantity(release.getHardbackQuantity())
        .pbFundCode(Optional.ofNullable(release.getPaperbackfund()).map(Fund::getCode).orElse(null))
        .pbFundName(Optional.ofNullable(release.getPaperbackfund()).map(Fund::getName).orElse(null))
        .pbQuantity(release.getPaperbackQuantity())
        .customerReference(allocation.getCustomerReference())
        .deliveryInstructions(allocation.getDeliveryInstructions())
        .notes(allocation.getNotes())
        .assignmentRule(release.getInitialAssignmentRule())
        .smallFormatPaperbackRule(release.getSmallFormatPaperbackRule())
        .build();
  }
}

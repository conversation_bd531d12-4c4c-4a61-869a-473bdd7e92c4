package au.com.peterpal.selecting.standingorders.gateways.clientweb.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Value;

import java.util.List;

@Value
@NoArgsConstructor(force = true)
@AllArgsConstructor
@Builder
public class ProductData {

    private List<CwProductInfo> content;

    private Integer pageSize;
    private Integer offset;
    private Integer totalElement;
}

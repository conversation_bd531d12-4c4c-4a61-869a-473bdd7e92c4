package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.titles.dto.TitleSearchRequest;
import au.com.peterpal.selecting.standingorders.titles.dto.TitleSearchResponse;
import au.com.peterpal.selecting.standingorders.titles.dto.TitleStandingOrderAllocationCategory;
import au.com.peterpal.selecting.standingorders.titles.entity.*;

import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.data.domain.Page;

public interface TitleRepositoryCustom {
  TitleSearchResponse search(TitleSearchRequest request);

  Page<Title> searchTitle(TitleSearchRequest request);

  List<Title> findTitleContainingIsbn(String isbn, TitleType type, MatchedProductStatus productStatus);

  List<Title> findAllTitleByIsbnAndStandingOrderId(String isbn, StandingOrderId standingOrderId);

  List<Title> findTitlesByStatusAndTypeAndAllocationId(
      List<TitleStatus> statuses, List<TitleType> types, AllocationId allocationId, boolean acceptedStandingOrderRequired);

  Map<StandingOrderId, TitleStandingOrderAllocationCategory> findStandingOrderCategories(Set<TitleId> titleIds);

  List<Title> searchTitle(TitleSearchRequest searchRequest, List<TitleId> excludedTitleIds);

  List<Title> findTitleStillOnOrderByStandingOrderIdAndType(
      StandingOrderId standingOrderId, TitleType original);
}

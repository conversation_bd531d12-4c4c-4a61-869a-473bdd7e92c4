package au.com.peterpal.selecting.standingorders.allocationpreference.dto;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import lombok.*;

import javax.validation.constraints.NotNull;

@With
@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class RemoveAllocationPreference {

  @NotNull private AllocationPreferenceId allocationPreferenceId;
}

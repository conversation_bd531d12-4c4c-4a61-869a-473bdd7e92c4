package au.com.peterpal.selecting.standingorders.catalog.model;

public enum PublishingRoleCode {

  PUB ("01", "Publisher", ""),
  COPUB ("02", "Co-publisher", ""),
  SPONS ("03", "Sponsor", ""),
  PUBOLV ("04", "Publisher of original-language version", "Of a translated work"),
  HOST ("05", "Host/distributor of electronic content", ""),
  PUBFOR ("06", "Published for/on behalf of", ""),
  PUBASS ("07", "Published in association with", "Use also for “Published in cooperation with”"),
  PUBFOROLD ("08", "Published on behalf of", "DEPRECATED : use code 06"),
  PUBNEW ("09", "New or acquiring publisher", "When ownership of a product or title is transferred from one publisher to another");

  private final String code;
  private final String description;
  private final String notes;

  PublishingRoleCode(String code, String description, String notes) {
    this.code = code;
    this.description = description;
    this.notes = notes;
  }

  public String getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public String getNotes() {
    return notes;
  }

  public static PublishingRoleCode fromCode(String code) {
    for (PublishingRoleCode value : PublishingRoleCode.values()) {
      if (value.code.equals(code)) {
        return value;
      }
    }
    throw new IllegalArgumentException("Invalid " + PublishingRoleCode.class.getSimpleName() + ": " + code);
  }

  @Override
  public String toString() {
    return this.description;
  }
}

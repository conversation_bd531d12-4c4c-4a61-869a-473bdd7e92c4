package au.com.peterpal.selecting.standingorders.customerstandingorder.control;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreferenceId;
import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ReleaseRepository extends JpaRepository<Release, ReleaseId> {
  Release findByAllocation_AllocationIdAndPreference_ReleasePreferenceId(
      AllocationId allocationId, ReleasePreferenceId releasePreferenceId);
}

package au.com.peterpal.selecting.standingorders.standingorder.model;

import au.com.peterpal.selecting.standingorders.utils.StringAffirm;
import au.com.peterpal.selecting.standingorders.utils.StringHelper;
import com.fasterxml.jackson.annotation.JsonBackReference;
import java.util.UUID;
import javax.persistence.*;
import lombok.*;
import lombok.extern.log4j.Log4j2;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@Builder
@Entity
@Log4j2
@Table(name = "term")
public class Term {

  @EqualsAndHashCode.Include @NonNull @EmbeddedId private TermId termId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JsonBackReference
  private StandingOrder standingOrder;

  @Enumerated(EnumType.STRING)
  private TermType type;

  @Enumerated(EnumType.STRING)
  @NonNull
  private OperationType operation;

  private String value;

  // New association to CombinedTerm entity
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "combined_term_id")
  @JsonBackReference
  private CombinedTerm combinedTerm;

  public static final Term DEFAULT =
      Term.builder()
          .termId(TermId.of(UUID.randomUUID()))
          .operation(OperationType.EXACT_MATCH)
          .build();

  public static Term from(
      String str, TermType type, OperationType operation, CombinedTermId combinedTermId) {
    Term term = null;
    if (!StringAffirm.of(str).hasText() || str.equals("%")) {
      return term;
    }

    term =
        Term.builder()
            .termId(TermId.of(UUID.randomUUID()))
            .combinedTerm(CombinedTerm.builder().combinedTermId(combinedTermId).build())
            .type(type)
            .operation(operation)
            .value(StringHelper.of(str).trim())
            .build();

    if (!str.contains("100%") && str.contains("%")) {
      // derive the operation from %
      StringBuilder sb = new StringBuilder(str);
      term.setOperation(OperationType.of(sb));
      term.setValue(sb.toString().replace("%", ""));
    }

    return term;
  }
}

package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.ClientWebApiGateway;
import au.com.peterpal.selecting.standingorders.products.control.ProductRepository;
import au.com.peterpal.selecting.standingorders.standingorder.control.MatchingService;
import au.com.peterpal.selecting.standingorders.standingorder.events.MatchFound;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductMatchInfo;
import au.com.peterpal.selecting.standingorders.titles.dto.RematchResult;
import au.com.peterpal.selecting.standingorders.titles.dto.RematchStandingOrderProductRequest;
import au.com.peterpal.selecting.standingorders.titles.dto.TitleSearchRequest;
import au.com.peterpal.selecting.standingorders.titles.entity.*;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import static java.util.Objects.isNull;

@Log4j2
@Service
@RequiredArgsConstructor
public class ProductMatchingBL {
  private static final int SIZE_OF_REMATCH_TITLE = 1000;
  private final MatchingService matchingService;
  private final RematchTitleService rematchTitleService;
  private final TitleService titleService;
  private final ProductRepository productRepository;
  private final ClientWebApiGateway clientWebApiGateway;
  private final MatchAndMergeTitleService matchAndMergeTitleService;

  @Value("${process-product-update-after.date}")
  String processProductUpdateLimitDateValue;

  @Value("${process-product-update-after.date.format:dd/MM/yyyy}")
  String processProductUpdateLimitDateFormat;

  public void matchCatalogueProductAdd(ProductMatchInfo product) {
    matchingService.matchAndHandle(product);
  }

  public void matchCatalogueProductUpdate(ProductMatchInfo product, String username) {
    if (skipProductUpdateProcessing(product)) {
      log.trace("Ignore process product update {}", product.getProductReference());
      return;
    }

    Optional<Title> existingTitle =
        titleService.findNonProcessedOriginalTitleByIsbn(product.getProductReference());
    if (existingTitle.isPresent()) {
      Title title = existingTitle.get();
      if (!title.isProcessed()) {
        // do rematch
        RematchResult result =
            rematchTitleService.rematchTitleForProductUpdate(title.getTitleId(), product, username);
        log.debug("Rematched by product update {}: {}", product.getProductReference(), result);
      } else {
        log.trace(
            "Product update ignored, found existing title ({}) with status {} for isbn {}",
            title.getTitleId(),
            title.getTitleStatus(),
            product.getProductReference());
      }
    } else {
      // send to normal product processing
      matchingService.matchAndHandle(product);
    }
  }

  public void rematchTitle(TitleId titleId, String username) {
    Title title = validate(titleId);
    RematchResult result = rematchTitleService.rematchTitle(title.getTitleId(), username);
    log.debug("Title {} rematched: {}", titleId, result);
  }

  public void rematchTitlesBySearchCategory(
      TitleStatus status, List<String> categories, String text, String username) {
    LocalDateTime startTime = LocalDateTime.now();
    Pageable pageable = PageRequest.of(0, SIZE_OF_REMATCH_TITLE, Sort.by("id").ascending());
    List<TitleStatus> statuses =
        Optional.ofNullable(status)
            .map(List::of)
            .orElse(List.of(TitleStatus.NEW, TitleStatus.PENDING, TitleStatus.DEFERRED));

    Page<Title> titlePage =
        titleService.searchTitle(
            TitleSearchRequest.builder()
                .statuses(statuses)
                .categories(categories)
                .pageRequest(pageable)
                .text(text)
                .build());

    while (titlePage.hasContent()) {
      List<Title> titles = titlePage.getContent();
      log.trace("Performing re-match-all for {} tiles", titles.size());
      titles.forEach(
          title -> {
            try {
              rematchTitleService.rematchTitle(title.getTitleId(), username);
            } catch (Exception e) {
              log.error("Cannot re-match for title id {}", title.getTitleId());
            }
          });
      log.trace("re-match-all for {} titles done", titles.size());

      pageable = pageable.next();
      titlePage =
          titleService.searchTitle(
              TitleSearchRequest.builder()
                  .statuses(statuses)
                  .categories(categories)
                  .pageRequest(pageable)
                  .text(text)
                  .build());
    }
    log.trace(
        "Completed re-match-all titles with status: {}, categories: {}, text {} in duration {} seconds",
        status,
        categories,
        text,
        Duration.between(startTime, LocalDateTime.now()).getSeconds());
  }

  public void rematchStandingOrder(RematchStandingOrderProductRequest request, String username) {
    ProductMatchInfo product =
        clientWebApiGateway.getProductInfoByIsbn(request.getProductReference());
    Optional<MatchFound> matchFound =
        matchingService.matchSingleStandingOrder(product, request.getStandingOrderId());

    if (matchFound.isPresent()) {
      List<Title> existingTitleMatches =
          titleService.findAllTitleByIsbnAndStandingOrderId(
              product.getProductReference(), request.getStandingOrderId());

      // Do not create titles if product & SO has previously been matched
      if (CollectionUtils.isNotEmpty(existingTitleMatches)) {
        existingTitleMatches.stream()
            .filter(Title::isNotProcessedOrRejected)
            .forEach(
                title -> {
                  RematchResult result =
                      rematchTitleService.rematchTitleForStandingOrder(
                          request.getStandingOrderId(),
                          matchFound.get().getMatchedTermId(),
                          title.getTitleId(),
                          product,
                          username);
                  log.debug(
                      "Rematched standing order {}: {}", request.getStandingOrderId(), result);
                });

      } else {
        // no previously been matched, publish event and let MatchAndMerge handle the event
        matchAndMergeTitleService.handle(matchFound.get());
      }
    }
  }

  Title validate(TitleId titleId) {
    Title title = titleService.findTitleById(titleId);
    if (title.getTitleStatus() == TitleStatus.PROCESSED
        || title.getTitleStatus() == TitleStatus.REJECTED) {
      throw new BusinessException("Rematch title can only be done for NEW or PENDING title");
    }

    return title;
  }

  boolean skipProductUpdateProcessing(ProductMatchInfo product) {
    LocalDate dateLimit =
        StringUtils.isNotEmpty(processProductUpdateLimitDateValue)
            ? LocalDate.parse(
                    processProductUpdateLimitDateValue,
                    DateTimeFormatter.ofPattern(processProductUpdateLimitDateFormat))
                .atStartOfDay()
                .toLocalDate()
            : null;

    boolean skipProduct;
    if (isNull(dateLimit)
        || (product.isInitialImportDateAfter(dateLimit))
        || (product.isInitialImportDateBefore(dateLimit)
            && productRepository.existsByProductReference(product.getProductReference()))) {
      skipProduct = false;
    } else {
      skipProduct = true;
    }

    return skipProduct;
  }
}

package au.com.peterpal.selecting.standingorders.pendingorder.dto;

import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import io.swagger.annotations.ApiModel;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.*;

@With
@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
@ApiModel
public class BulkUpdatePendingOrderRequest {

  @NotEmpty private List<PendingOrderId> pendingOrderId;

  private String customerReference;

  private String category;

  private String collectionCode;

  private String deliveryInstructions;

  private String notes;

  private FundId fundId;

  private Integer quantity;
}

package au.com.peterpal.selecting.standingorders.allocation.model;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class AllocationId extends UuidEntityId {

  public static AllocationId of(@NotEmpty UUID id) {
    return new AllocationId(id);
  }

  public static AllocationId of(@NotEmpty String id) {
    return new AllocationId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public AllocationId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public AllocationId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public AllocationId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

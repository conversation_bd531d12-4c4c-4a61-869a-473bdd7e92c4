package au.com.peterpal.selecting.standingorders.ext.category.control;

import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryStatus;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CategoryRepository extends JpaRepository<Category, CategoryId> {

  List<Category> findAllByStatusOrderByCode(CategoryStatus status);

  Category findByCode(String code);

  List<Category> findByCodeIn(List<String> codes);
}

package au.com.peterpal.selecting.standingorders.customerstandingorder.dto;

import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.UUID;

@With
@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class CreateCustomerStandingOrder {

  @NotNull
  private UUID customerId;

  @NotNull
  private UUID standingOrderId;

}

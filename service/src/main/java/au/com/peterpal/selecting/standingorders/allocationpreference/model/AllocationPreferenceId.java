package au.com.peterpal.selecting.standingorders.allocationpreference.model;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class AllocationPreferenceId extends UuidEntityId {

  public static AllocationPreferenceId of(@NotEmpty UUID id) {
    return new AllocationPreferenceId(id);
  }

  public static AllocationPreferenceId of(@NotEmpty String id) {
    return new AllocationPreferenceId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public AllocationPreferenceId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public AllocationPreferenceId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public AllocationPreferenceId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

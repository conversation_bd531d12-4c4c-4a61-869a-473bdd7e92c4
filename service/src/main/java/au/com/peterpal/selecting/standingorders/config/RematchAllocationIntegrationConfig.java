package au.com.peterpal.selecting.standingorders.config;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.titles.control.RematchAllocationService;
import javax.jms.ConnectionFactory;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.context.IntegrationContextUtils;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.dsl.channel.MessageChannels;
import org.springframework.integration.jms.dsl.Jms;
import org.springframework.messaging.MessageChannel;

@Log4j2
@Configuration
public class RematchAllocationIntegrationConfig {
  private static final String NEW_REMATCH_ALLOCATIOn_ORDER_MSG =
      "Received new allocation rematch message -- Payload: %s Headers: %s";

  @Value("${rematch.allocation.queue.name:rematch-allocation}")
  private String rematchAllocation;

  private final ConnectionFactory connectionFactory;
  private final RematchAllocationService rematchAllocationService;

  public RematchAllocationIntegrationConfig(
      ConnectionFactory connectionFactory,
      RematchAllocationService rematchAllocationService) {
    this.connectionFactory = connectionFactory;
    this.rematchAllocationService = rematchAllocationService;
  }

  @Bean("rematchAllocationOutboundChannel")
  public MessageChannel rematchAllocationOutboundChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow rematchAllocationOutboundFlow() {
    return IntegrationFlows.from(rematchAllocationOutboundChannel())
        .transform(Transformers.toJson())
        .handle(Jms.outboundAdapter(connectionFactory).destination(rematchAllocation))
        .get();
  }

  @Bean
  public IntegrationFlow rematchAllocationOutboundChannelInboundFlow() {
    return IntegrationFlows.from(
            Jms.messageDrivenChannelAdapter(this.connectionFactory)
                .destination(rematchAllocation)
                .errorChannel(IntegrationContextUtils.ERROR_CHANNEL_BEAN_NAME))
        .log(
            msg ->
                String.format(
                    "Received rematch Allocation message -- Payload: %s Headers: %s",
                    msg.getPayload(), msg.getHeaders()))
        .log(
            msg ->
                String.format(NEW_REMATCH_ALLOCATIOn_ORDER_MSG, msg.getPayload(), msg.getHeaders()))
        .transform(Transformers.fromJson(AllocationId.class))
        .<AllocationId>handle(
            ((payload, headers) -> {
              rematchAllocationService.rematch(payload, "system");
              return null;
            }))
        .get();
  }
}

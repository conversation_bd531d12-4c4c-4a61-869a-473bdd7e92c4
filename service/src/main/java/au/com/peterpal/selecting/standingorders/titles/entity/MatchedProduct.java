package au.com.peterpal.selecting.standingorders.titles.entity;

import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.Format;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductChangeListener;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductMatchInfo;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder(toBuilder = true)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "matched_product")
public class MatchedProduct {

  @EqualsAndHashCode.Include @NonNull @EmbeddedId private MatchedProductId matchedProductId;

  @ManyToOne
  @JoinColumn(name = "titleId", nullable = false)
  private Title title;

  @ManyToOne
  @JoinColumn(name = "category_id")
  private Category category;

  @Enumerated(EnumType.STRING)
  private Format format;

  // product catalogue info
  private Integer catalogueId;
  private String productTitle;
  private String subtitle;
  private String series;
  private String isbn;
  private String personName;
  private String imprint;
  private LocalDate publicationDate;

  // end of product catalogue info

  @OneToMany(mappedBy = "matchedProduct", cascade = CascadeType.ALL, orphanRemoval = true)
  @JsonIgnore
  @Builder.Default
  private List<MatchedStandingOrder> matchedStandingOrders = new ArrayList<>();

  @Builder.Default
  @Enumerated(EnumType.STRING)
  private MatchedProductStatus status = MatchedProductStatus.ACTIVE;

  @JsonIgnore
  public List<MatchedStandingOrder> getActiveMatchedStandingOrder() {
    return getMatchedStandingOrders().stream()
        .filter(MatchedStandingOrder::isActive)
        .collect(Collectors.toList());
  }

  @JsonIgnore
  public boolean noMatchStandingOrders() {
    return CollectionUtils.isEmpty(getActiveMatchedStandingOrder());
  }

  public void addMatchedStandingOrder(MatchedStandingOrder matchedStandingOrder) {
    matchedStandingOrders.add(matchedStandingOrder);
  }

  public Optional<MatchedStandingOrder> findMatchedStandingOrderByStandingOrderId(
      StandingOrderId standingOrderId) {
    return this.getMatchedStandingOrders().stream()
        .filter(som -> som.getStandingOrder().getStandingOrderId().equals(standingOrderId))
        .findAny();
  }

  public boolean isStandingOrderExist(StandingOrderId standingOrderId) {
    return findMatchedStandingOrderByStandingOrderId(standingOrderId).isPresent();
  }

  public static MatchedProduct copy(MatchedProduct originalMatchedProduct, Title title) {
    return MatchedProduct.builder()
        .matchedProductId(new MatchedProductId())
        .title(title)
        .category(originalMatchedProduct.getCategory())
        .format(originalMatchedProduct.getFormat())
        .catalogueId(originalMatchedProduct.getCatalogueId())
        .productTitle(originalMatchedProduct.getProductTitle())
        .subtitle(originalMatchedProduct.getSubtitle())
        .series(originalMatchedProduct.getSeries())
        .isbn(originalMatchedProduct.getIsbn())
        .personName(originalMatchedProduct.getPersonName())
        .imprint(originalMatchedProduct.getImprint())
        .publicationDate(originalMatchedProduct.getPublicationDate())
        .build();
  }

  @JsonIgnore
  public boolean isActive() {
    return status == MatchedProductStatus.ACTIVE;
  }

  @JsonIgnore
  public boolean isInactive() {
    return status == MatchedProductStatus.INACTIVE;
  }

  public void updateWithCatalogueInfo(ProductMatchInfo info) {
    String personName =
        info.getPersonName(
            ProductChangeListener.VALID_CONTRIBUTOR_CODES, ProductChangeListener.NO_AUTHOR);
    String series =
        Optional.ofNullable(info.getSeriesTitle())
            .map(ProductMatchInfo.TitleInfo::getText)
            .orElse(null);
    String productTitle =
        Optional.ofNullable(info.getTitle())
            .map(ProductMatchInfo.TitleInfo::getWithoutPrefix)
            .orElse(null);
    String subtitle =
        Optional.ofNullable(info.getTitle())
            .map(ProductMatchInfo.TitleInfo::getSubtitle)
            .orElse(null);

    String imprint = info.getImprint();
    LocalDate publicationDate = info.getPublicationDate();

    setProductTitle(productTitle);
    setSubtitle(subtitle);
    setPersonName(personName);
    setSeries(series);
    setImprint(imprint);
    setPublicationDate(publicationDate);
  }

  @JsonIgnore
  public boolean shouldBeDeferred(LocalDate deferDateLimit) {
    return Optional.ofNullable(getPublicationDate())
        .map(pd -> pd.isAfter(deferDateLimit))
        .orElse(false);
  }

  @JsonIgnore
  public LocalDate calculateDeferredDate(Integer deferredMinusMonth) {
    return Optional.ofNullable(getPublicationDate())
        .map(LocalDate::atStartOfDay)
        .map(d -> d.minusMonths(deferredMinusMonth).toLocalDate())
        .orElse(null);
  }
}

package au.com.peterpal.selecting.standingorders.standingorder.model;

import au.com.peterpal.selecting.standingorders.standingorder.dto.StandingOrderUpdatedMessage;
import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderUpdated;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class SORematchSummary implements SOSummary {

  private UUID id;
  private String number;
  private List<Term> terms;

  public static SOSummary from(StandingOrder standingOrder) {
    return SORematchSummary.builder()
        .id(standingOrder.getStandingOrderId().getId())
        .number(standingOrder.getStandingOrderNumber())
        .terms(standingOrder.getTerms())
        .build();
  }

  public static SOSummary from(StandingOrderUpdated standingOrder) {
    return SORematchSummary.builder()
        .id(standingOrder.getId())
        .number(standingOrder.getStandingOrderNumber())
        .terms(standingOrder.getTerms())
        .build();
  }
  public static SOSummary from(final StandingOrderUpdatedMessage standingOrderUpdatedMessage) {
    return SORematchSummary.builder()
        .id(standingOrderUpdatedMessage.getId())
        .number(standingOrderUpdatedMessage.getStandingOrderNumber())
        .terms(standingOrderUpdatedMessage.toTerms())
        .build();
  }

  @Override
  public UUID getId() {
    return id;
  }

  @Override
  public String getNumber() {
    return number;
  }

  @Override
  public List<Term> getTerms() {
    return terms;
  }
}

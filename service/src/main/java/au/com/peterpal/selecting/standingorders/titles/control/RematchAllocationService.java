package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationRepository;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.customerstandingorder.events.AllocationRematched;
import au.com.peterpal.selecting.standingorders.ext.order.boundary.dto.OrderStatus;
import au.com.peterpal.selecting.standingorders.ext.order.control.OrderRepository;
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderBL;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.titles.dto.CreateRelatedTitleByStandingOrder;
import au.com.peterpal.selecting.standingorders.titles.dto.RematchAllocationResult;
import au.com.peterpal.selecting.standingorders.titles.entity.*;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@RequiredArgsConstructor
@Log4j2
@Service
public class RematchAllocationService {
  private final AllocationRepository allocationRepository;
  private final TitleService titleService;
  private final RelatedTitleBL relatedTitleBL;
  private final RelatedTitleRepository relatedTitleRepository;
  private final PendingOrderBL pendingOrderBL;
  private final EventPublisher eventPublisher;
  private final OrderRepository orderRepository;
  @Transactional
  public void rematch(AllocationId allocationId, String username) {
    Allocation allocation =
        allocationRepository
            .findById(allocationId)
            .orElseThrow(
                () ->
                    new ResourceNotFoundException(Allocation.class, String.valueOf(allocationId)));
    List<Title> titles =
        titleService.findOriginalTitleStillOnOrderByStandingOrderId(
            allocation.getCustomerStandingOrder().getStandingOrder().getStandingOrderId());
    log.debug("Rematching {} titles with allocation {}", titles.size(), allocationId);

    List<Title> targetTitles =
        titles.stream()
            .filter(title -> titleHasMatchedCategory(title, allocation))
            .filter(title -> !isTitleHasAnyInvoicedOrder(title, allocation))
            .collect(Collectors.toList());
    List<RematchAllocationResult> rematchAllocationResults =
        targetTitles.stream()
            .map(title -> rematch(title, allocation, username))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

    if (CollectionUtils.isNotEmpty(rematchAllocationResults)) {
      eventPublisher.publishEvent(
          AllocationRematched.from(allocation, rematchAllocationResults, username));
    }
  }

  boolean titleHasMatchedCategory(Title title, Allocation allocation) {
    return allocation.containsCategory(title.getCategory());
  }

  boolean isTitleHasAnyInvoicedOrder(Title title, Allocation allocation) {
    List<String> orderNumbers =
        pendingOrderBL
            .findAllPendingOrderByTitleIdAllocationId(title.getTitleId(), allocation.getAllocationId())
            .stream()
            .map(PendingOrder::getOrderNumber)
            .collect(Collectors.toList());

    return orderRepository.findAllByOrderNumberIn(orderNumbers).stream()
            .anyMatch(order -> order.getStatus() == OrderStatus.INVOICED);
  }

  public RematchAllocationResult rematch(
      Title originalTitle, Allocation allocation, String username) {
    RematchAllocationResult result = null;
    if (originalTitle.isPending()) {
      // do not create new related title
      List<PendingOrder> pendingOrdersUpdated =
          pendingOrderBL.updatePendingOrder(originalTitle, allocation);
      result = RematchAllocationResult.from(originalTitle, null, pendingOrdersUpdated);

    } else {
      List<Title> relatedTitles =
          relatedTitleRepository.findAllByOriginalTitleTitleId(originalTitle.getTitleId()).stream()
              .map(RelatedTitle::getRelatedTitle)
              .filter(t -> t.isPending() || t.isProcessed())
              .collect(Collectors.toList());

      Title rematchedTitle;
      if (CollectionUtils.isEmpty(relatedTitles)) {
        rematchedTitle =
            relatedTitleBL.handle(
                CreateRelatedTitleByStandingOrder.from(
                    originalTitle.getTitleId(),
                    allocation.getCustomerStandingOrder().getStandingOrder().getStandingOrderId()),
                username);
      } else {
        Optional<Title> pendingRelated = relatedTitles.stream().filter(Title::isPending).findAny();
        rematchedTitle =
            pendingRelated.orElseGet(
                () ->
                    relatedTitleBL.handle(
                        CreateRelatedTitleByStandingOrder.from(
                            originalTitle.getTitleId(),
                            allocation
                                .getCustomerStandingOrder()
                                .getStandingOrder()
                                .getStandingOrderId()),
                        username));
      }

      List<TitleId> sourceTitleIds =
          relatedTitles.stream().map(Title::getTitleId).collect(Collectors.toList());
      sourceTitleIds.add(originalTitle.getTitleId());

      List<PendingOrder> pendingOrdersCreated =
          pendingOrderBL.copyOrCreateAdditionalPendingOrder(
              rematchedTitle, sourceTitleIds, allocation);
      if (CollectionUtils.isNotEmpty(
          pendingOrderBL.findAllNewPendingOrderByTitleIdAllocationId(
              rematchedTitle.getTitleId(), allocation.getAllocationId()))) {
        result = RematchAllocationResult.from(rematchedTitle, pendingOrdersCreated, null);
      } else {
        relatedTitleBL.deleteRelatedTitle(rematchedTitle);
      }
    }

    log.debug("rematch result {}", result);
    return result;
  }
}

package au.com.peterpal.selecting.standingorders.titles.dto;

import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.util.List;

@Data
@Builder
@With
@AllArgsConstructor(staticName = "of")
@EqualsAndHashCode
public class TitleStandingOrderAllocationCategory {
  @JsonIgnore
  private TitleId titleId;
  private StandingOrderId standingOrderId;
  private List<Category> categories;
}

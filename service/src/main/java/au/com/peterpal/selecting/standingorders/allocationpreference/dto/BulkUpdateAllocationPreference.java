package au.com.peterpal.selecting.standingorders.allocationpreference.dto;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceStatus;
import java.util.List;
import lombok.*;

import javax.validation.constraints.NotEmpty;

@With
@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class BulkUpdateAllocationPreference {

  @NonNull @NotEmpty
  List<AllocationPreferenceId> includedAllocationPreferenceIds;

  List<String> updatedCategory;

  String updatedCustomerReference;

  String updatedDeliveryInstructions;

  String updatedNotes;

  String updatedFundCode;

  AllocationPreferenceStatus updatedAllocationPreferenceStatus;
}

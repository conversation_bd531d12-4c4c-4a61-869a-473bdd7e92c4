package au.com.peterpal.selecting.standingorders.standingorder.control;

import au.com.peterpal.selecting.standingorders.standingorder.model.CategoryInfo;
import au.com.peterpal.selecting.standingorders.standingorder.model.CategoryMapping;
import au.com.peterpal.selecting.standingorders.utils.StringAffirm;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class SubjectMatcher {

  private final Map<Character, List<CategoryMapping>> map = new HashMap<>();

  /**
   * Create a subject matcher given a list of category mappings.
   * <p>
   * For each  subject in the category mapping list get the first character. Find the list of
   * category mappings corresponding to the character in the map or create a new empty list. Add
   * the category mapping to the list.
   *
   * @param mapping list of category mappings
   * @return a new subject matcher
   */
  public static SubjectMatcher of(List<CategoryMapping> mapping) {
    SubjectMatcher matcher = new SubjectMatcher();
    if (mapping != null) {
      mapping.forEach(
          m -> {
            Character key = m.getSubject().charAt(0);
            List<CategoryMapping> l = matcher.map.computeIfAbsent(key, k -> new ArrayList<>());
            l.add(m);
          });
    }
    return matcher;
  }

  private SubjectMatcher() {
  }

  /**
   * Find a category and priority for given subject string.
   * <p>
   * Get the first character of the subject.
   * Search for a mapping in the map.
   * If mapping is found and there is one mapping then return a category info
   * If more than one mapping found: find first mapping that subject are the same.
   * If not found subject are the same then recursive this method with subject without latest character
   * @param subject the subject string
   * @return a category and priority, or null if map is empty or subject is null or empty.
   */
  public CategoryInfo findMatchingCategory(String subject) {
    CategoryInfo catInfo = null;
    if (!map.isEmpty() && StringAffirm.of(subject).hasText()) {
      Character key = subject.charAt(0);
      catInfo = Optional.ofNullable(map.get(key))
          .map(categoryMappings -> {
            CategoryInfo match;
            if (categoryMappings.size() == 1) {
              CategoryMapping catMap = categoryMappings.get(0);
              match = CategoryInfo.of(catMap.getCategory(), catMap.getPriority());
            } else {
              match = categoryMappings.stream()
                  .filter(cm -> StringUtils.equals(subject,cm.getSubject()))
                  .map(cm -> CategoryInfo.of(cm.getCategory(), cm.getPriority()))
                  .findAny()
                  .orElseGet(() -> findMatchingCategory(
                      StringUtils.substring(subject, 0, subject.length() - 1)));
            }
            return match;
          })
          .orElse(null);
    }
    return catInfo;
  }
    /**
     * Find a category and priority for given subject string.
     * <p>
     * Get the first character of the subject.
     * Search for a mapping in the map.
     * If mapping is found and there is one mapping then return a category info
     * If more than one mapping found: find first mapping that subject length is >=
     *
     * @param subject the subject string
     * @return a category and priority, or null if map is empty or subject is null or empty.
     */
    public CategoryInfo match(String subject) {
        CategoryInfo catInfo = null;
        if (!map.isEmpty() && StringAffirm.of(subject).hasText()) {
            Character key = subject.charAt(0);
            catInfo = Optional.ofNullable(map.get(key))
                .map(categoryMappings -> {
                    CategoryInfo match;
                    if (categoryMappings.size() == 1) {
                        CategoryMapping catMap = categoryMappings.get(0);
                        match = CategoryInfo.of(catMap.getCategory(), catMap.getPriority());
                    } else {
                        match = categoryMappings.stream()
                            .filter(cm -> StringUtils.equals(subject,cm.getSubject()))
                            .map(cm -> CategoryInfo.of(cm.getCategory(), cm.getPriority()))
                            .findAny()
                            .orElseGet(() -> getCategoryStartsWithSubject(categoryMappings, subject));
                    }
                    return match;
                })
                .orElse(null);
        }
        return catInfo;
    }

    private CategoryInfo getCategoryStartsWithSubject(List<CategoryMapping> categoryMappings, String subject) {
        return categoryMappings.stream()
            .filter(cm -> subject.length() >= cm.getSubject().length() && subject.startsWith(cm.getSubject()))
            .min(Comparator.comparingInt(CategoryMapping::getPriority))
            .map(cm -> CategoryInfo.of(cm.getCategory(), cm.getPriority()))
            .orElse(null);
    }
}

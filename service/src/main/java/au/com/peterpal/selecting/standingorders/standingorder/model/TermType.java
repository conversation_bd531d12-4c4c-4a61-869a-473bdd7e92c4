package au.com.peterpal.selecting.standingorders.standingorder.model;

import lombok.Getter;

@Getter
public enum TermType {
  PERSON_NAME("PERSON_NAME", "Person Name"),
  SERIES_TITLE("SERIES_TITLE", "Series Title"),
  IMPRINT_NAME("IMPRINT_NAME", "Imprint Name"),
  TITLE("TITLE", "Title");

  private final String description;
  private final String code;

  TermType(String code, String description) {
    this.code = code;
    this.description = description;
  }
}

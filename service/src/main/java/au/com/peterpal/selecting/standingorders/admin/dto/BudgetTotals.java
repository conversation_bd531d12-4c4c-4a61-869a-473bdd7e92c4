package au.com.peterpal.selecting.standingorders.admin.dto;

import lombok.*;

import java.math.BigDecimal;
@With
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BudgetTotals {
  private BigDecimal totalBudgetAmount = BigDecimal.ZERO;
  private BigDecimal totalOnOrderAmount = BigDecimal.ZERO;
  private BigDecimal totalInvoicedAmount = BigDecimal.ZERO;
  private BigDecimal totalSpentAmount = BigDecimal.ZERO;
  private BigDecimal totalRemainingAmountMonthly = BigDecimal.ZERO;
  private BigDecimal totalAmountWithRelatedFundMonthly = BigDecimal.ZERO;
  private BigDecimal totalRemainingAmountYearly = BigDecimal.ZERO;
  private BigDecimal totalAmountWithRelatedFundYearly = BigDecimal.ZERO;
}

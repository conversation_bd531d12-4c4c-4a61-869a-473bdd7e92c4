package au.com.peterpal.selecting.standingorders.pendingorder.model;

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId;
import lombok.*;

import java.util.Optional;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BranchDistribution {
  private BranchId branchId;
  private AllocationId allocationId;
  private String branchCode;
  private Integer quantity;
  private String fundCode;
  private String collectionCode;
  private String categoryCode;
  private String notes;

  public static BranchDistribution from(
      Allocation allocation, Category category, String fundCode, Integer quantity) {
    return BranchDistribution.builder()
        .branchId(allocation.getBranch().getBranchId())
        .allocationId(allocation.getAllocationId())
        .branchCode(allocation.getBranch().getCode())
        .quantity(quantity)
        .fundCode(fundCode)
        .collectionCode(allocation.getCollectionCode())
        .categoryCode(Optional.ofNullable(category).map(Category::getCode).orElse(null))
        .notes(allocation.getNotes())
        .build();
  }
}

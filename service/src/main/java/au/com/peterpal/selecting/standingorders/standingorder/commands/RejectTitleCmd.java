//package au.com.peterpal.selecting.standingorders.standingorder.commands;
//
//import au.com.peterpal.selecting.standingorders.standingorder.dto.RejectTitleRequest;
//import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonEnum;
//import au.com.peterpal.selecting.standingorders.standingorder.model.TitleId;
//import lombok.*;
//
//import javax.validation.constraints.NotNull;
//
//@Value
//@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
//@AllArgsConstructor(staticName = "of")
//@Builder
//public class RejectTitleCmd {
//  @NotNull @NonNull private TitleId titleId;
//  @NotNull @NonNull private RejectionReasonEnum rejectionReasonEnum;
//  private String otherReason;
//
//  public static RejectTitleCmd from(TitleId titleId, RejectTitleRequest rejectTitleRequest) {
//    return RejectTitleCmd.builder()
//        .titleId(titleId)
//        .rejectionReasonEnum(
//            RejectionReasonEnum.valueOf(rejectTitleRequest.getRejectionReasonType()))
//        .otherReason(rejectTitleRequest.getOtherReason())
//        .build();
//  }
//}

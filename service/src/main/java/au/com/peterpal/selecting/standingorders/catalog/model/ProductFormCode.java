package au.com.peterpal.selecting.standingorders.catalog.model;

/**
 * @docRoot Based on Onix List 7
 */
public enum ProductFormCode {
  UN("00", "Undefined", ""),
  AA("AA", "Audio", "Audio recording – detail unspecified."),
  AB("AB", "Audio cassette", "Audio cassette (analogue)."),
  AC(
      "AC",
      "CD-Audio",
      "Audio compact disc, in any recording format: use for 'red book' (conventional audio CD) and SACD, and use coding in Product Form Detail to specify the format, if required."),
  AD("AD", "DAT", "Digital audio tape cassette."),
  AE("AE", "Audio disc", "Audio disc (excluding CD-Audio)."),
  AF("AF", "Audio tape", "Audio tape (analogue open reel tape)."),
  AG("AG", "MiniDisc", "Sony MiniDisc format."),
  AH(
      "AH",
      "CD-Extra",
      "Audio compact disc with part CD-ROM content, also termed CD-Plus or Enhanced-CD: use for 'blue book' and 'yellow/red book' two-session discs."),
  AI("AI", "DVD Audio", ""),
  AJ("AJ", "Downloadable audio file", "Audio recording downloadable online."),
  AK(
      "AK",
      "Pre-recorded digital audio player",
      "For example, Playaway audiobook and player: use coding in Product Form Detail to specify the recording format, if required."),
  AL("AL", "Pre-recorded SD card", "For example, Audiofy audiobook chip."),
  AM("AM", "LP", "Vinyl disc (analogue)."),
  AN(
      "AN",
      "Downloadable and online audio file",
      "Digital audio recording available both by download to the purchaser’s own device(s) and by online (eg streamed) access."),
  AO(
      "AO",
      "Online audio file",
      "Digital audio recording available online (eg streamed), not downloadable to the purchaser’s own device(s)."),
  AZ("AZ", "Other audio format", "Other audio format not specified by AB to AL."),
  BA("BA", "Book", "Book – detail unspecified."),
  BB("BB", "Hardback", "Hardback or cased book."),
  BC("BC", "Paperback / softback", "Paperback or other softback book."),
  BD("BD", "Loose-leaf", "Loose-leaf book."),
  BE("BE", "Spiral bound", "Spiral, comb or coil bound book."),
  BF(
      "BF",
      "Pamphlet",
      "Pamphlet or brochure, stapled; German 'geheftet'. Includes low-extent wire-stitched books bound without a distinct spine (eg many comic books)."),
  BG("BG", "Leather / fine binding", ""),
  BH("BH", "Board book", "Child's book with all pages printed on board."),
  BI("BI", "Rag book", "Child's book with all pages printed on textile."),
  BJ("BJ", "Bath book", "Child's book printed on waterproof material."),
  BK(
      "BK",
      "Novelty book",
      "A book whose novelty consists wholly or partly in a format which cannot be described by any other available code – a 'conventional' format code is always to be preferred; one or more Product Form Detail codes, eg from the B2nn group, should be used whenever possible to provide additional description."),
  BL("BL", "Slide bound", "Slide bound book."),
  BM(
      "BM",
      "Big book",
      "Extra-large format for teaching etc; this format and terminology may be specifically UK; required as a top-level differentiator."),
  BN(
      "BN",
      "Part-work (fascículo)",
      "A part-work issued with its own ISBN and intended to be collected and bound into a complete book."),
  BO(
      "BO",
      "Fold-out book or chart",
      "Concertina-folded book or chart, designed to fold to pocket or regular page size: use for German 'Leporello'."),
  BP("BP", "Foam book", "A children's book whose cover and pages are made of foam."),
  BZ("BZ", "Other book format", "Other book format or binding not specified by BB to BP."),
  CA("CA", "Sheet map", "Sheet map – detail unspecified."),
  CB("CB", "Sheet map, folded", ""),
  CC("CC", "Sheet map, flat", ""),
  CD("CD", "Sheet map, rolled", "See Code List 80 for 'rolled in tube'."),
  CE("CE", "Globe", "Globe or planisphere."),
  CZ("CZ", "Other cartographic", "Other cartographic format not specified by CB to CE."),
  DA("DA", "Digital", "Digital or multimedia (detail unspecified)."),
  DB("DB", "CD-ROM", ""),
  DC("DC", "CD-I", "CD interactive, use for 'green book' discs."),
  DD("DD", "DVD", "DEPRECATED – use VI for DVD video, AI for DVD audio, DI for DVD-ROM."),
  DE("DE", "Game cartridge", ""),
  DF("DF", "Diskette", "AKA 'floppy disc'."),
  DG("DG", "Electronic book text", "Electronic book text in proprietary or open standard format."),
  DH(
      "DH",
      "Online resource",
      "An electronic database or other resource or service accessible through online networks."),
  DI("DI", "DVD-ROM", ""),
  DJ("DJ", "Secure Digital (SD) Memory Card", ""),
  DK("DK", "Compact Flash Memory Card", ""),
  DL("DL", "Memory Stick Memory Card", ""),
  DM("DM", "USB Flash Drive", ""),
  DN(
      "DN",
      "Double-sided CD/DVD",
      "Double-sided disc, one side CD-Audio/CD-ROM, other side DVD-Audio/DVD-Video/DVD-ROM (at least one side must be -ROM)."),
  DO(
      "DO",
      "Digital product license key",
      "Digital product license delivered through the retail supply chain as a physical “key”, typically a card or booklet containing a code enabling the purchaser to download or activate the associated product."),
  DZ("DZ", "Other digital", "Other digital or multimedia not specified by DB to DN."),
  EA("EA", "Ebook", "Digital content delivered electronically (delivery method unspecified)."),
  EB(
      "EB",
      "Digital download and online",
      "Digital content available both by download and by online access."),
  EC("EC", "Digital online", "Digital content accessed online only."),
  ED("ED", "Digital download", "Digital content delivered by download only."),
  FA("FA", "Film or transparency", "Film or transparency – detail unspecified."),
  FB("FB", "Film", "Continuous film or filmstrip: DEPRECATED – use FE or FF."),
  FC("FC", "Slides", "Photographic transparencies mounted for projection."),
  FD("FD", "OHP transparencies", "Transparencies for overhead projector."),
  FE("FE", "Filmstrip", ""),
  FF("FF", "Film", "Continuous movie film as opposed to filmstrip."),
  FZ(
      "FZ",
      "Other film or transparency format",
      "Other film or transparency format not specified by FB to FF."),
  MA("MA", "Microform", "Microform – detail unspecified."),
  MB("MB", "Microfiche", ""),
  MC("MC", "Microfilm", "Roll microfilm."),
  MZ("MZ", "Other microform", "Other microform not specified by MB or MC."),
  PA("PA", "Miscellaneous print", "Miscellaneous printed material – detail unspecified."),
  PB("PB", "Address book", "May use product form detail codes P201 to P204 to specify binding."),
  PC("PC", "Calendar", ""),
  PD("PD", "Cards", "Cards, flash cards (eg for teaching reading)."),
  PE("PE", "Copymasters", "Copymasters, photocopiable sheets."),
  PF("PF", "Diary", "May use product form detail codes P201 to P204 to specify binding."),
  PG(
      "PG",
      "Frieze",
      "Narrow strip-shaped printed sheet used mostly for education or children's products (eg depicting alphabet, number line, procession of illustrated characters etc). Usually intended for horizontal display."),
  PH("PH", "Kit", "Parts for post-purchase assembly."),
  PI("PI", "Sheet music", ""),
  PJ("PJ", "Postcard book or pack", ""),
  PK("PK", "Poster", "Poster for retail sale – see also XF."),
  PL(
      "PL",
      "Record book",
      "Record book (eg 'birthday book', 'baby book'): may use product form detail codes P201 to P204 to specify binding."),
  PM(
      "PM",
      "Wallet or folder",
      "Wallet or folder (containing loose sheets etc): it is preferable to code the contents and treat 'wallet' as packaging (List 80), but if this is not possible the product as a whole may be coded as a 'wallet'."),
  PN("PN", "Pictures or photographs", ""),
  PO("PO", "Wallchart", ""),
  PP("PP", "Stickers", ""),
  PQ(
      "PQ",
      "Plate (lámina)",
      "A book-sized (as opposed to poster-sized) sheet, usually in colour or high quality print."),
  PR(
      "PR",
      "Notebook / blank book",
      "A book with all pages blank for the buyer's own use: may use product form detail codes P201 to P204 to specify binding."),
  PS("PS", "Organizer", "May use product form detail codes P201 to P204 to specify binding."),
  PT("PT", "Bookmark", ""),
  PU("PU", "Leaflet", "Folded but unbound"),
  PV("PV", "Book plates", "Ex libris’ book labels and packs"),
  PZ("PZ", "Other printed item", "Other printed item not specified by PB to PT."),
  SA(
      "SA",
      "Multiple-component retail product",
      "Presentation unspecified: format of product components must be given in <ProductPart>. Use only when the packaging of the product is unknown, or when no other S* code applies and the presentation is described in <ProductFormDescription>."),
  SB(
      "SB",
      "Multiple-component retail product, boxed",
      "Format of product components must be given in <ProductPart>."),
  SC(
      "SC",
      "Multiple-component retail product, slip-cased",
      "Format of product components must be given in <ProductPart>."),
  SD(
      "SD",
      "Multiple-component retail product, shrink-wrapped",
      "Format of product components must be given in <ProductPart>. Use code XL for a shrink-wrapped pack for trade supply, where the retail items it contains are intended for sale individually."),
  SE(
      "SE",
      "Multiple-component retail product, loose",
      "Format of product components must be given in <ProductPart>"),
  SF(
      "SF",
      "Multiple-component retail product, part(s) enclosed",
      "Multiple component product where subsidiary product part(s) is/are supplied as enclosures to the primary part, eg a book with a CD packaged in a sleeve glued within the back cover."),
  SG(
      "SG",
      "Multiple-component retail product, entirely digital",
      "Multiple component product where all parts are digital, and delivered as separate files, eg a group of individual EPUB files, an EPUB with a PDF, an e-book with a license to access a range of online resources, etc."),
  VA("VA", "Video", "Video – detail unspecified."),
  VB("VB", "Video, VHS, PAL", "DEPRECATED – use new VJ."),
  VC("VC", "Video, VHS, NTSC", "DEPRECATED – use new VJ."),
  VD("VD", "Video, Betamax, PAL", "DEPRECATED – use new VK."),
  VE("VE", "Video, Betamax, NTSC", "DEPRECATED – use new VK."),
  VF("VF", "Videodisc", "eg Laserdisc."),
  VG("VG", "Video, VHS, SECAM", "DEPRECATED – use new VJ."),
  VH("VH", "Video, Betamax, SECAM", "DEPRECATED – use new VK."),
  VI("VI", "DVD video", "DVD video: specify TV standard in List 78."),
  VJ("VJ", "VHS video", "VHS videotape: specify TV standard in List 78."),
  VK("VK", "Betamax video", "Betamax videotape: specify TV standard in List 78."),
  VL("VL", "VCD", "VideoCD."),
  VM("VM", "SVCD", "Super VideoCD."),
  VN("VN", "HD DVD", "High definition DVD disc, Toshiba HD DVD format."),
  VO("VO", "Blu-ray", "High definition DVD disc, Sony Blu-ray format."),
  VP("VP", "UMD Video", "Sony Universal Media disc."),
  VQ("VQ", "CBHD", "China Blue High-Definition, derivative of HD-DVD."),
  VZ("VZ", "Other video format", "Other video format not specified by VB to VP."),
  WW(
      "WW",
      "Mixed media product",
      "A product consisting of two or more items in different media or different product forms, eg book and CD-ROM, book and toy, hardback book and e-book, etc."),
  WX(
      "WX",
      "Multiple copy pack",
      "A product containing multiple copies of one or more items packaged together for retail sale, consisting of either (a) several copies of a single item (eg 6 copies of a graded reader), or (b) several copies of each of several items (eg 3 copies each of 3 different graded readers), or (c) several copies of one or more single items plus a single copy of one or more related items (eg 30 copies of a pupil's textbook plus 1 of teacher's text). NOT TO BE CONFUSED WITH: multi-volume sets, or sets containing a single copy of a number of different items (boxed, slip-cased or otherwise); items with several components of different physical forms (see WW); or packs intended for trade distribution only, where the contents are retailed separately (see XC, XE, XL)."),
  XA("XA", "Trade-only material", "Trade-only material (unspecified)."),
  XB("XB", "Dumpbin – empty", ""),
  XC("XC", "Dumpbin – filled", "Dumpbin with contents."),
  XD("XD", "Counterpack – empty", ""),
  XE("XE", "Counterpack – filled", "Counterpack with contents."),
  XF("XF", "Poster, promotional", "Promotional poster for display, not for sale – see also PK."),
  XG("XG", "Shelf strip", ""),
  XH("XH", "Window piece", "Promotional piece for shop window display."),
  XI("XI", "Streamer", ""),
  XJ("XJ", "Spinner", ""),
  XK("XK", "Large book display", "Large scale facsimile of book for promotional display."),
  XL(
      "XL",
      "Shrink-wrapped pack",
      "A quantity pack with its own product code, for trade supply only: the retail items it contains are intended for sale individually – see also WX. For products or product bundles supplied shrink-wrapped for retail sale, use the Product Form code of the contents plus code 21 from List 80."),
  XM(
      "XM",
      "Boxed pack",
      "A quantity pack with its own product code, for trade supply only: the retail items it contains are intended for sale individually – see also WX. For products or product bundles supplied boxed for retail sale, use the Product Form code of the contents plus code 09 from List 80."),
  XN(
      "XN",
      "Pack (outer packaging unspecified)",
      "A quantity pack with its own product code, usually for trade supply only: the retail items it contains are intended for sale individually. ISBN (where applicable) and format of contained items must be given in <ProductPart>. Use only when the pack is neither shrinp-wrapped nor boxed."),
  XO("XO", "Spinner – filled", "."),
  XY("XY", "Other point of sale – including retail product", "."),
  XZ("XZ", "Other point of sale", "Other point of sale material not specified by XB to XL."),
  ZA("ZA", "General merchandise", "General merchandise – unspecified."),
  ZB("ZB", "Doll", ""),
  ZC("ZC", "Soft toy", "Soft or plush toy."),
  ZD("ZD", "Toy", ""),
  ZE("ZE", "Game", "Board game, or other game (except computer game: see DE)."),
  ZF("ZF", "T-shirt", ""),
  ZG("ZG", "E-book reader", "Dedicated e-book reading device, typically with mono screen."),
  ZH("ZH", "Tablet computer", "General purpose tablet computer, typically with color screen."),
  ZI(
      "ZI",
      "Audiobook player",
      "Dedicated audiobook player device, typically including book-related features like bookmarking."),
  ZJ("ZJ", "Jigsaw", ""),
  ZK("ZK", "Mug", "For example, branded, promotional or tie-in drinking mug, cup etc."),
  ZL("ZL", "Tote bag", "For example, branded, promotional or tie-in bag."),
  ZM(
      "ZM",
      "Tableware",
      "For example, branded, promotional or tie-in plates, bowls etc (note for mugs and cups, use code ZK)."),
  ZN("ZN", "Umbrella", "For example, branded, promotional or tie-in umbrella."),
  ZO("ZO", "Paints, crayons, pencils", "Coloring set, including pens, chalks, etc."),
  ZP(
      "ZP",
      "Handicraft kit",
      "Handicraft kit or set, eg sewing, crochet, weaving, basketry, beadwork, leather, wood or metalworking, pottery and glassworking, candlemaking etc."),
  ZX(
      "ZX",
      "Other toy/game accessories",
      "Other toy, game and puzzle items not specified by ZB to ZQ, generally accessories to other products etc. Further detail is expected in <ProductFormDescription>, as <ProductFormDetail> and <ProductFormFeature> are unlikely to be sufficient."),
  ZY(
      "ZY",
      "Other apparel",
      "Other apparel items not specified by ZB to ZJ, including promotional or branded scarves, caps, aprons etc."),
  ZZ("ZZ", "Other merchandise", "Other merchandise not specified by ZB to ZY.");

  private final String code;
  private final String description;
  private final String notes;

  ProductFormCode(String code, String description, String notes) {
    this.code = code;
    this.description = description;
    this.notes = notes;
  }

  public String getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public String getNotes() {
    return notes;
  }

  public static ProductFormCode mapOnixCode(String onixCode) {
    for (ProductFormCode value : ProductFormCode.values()) {
      if (value.code.equals(onixCode)) {
        return value;
      }
    }
    throw new IllegalArgumentException(
        "Invalid " + ProductFormCode.class.getSimpleName() + ": " + onixCode);
  }

  public Boolean isGroup() {
    return code.endsWith("A");
  }

  public Boolean isEbook() {
    boolean ebook = false;
    switch (this) {
      case DG:
      case EA:
      case EB:
      case EC:
      case ED:
        ebook = true;
        break;
      default:
        break;
    }
    return ebook;
  }

  @Override
  public String toString() {
    return this.description;
  }
}

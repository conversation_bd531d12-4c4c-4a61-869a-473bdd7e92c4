package au.com.peterpal.selecting.standingorders.ext.supplier.control;

import au.com.peterpal.selecting.standingorders.ext.supplier.boundary.dto.SupplierStatus;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.Supplier;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.SupplierId;
import java.util.Optional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SupplierRepository extends JpaRepository<Supplier, SupplierId> {

  List<Supplier> findAllByNameStartsWithIgnoreCaseAndStatus(String name, SupplierStatus status, Pageable pageable);

  @Query("select s from Supplier s where s.status = 'ACTIVE' and s.code = ?1")
  Optional<Supplier> findByCode(String code);

  @Query("select s from Supplier s where s.status = 'ACTIVE' and s.code = ?1")
  List<Supplier> findAllByCode(String code);

  @Query("select s from Supplier s where s.status = 'ACTIVE' order by s.code")
  List<Supplier> findAllActive();
}

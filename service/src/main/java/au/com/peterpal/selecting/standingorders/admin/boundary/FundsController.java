package au.com.peterpal.selecting.standingorders.admin.boundary;

import au.com.peterpal.selecting.standingorders.admin.dto.BudgetResponse;
import au.com.peterpal.selecting.standingorders.admin.dto.SearchBudgetRequest;
import au.com.peterpal.selecting.standingorders.admin.dto.SearchFundRequest;
import au.com.peterpal.selecting.standingorders.ext.budget.entity.FundType;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.boundary.dto.FundStatus;
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundService;
import au.com.peterpal.selecting.standingorders.ext.fund.dto.FundInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Log4j2
@RequestMapping("/api/funds")
@RestController
@AllArgsConstructor
@SecurityRequirement(name = "BearerAuth")
public class FundsController {

  private final FundService service;

  @GetMapping("/search")
  @Operation(summary = "Search by fund code, customer id or both")
  public List<FundInfo> search(
          @RequestParam(required = false) String code,
          @RequestParam(required = false) FundType type,
          @RequestParam(required = false) CustomerId customerId,
          @RequestParam(required = false) String staff,
          @RequestParam(required = false) FundStatus status,
          @RequestParam(required = false) List<CategoryId> categoryIds) {
    SearchFundRequest request =
        SearchFundRequest.builder()
            .code(code)
            .customerId(customerId)
            .staff(staff)
            .status(status)
            .type(type)
            .categoryIds(categoryIds)
            .build();
    log.info(() -> String.format("Search fund request %s", request));

    return FundInfo.from(service.searchFund(request));
  }

  @GetMapping("/search-by-current-fin-year")
  @Operation(summary = "Search by fund code, customer id, staff, status and current financial year")
  public List<FundInfo> searchFundByCurrentFinYear(
      @RequestParam(required = false) String code,
      @RequestParam(required = false) FundType type,
      @RequestParam(required = false) CustomerId customerId,
      @RequestParam(required = false) String staff,
      @RequestParam(required = false) FundStatus status,
      @RequestParam(required = false) List<CategoryId> categoryIds) {
    SearchFundRequest request =
        SearchFundRequest.builder()
            .code(code)
            .customerId(customerId)
            .staff(staff)
            .status(status)
            .type(type)
            .categoryIds(categoryIds)
            .build();
    log.info(() -> String.format("Search fund request %s", request));

    return FundInfo.from(service.searchFundByCurrentFinYear(request));
  }

  @GetMapping("/budget/search")
  public BudgetResponse search(
      @RequestParam(required = false) String customerCode,
      @RequestParam(required = false) FundType fundType,
      @RequestParam(required = false) List<String> fundCodes,
      @RequestParam(required = false) String staff,
      @RequestParam(required = false) String sortByKey,
      @RequestParam(required = false) Sort.Direction sortByDirection,
      @RequestParam(required = false) List<CategoryId> categoryIds,
      @PageableDefault(size = 20, direction = Sort.Direction.ASC, sort = "code")
          Pageable pageRequest) {
    SearchBudgetRequest searchBudgetRequest =
        SearchBudgetRequest.builder()
            .customerCode(customerCode)
            .fundCodes(fundCodes)
            .staff(staff)
            .fundType(fundType)
            .sortByKey(sortByKey)
            .sortByDirection(sortByDirection)
            .categoryIds(categoryIds)
            .build();
    log.info(() -> String.format("Search budget request %s", searchBudgetRequest));

    return service.searchBudget(searchBudgetRequest, pageRequest);
  }
}

package au.com.peterpal.selecting.standingorders.standingorder.match;

import au.com.peterpal.selecting.standingorders.standingorder.model.TermId;
import lombok.AllArgsConstructor;
import lombok.Value;
import lombok.With;

@Value
@With
@AllArgsConstructor(staticName = "of")
public class MatchResult {
  public static final MatchResult FALSE = MatchResult.of(false, "NO MATCH", "", null);

  boolean matched;
  String note;
  String matchedPersonName;
  TermId matchedTermId;
}

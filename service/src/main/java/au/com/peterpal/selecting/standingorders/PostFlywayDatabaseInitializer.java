package au.com.peterpal.selecting.standingorders;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class PostFlywayDatabaseInitializer implements CommandLineRunner {

  private final JdbcTemplate jdbcTemplate;

  @Value("${standing-orders.post-flyway-script}")
  private String sql;

  public PostFlywayDatabaseInitializer(JdbcTemplate jdbcTemplate) {
    this.jdbcTemplate = jdbcTemplate;
  }

  @Override
  public void run(String... args) throws Exception {
    log.debug("about to run " + sql);
    jdbcTemplate.execute(sql);
  }
}

package au.com.peterpal.selecting.standingorders.allocationpreference.control;

import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AllocationPreferenceResponse;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AllocationPreferenceSearchRequest;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.BulkUpdateAllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface AllocationPreferenceRepositoryCustom {
  Page<AllocationPreference> searchAllByCustomer(String customerCode, Pageable pageRequest);

  List<AllocationPreferenceResponse> search(AllocationPreferenceSearchRequest searchRequest);
}

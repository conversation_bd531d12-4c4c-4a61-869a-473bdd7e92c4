package au.com.peterpal.selecting.standingorders;

import au.com.peterpal.common.audit.CommonAuditConfig;
import au.com.peterpal.common.sequencenumbergenerator.SequenceApplication;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(scanBasePackages = "au.com.peterpal")
@EntityScan(
    basePackageClasses = {
      Application.class,
      SequenceApplication.class,
      CommonAuditConfig.class
    })
@EnableTransactionManagement
public class Application {
  public static void main(String[] args) {
    SpringApplication.run(Application.class, args);
  }
}

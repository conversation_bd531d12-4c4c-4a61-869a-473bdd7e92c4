package au.com.peterpal.selecting.standingorders.standingorder.control;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.ext.category.control.CategoryRepository;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import au.com.peterpal.selecting.standingorders.standingorder.model.CategoryInfo;
import au.com.peterpal.selecting.standingorders.standingorder.model.CategoryMapping;
import com.google.common.annotations.VisibleForTesting;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
@Log4j2
@Transactional
public class CategoryService {

  private final CategoryMappingRepository categoryMappingRepository;
  private final CategoryRepository categoryRepository;

//  /**
//   * Replace current category mapping.
//   * @param mappingList new category mappings
//   * @return the new category mappings list
//   */
//  public List<CategoryMapping> setCategoryMappings(List<String> mappingList) {
//    Affirm.of(mappingList).notNull("Category mapping list must not be null");
//
//    List<CategoryMapping> catMap = makeCategoryMap(mappingList);
//    categoryMappingRepository.deleteAll();
//    return categoryMappingRepository.saveAll(catMap);
//  }

  /**
   * Returns current category mappings.
   *
   * @return the current category mappings
   */
  public List<CategoryMapping> getCategoryMappings() {
    return categoryMappingRepository.findAll();
  }

  public Map<String, Category> getCategoryCache() {
    return categoryRepository.findAll().stream()
        .collect(Collectors.toMap(e -> e.getCode(), e -> e));
  }
  public List<Category> getCategoryByCodeIn(List<String> categoryCodes) {
    return categoryRepository.findByCodeIn(categoryCodes);
  }

  public Category categoryFromSubject(List<String> subjects) {

    Category category = null;
    List<CategoryMapping> categoryMappings = getCategoryMappings();

    for (String subject : subjects) {
      Optional<Category> categoryOptional = Optional.ofNullable(subject)
          .filter(s -> !s.isEmpty())
          .map(
              s -> {
                SubjectMatcher mapper = SubjectMatcher.of(categoryMappings);
                return Optional.ofNullable(mapper.findMatchingCategory(s))
                    .map(CategoryInfo::getCategory)
                    .orElse(null);
              });
      if (category == null ||
          (categoryOptional.isPresent() &&
              StringUtils.containsIgnoreCase(categoryOptional.get().getCode(), category.getCode()))) {
        category = categoryOptional.orElse(null);
      }
    }
    return category;
  }

  /**
   * Returns category information (category and priority) for given subject.
   *
   * @param subject subject for which category has to be returned
   * @return <class>CategoryInfo</class> if a match exists or null
   */
  public CategoryInfo getCategory(String subject) {
    if (subject == null) {
      return null;
    }
    SubjectMatcher matcher = SubjectMatcher.of(categoryMappingRepository.findAll());
    return matcher.match(subject);
  }

//  public List<String> getCategories() {
//    return categoryRepository.getCategories();
//  }

  public Category getCategoryByCode(String categoryCode) {
    return categoryRepository.findByCode(categoryCode);
  }

  @VisibleForTesting
  private List<CategoryMapping> makeCategoryMap(List<String> mappingList) {
    List<CategoryMapping> result = new ArrayList<>();
    for (int i = 0; i < mappingList.size(); i++) {
      String s = mappingList.get(i);
      List<String> idList = Stream.of(s.split(",", -1))
          .map(String::trim)
          .collect(Collectors.toList());

      if (idList.size() == 2) {
        Category category = categoryRepository.findByCode(idList.get(1));
        result.add(CategoryMapping.of(idList.get(0), category, i));
      } else {
        String msg = String.format("Wrong mapping. Expecting \"<subject>,<category>\"; found \"%s\"", s);
        log.error(() -> msg);
        throw new BusinessException(msg);
      }
    }
    return result;
  }

  public Category findById(CategoryId categoryId) {
    return categoryRepository
        .findById(categoryId)
        .orElseThrow(
            () -> new ResourceNotFoundException(Category.class, String.valueOf(categoryId)));
  }
}

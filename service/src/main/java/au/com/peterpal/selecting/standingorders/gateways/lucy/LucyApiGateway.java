package au.com.peterpal.selecting.standingorders.gateways.lucy;

import au.com.peterpal.selecting.standingorders.gateways.lucy.dto.TitleOrderInfo;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class LucyApiGateway {

  private final LucyApiClient lucyApiClient;

  public LucyApiGateway(LucyApiClient lucyApiClient) {
    this.lucyApiClient = lucyApiClient;
  }

  public TitleOrderInfo addTitleOrderInfo(TitleOrderInfo titleOrderInfo) {
    Affirm.of(titleOrderInfo).notNull("TitleOrderInfo must not be null");
    return lucyApiClient.addTitleOrder(titleOrderInfo);
  }
}

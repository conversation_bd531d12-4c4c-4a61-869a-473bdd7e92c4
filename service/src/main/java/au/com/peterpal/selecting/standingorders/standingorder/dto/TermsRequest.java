package au.com.peterpal.selecting.standingorders.standingorder.dto;

import au.com.peterpal.selecting.standingorders.standingorder.model.*;
import au.com.peterpal.selecting.standingorders.utils.StringHelper;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;
import org.apache.commons.collections.CollectionUtils;

@With
@Data
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
public class TermsRequest {

  private TermId termId;
  private TermType type;
  private OperationType operation;
  private String value;
  private CombinedTermId combinedTermId;
  private List<TermsRequest> andTerms;

  public static List<Term> from(List<TermsRequest> termsRequestList) {
    return Optional.ofNullable(termsRequestList)
        .map(List::stream)
        .orElseGet(Stream::empty)
        .map(TermsRequest::from)
        .filter(Objects::nonNull)
        .flatMap(Collection::stream)
        .collect(Collectors.toList());
  }

  public static List<Term> from(TermsRequest termsRequest) {
    CombinedTermId combinedTermId =
        Optional.ofNullable(termsRequest.getCombinedTermId())
            .orElse(CombinedTermId.of(UUID.randomUUID()));
    List<Term> terms = new ArrayList<>();
    Term t =
        Term.builder()
            .termId(
                Optional.ofNullable(termsRequest.getTermId()).orElse(TermId.of(UUID.randomUUID())))
            .type(termsRequest.getType())
            .operation(termsRequest.getOperation())
            .value(StringHelper.of(termsRequest.getValue()).trim())
            .combinedTerm(CombinedTerm.builder().combinedTermId(combinedTermId).build())
            .build();
    terms.add(t);
    if (CollectionUtils.isNotEmpty(termsRequest.getAndTerms())) {
      for (TermsRequest tr : termsRequest.getAndTerms()) {
        tr.setCombinedTermId(combinedTermId);
        List<Term> andTerms = from(tr);
        terms.addAll(andTerms);
      }
    }
    return terms;
  }
}

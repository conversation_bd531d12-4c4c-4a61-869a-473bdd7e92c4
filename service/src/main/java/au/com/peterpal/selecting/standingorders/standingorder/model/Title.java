//package au.com.peterpal.selecting.standingorders.standingorder.model;
//
//import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
//import au.com.peterpal.selecting.standingorders.standingorder.dto.TitleInfo;
//import au.com.peterpal.selecting.standingorders.standingorder.dto.TitleStatusInfo;
//import au.com.peterpal.selecting.standingorders.standingorder.events.*;
//import com.fasterxml.jackson.annotation.JsonIgnore;
//import com.fasterxml.jackson.annotation.JsonInclude;
//
//import java.time.LocalDate;
//import lombok.*;
//import org.springframework.data.domain.Page;
//
//import javax.persistence.*;
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Optional;
//
//@With
//@Data
//@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
//@AllArgsConstructor(access = AccessLevel.PRIVATE)
//@Builder(toBuilder = true)
//@EqualsAndHashCode(onlyExplicitlyIncluded = true)
//@ToString(onlyExplicitlyIncluded = true)
//@JsonInclude(JsonInclude.Include.NON_NULL)
//@NamedNativeQuery(
//    name = "Title.getCounterInfo_Named",
//    query =
//        "SELECT count(*) FILTER (WHERE title_status = 'NEW') AS newTitle"
//            + ", count(*) FILTER (WHERE deferred_date > now()) AS deferred"
//            + ", count(*) FILTER (WHERE title_status = 'PENDING')  AS pending"
//            + ", count(*) FILTER (WHERE title_status = 'REJECTED')  AS rejected"
//            + ", count(*) FILTER (WHERE title_status = 'PROCESSED')  AS processed "
//            + "FROM title",
//    resultSetMapping = "Mapping.TitleStatusInfo")
//@SqlResultSetMapping(
//    name = "Mapping.TitleStatusInfo",
//    classes =
//        @ConstructorResult(
//            targetClass = TitleStatusInfo.class,
//            columns = {
//              @ColumnResult(name = "newTitle", type = Integer.class),
//              @ColumnResult(name = "deferred", type = Integer.class),
//              @ColumnResult(name = "pending", type = Integer.class),
//              @ColumnResult(name = "rejected", type = Integer.class),
//              @ColumnResult(name = "processed", type = Integer.class)
//            }))
//@Entity
//@Table(name = "title")
//public class Title {
//
//  @EqualsAndHashCode.Include @NonNull @EmbeddedId private TitleId titleId;
//
//  @NonNull private Integer productId;
//
//  @Enumerated(EnumType.STRING)
//  private ReleaseType releaseType;
//
//  private String category;
//
//  private LocalDateTime deferredDate;
//
//  @Enumerated(EnumType.STRING)
//  private TitleStatus titleStatus;
//
//  @Enumerated(EnumType.STRING)
//  private TitleType type;
//
//  private String title;
//  private String subtitle;
//  private String series;
//  private String isbn;
//  private String personName;
//
//  private String corporateName;
//
//  private String imprint;
//
//  private LocalDate publicationDate;
//
//  private LocalDateTime dateAdded;
//
//  @ManyToOne
//  @JoinColumn(name = "rejectionReasonTypeId")
//  private RejectionReasonType rejectionReasonType;
//
//  private String rejectionOtherReason;
//
//  @OneToMany(mappedBy = "title", cascade = CascadeType.ALL, orphanRemoval = true)
//  @JsonIgnore
//  @Builder.Default
//  private List<StandingOrderTitle> standingOrderTitles = new ArrayList<>();
//
//  @OneToOne
//  @JoinColumn(name = "titleGroupId", nullable = false)
//  private TitleGroup titleGroup;
//
//  public Title update(RejectionReasonType rejectionReasonType, TitleRejected event) {
//    this.setTitleStatus(TitleStatus.REJECTED);
//    this.setRejectionReasonType(rejectionReasonType);
//    this.setRejectionOtherReason(event.getOtherReason());
//    return this;
//  }
//
//  public Title update(TitleDeferred event) {
//    this.setDeferredDate(event.getDeferredDate());
//    return this;
//  }
//
//  public Title addStandingOrderTitle(StandingOrderTitle standingOrderTitle) {
//    this.getStandingOrderTitles().add(standingOrderTitle);
//
//    boolean isAllRejected =
//        this.getStandingOrderTitles().stream()
//            .allMatch(st -> st.getStatus() == StandingTitleStatus.REJECTED);
//
//    if (isAllRejected) {
//      this.titleStatus = TitleStatus.REJECTED;
//    }
//
//    return this;
//  }
//
//  public static Title from(TitleInitialRelease event) {
//    return Title.builder()
//        .titleId(TitleId.of(event.getId()))
//        .releaseType(event.getReleaseType())
//        .deferredDate(event.getDeferredDate())
//        .build();
//  }
//
//  public static Title from(TitleFirstSmallFormatPaperback event) {
//    return Title.builder()
//        .titleId(TitleId.of(event.getId()))
//        .releaseType(event.getReleaseType())
//        .deferredDate(event.getDeferredDate())
//        .build();
//  }
//
//  public static Title from(TitleReissued event) {
//    return Title.builder()
//        .titleId(TitleId.of(event.getId()))
//        .releaseType(event.getReleaseType())
//        .deferredDate(event.getDeferredDate())
//        .build();
//  }
//
//  public static Page<TitleInfo> toTitleInfo(Page<Title> titlePage) {
//    return Optional.ofNullable(titlePage)
//        .map(p -> p.map(Title::toTitleInfo))
//        .orElseGet(Page::empty);
//  }
//
//  public TitleInfo toTitleInfo() {
//    return TitleInfo.builder()
//        .titleId(titleId)
//        .category(category)
//        .releaseType(releaseType)
//        .titleStatus(titleStatus)
//        .type(type)
//        .title(title)
//        .subtitle(subtitle)
//        .series(series)
//        .productRef(isbn)
//        .personName(personName)
//        .corporateName(corporateName)
//        .imprint(imprint)
//        .dateAdded(dateAdded)
//        .publicationDate(publicationDate)
//        .build();
//  }
//
//  public boolean isDeferred() {
//    return deferredDate != null && deferredDate.isAfter(LocalDateTime.now());
//  }
//
//  public boolean isRejected() {
//    return titleStatus == TitleStatus.REJECTED;
//  }
//}

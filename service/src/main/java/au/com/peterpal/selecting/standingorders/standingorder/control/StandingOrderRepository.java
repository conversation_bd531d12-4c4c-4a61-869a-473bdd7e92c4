package au.com.peterpal.selecting.standingorders.standingorder.control;

import au.com.peterpal.selecting.standingorders.standingorder.model.SOSummary;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface StandingOrderRepository
    extends JpaRepository<StandingOrder, StandingOrderId>, StandingOrderRepositoryCustom {

  Optional<StandingOrder> findByStandingOrderNumber(String standingOrderNumber);

  @Query("select so from StandingOrder so where so.standingOrderStatus = 'ACTIVE'")
  List<SOSummary> findAllSummary();


  @Query(
      "select so from StandingOrder so where so.standingOrderStatus = 'ACTIVE' and so.standingOrderId = ?1")
  Optional<SOSummary> findSummaryById(StandingOrderId id);

  boolean existsByDescription(String description);

  List<StandingOrder> findByStandingOrderStatus(StandingOrderStatus standingOrderStatus);
}

//package au.com.peterpal.selecting.standingorders.standingorder.dto;
//
//import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
//import au.com.peterpal.selecting.standingorders.standingorder.model.TitleStatus;
//import javax.validation.constraints.NotNull;
//import lombok.AccessLevel;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import lombok.NonNull;
//import org.springframework.data.domain.Pageable;
//
//@Data
//@NoArgsConstructor
//@AllArgsConstructor(access = AccessLevel.PRIVATE)
//@Builder
//public class TitleSearchRequest {
//  private TitleStatus status;
//  private String category;
//  private ReleaseType release;
//  private boolean includeDeferred;
//  private String text;
//
//  @NonNull
//  @NotNull
//  private Pageable pageRequest;
//}

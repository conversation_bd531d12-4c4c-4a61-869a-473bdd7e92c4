package au.com.peterpal.selecting.standingorders.titles.entity;

import au.com.peterpal.selecting.standingorders.standingorder.model.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.Set;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder(toBuilder = true)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "standing_order_aggregated")
public class StandingOrderAggregated {

  @EqualsAndHashCode.Include @NonNull @EmbeddedId
  private StandingOrderAggregatedId standingOrderAggregatedId;

  @OneToOne
  @JoinColumn(name = "standingOrderId", nullable = false)
  private StandingOrder standingOrder;

  @Type(type = "json")
  @Column(columnDefinition = "json")
  private Set<MatchedTermTuple> matchedTermTuples;

  @ManyToOne
  @JoinColumn(name = "titleId", nullable = false)
  private Title title;

  @Enumerated(EnumType.STRING)
  private StandingOrderAggregatedStatus status;

  public static StandingOrderAggregated copyAcceptedMatchStandingOrderAggregated(
      StandingOrderAggregated soAggregated, Title relatedTitle) {
    return StandingOrderAggregated.builder()
        .standingOrderAggregatedId(new StandingOrderAggregatedId())
        .standingOrder(soAggregated.getStandingOrder())
        .matchedTermTuples(soAggregated.getMatchedTermTuples())
        .title(relatedTitle)
        .status(StandingOrderAggregatedStatus.ACCEPTED)
        .build();
  }
}

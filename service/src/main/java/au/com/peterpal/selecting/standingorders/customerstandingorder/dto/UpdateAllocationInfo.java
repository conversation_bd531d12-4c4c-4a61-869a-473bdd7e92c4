package au.com.peterpal.selecting.standingorders.customerstandingorder.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.SmallFormatPaperbackRule;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@With
@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class UpdateAllocationInfo {
  @NotNull UUID allocationId;
  @NotNull UUID customerStandingOrderId;
  UUID allocationPreferenceId;
  @NotNull AllocationStatus status;
  @NotNull List<String> categories;
  UUID fundId;
  String customerReference;
  String deliveryInstructions;
  String notes;
  String collectionCode;
  Integer hbQty;
  Integer pbQty;
  Integer totalQty;
  UUID hbFundId;
  UUID pbFundId;
  ActionType actionType;
  AssignmentRule assignmentRule;
  SmallFormatPaperbackRule smallFormatPaperbackRule;
  @Builder.Default
  Boolean rematchAllocationAfterUpdate = false;

  public Integer getReleaseHBQuantity(Integer defaultValue) {
    if (Objects.isNull(getAssignmentRule())) {
      return defaultValue;
    }
    if (Objects.requireNonNull(getAssignmentRule()) == AssignmentRule.SPLIT) {
      return Optional.ofNullable(getHbQty()).orElse(defaultValue);
    }
    return 0;
  }

  public Integer getReleasePBQuantity(Integer defaultValue) {
    if (Objects.isNull(getAssignmentRule())) {
      return defaultValue;
    }
    if (Objects.requireNonNull(getAssignmentRule()) == AssignmentRule.SPLIT) {
      return Optional.ofNullable(getPbQty()).orElse(defaultValue);
    }
    return 0;
  }

  public Integer getReleaseQuantity(Integer defaultValue) {
    if (Objects.isNull(getAssignmentRule())) {
      return defaultValue;
    }
    switch (getAssignmentRule()) {
      case FIRST_AVAILABLE:
        return Optional.ofNullable(getTotalQty()).orElse(defaultValue);
      case SPLIT:
        return Objects.nonNull(getPbQty()) && Objects.nonNull(getHbQty())
            ? getPbQty() + getHbQty()
            : defaultValue;
      default:
        return defaultValue;
    }
  }

  @JsonIgnore
  public boolean hasQtyUpdate() {
    return Objects.nonNull(getHbQty())
        || Objects.nonNull(getPbQty())
        || Objects.nonNull(getTotalQty());
  }
}

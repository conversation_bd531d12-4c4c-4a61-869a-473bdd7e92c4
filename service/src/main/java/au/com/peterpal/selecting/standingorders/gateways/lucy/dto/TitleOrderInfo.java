package au.com.peterpal.selecting.standingorders.gateways.lucy.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.ProductSummaryInfo;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.ToString;
import lombok.Value;
import lombok.extern.log4j.Log4j2;

import java.time.LocalDate;
import java.util.Optional;

@Builder
@Value
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@Log4j2
public class TitleOrderInfo {

  private String status;

  private String customerCode;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
  private LocalDate orderDate;

  private String sourceCode;

  private Integer qty;

  private String productIdValue;

  private String title;

  private String author;

  private String publisherName;

  private String customerReference;

  private String fundCode;

  private String deliveryInstructions;

  private String orderNumber;

  public static TitleOrderInfo from(
      ProductSummaryInfo productSummaryInfo,
      Allocation allocation,
      Release release,
      Fund fund,
      Customer customer,
      PendingOrder pendingOrder) {

    Affirm.of(productSummaryInfo).notNull("ProductSummaryInfo must not be null");
    Affirm.of(allocation).notNull("Allocation must not be null");
    Affirm.of(release).notNull("Release must not be null");
    Affirm.of(customer).notNull("Customer must not be null");
    Affirm.of(pendingOrder).notNull("PendingOrder must not be null");

    return TitleOrderInfo.builder()
        .publisherName(productSummaryInfo.getPublisher())
        .sourceCode("STANDING ORDER")
        .title(productSummaryInfo.getTitle().getTitleText())
        .author(productSummaryInfo.getAuthors())
        .productIdValue(productSummaryInfo.getIsbn())
        .deliveryInstructions(allocation.getDeliveryInstructions())
        .orderDate(LocalDate.now())
        .qty(release.getQuantity())
        .fundCode(Optional.ofNullable(fund).map(Fund::getCode).orElse(null))
        .customerCode(customer.getCode())
        .customerReference(pendingOrder.getCustomerReference())
        .build();
  }
}

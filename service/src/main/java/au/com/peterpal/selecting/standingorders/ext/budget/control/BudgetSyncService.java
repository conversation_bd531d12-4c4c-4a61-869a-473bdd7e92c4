package au.com.peterpal.selecting.standingorders.ext.budget.control;

import au.com.peterpal.common.utils.ext.SyncService;
import au.com.peterpal.selecting.standingorders.ext.budget.boundary.dto.BudgetMessage;
import au.com.peterpal.selecting.standingorders.ext.budget.entity.Budget;
import au.com.peterpal.selecting.standingorders.ext.budget.entity.BudgetRelatedFund;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerService;
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundRepository;
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundService;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

@Log4j2
@Service
public class BudgetSyncService extends SyncService<BudgetRepository, BudgetMessage, Budget> {

  private final CustomerService customerService;
  private final FundService fundService;
  private final FundRepository fundRepository;

  public BudgetSyncService(
      CustomerService customerService,
      BudgetRepository budgetRepository,
      FundService fundService,
      FundRepository fundRepository) {
    super(budgetRepository);
    this.customerService = customerService;
    this.fundService = fundService;
    this.fundRepository = fundRepository;
  }

  @Override
  protected Object getId(BudgetMessage msg) {
    return msg.getBudgetId();
  }

  @Override
  protected BudgetMessage update(Budget existing, BudgetMessage msg) {
    existing.setFund(fundService.getFund(msg.getFundId()));
    existing.setAmount(msg.getAmount());
    existing.setCustomer(customerService.findById(msg.getCustomerId()));
    existing.setStartDate(msg.getStartDate());
    existing.setEndDate(msg.getEndDate());
    existing.setCurrencyCode(msg.getCurrencyCode());
    existing.setTopUpAmount(msg.getTopUpAmount());
    Map<FundId, Fund> funds =
        fundRepository.findAllById(msg.getMapOfRelatedFunds().values()).stream()
            .collect(Collectors.toMap(Fund::getFundId, fund -> fund));
    existing.getRelatedFunds().clear();
    existing
        .getRelatedFunds()
        .addAll(
            msg.getMapOfRelatedFunds().entrySet().stream()
                .map(
                    f ->
                        BudgetRelatedFund.builder()
                            .budgetRelatedFundId(f.getKey())
                            .budget(existing)
                            .fund(funds.get(f.getValue()))
                            .build())
                .collect(Collectors.toSet()));
    save(existing);
    return msg;
  }

  @Override
  protected BudgetMessage create(BudgetMessage msg) {
    Budget budget =
        Budget.builder()
            .budgetId(msg.getBudgetId())
            .fund(fundService.getFund(msg.getFundId()))
            .amount(msg.getAmount())
            .topUpAmount(msg.getTopUpAmount())
            .customer(customerService.findById(msg.getCustomerId()))
            .startDate(msg.getStartDate())
            .endDate(msg.getEndDate())
            .currencyCode(msg.getCurrencyCode())
            .build();
    Map<FundId, Fund> funds =
        fundRepository.findAllById(msg.getMapOfRelatedFunds().values()).stream()
            .collect(Collectors.toMap(Fund::getFundId, fund -> fund));
    budget.setRelatedFunds(
        msg.getMapOfRelatedFunds().entrySet().stream()
            .map(
                f ->
                    BudgetRelatedFund.builder()
                        .budgetRelatedFundId(f.getKey())
                        .budget(budget)
                        .fund(funds.get(f.getValue()))
                        .build())
            .collect(Collectors.toSet()));
    save(budget);
    return msg;
  }
}

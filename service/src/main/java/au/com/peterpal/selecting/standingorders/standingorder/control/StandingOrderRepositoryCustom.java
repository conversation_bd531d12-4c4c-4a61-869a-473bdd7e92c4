package au.com.peterpal.selecting.standingorders.standingorder.control;

import au.com.peterpal.selecting.standingorders.standingorder.dto.BulkUpdateStandingOrderRequest;
import au.com.peterpal.selecting.standingorders.standingorder.dto.StandingOrderRequest;
import au.com.peterpal.selecting.standingorders.standingorder.dto.StandingOrderResponse;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface StandingOrderRepositoryCustom {
    Page<StandingOrderResponse> search(StandingOrderRequest searchRequest, Pageable pageRequest);

    List<StandingOrder> searchStandingOrder(StandingOrderRequest searchRequest);

    List<StandingOrderId> getStandingOrderIdsForBulkUpdate(StandingOrderRequest searchRequest, List<StandingOrderId> excludedStandingOrderIds);

    Long bulkUpdate(List<StandingOrderId> standingOrderIds, BulkUpdateStandingOrderRequest request);
}

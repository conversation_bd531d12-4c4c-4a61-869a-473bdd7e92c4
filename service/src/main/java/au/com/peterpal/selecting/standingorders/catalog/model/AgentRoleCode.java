package au.com.peterpal.selecting.standingorders.catalog.model;

/**
 * @docRoot
 * Based on Onix List 69
 */
public enum AgentRoleCode {

	ESA ("05", "Exclusive sales agent", "Publisher's exclusive sales agent in a specified territory"),
	NESA ("06", "Non-exclusive sales agent", "Publisher's non-exclusive sales agent in a specified territory"),
	LP ("07", "Local publisher", "Publisher for a specified territory"),
	SA ("08", "Sales agent", "Publisher's sales agent in a specific territory. Use only where exclusive / non-exclusive status is not known. Prefer 05 or 06 as appropriate, where possible.");

	private final String code;
	private final String description;
	private final String notes;

	AgentRoleCode(String code, String description, String notes) {
		this.code = code;
		this.description = description;
		this.notes = notes;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public String getNotes() {
		return notes;
	}

	public static AgentRoleCode mapOnixCode(String onixCode) {
		for (AgentRoleCode value : AgentRoleCode.values()) {
			if (value.code.equals(onixCode)) {
				return value;
			}
		}
		throw new IllegalArgumentException("Invalid " + AgentRoleCode.class.getSimpleName() + ": " + onixCode);
	}

	@Override
	public String toString() {
		return this.description;
	}
}

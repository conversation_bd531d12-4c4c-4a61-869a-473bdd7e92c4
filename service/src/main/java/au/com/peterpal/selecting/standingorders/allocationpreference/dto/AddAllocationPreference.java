package au.com.peterpal.selecting.standingorders.allocationpreference.dto;

import java.util.List;
import java.util.UUID;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.Value;
import lombok.With;

@With
@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class AddAllocationPreference {

  @NotNull List<String> categories;

  String customerReference;

  String deliveryInstructions;

  String notes;

  String fundCode;

  @NotNull @NonNull UUID customerId;
}

package au.com.peterpal.selecting.standingorders.pendingorder.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.Supplier;
import au.com.peterpal.selecting.standingorders.titles.entity.Title;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotNull;

import au.com.peterpal.selecting.standingorders.utils.CurrencyCode;
import lombok.*;

@Builder
@Value
public class CopyPendingOrderDto {
  @NotNull Title title;
  @NotNull Allocation allocation;
  List<Allocation> branchAllocations;
  Map<ReleaseFormat, PendingOrderDetail> pendingOrderDetailMap;

  @With
  @Builder
  @Value
  public static class PendingOrderDetail {
    String isbn;
    Fund fund;
    ReleaseFormat format;
    String deliveryInstruction;
    String notes;
    BigDecimal price;
    LocalDate publicationDate;;
    CurrencyCode currencyCode;
    Supplier supplier;
    String customerReference;
    String collectionCode;
    Category category;
    @NotNull Integer quantityTaken;
    Map<AllocationId, Integer> brantchQtyTakenMap;
  }
}

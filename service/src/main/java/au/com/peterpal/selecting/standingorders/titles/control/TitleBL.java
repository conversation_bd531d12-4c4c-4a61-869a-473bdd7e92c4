package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationService;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderBL;
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderService;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderBL;
import au.com.peterpal.selecting.standingorders.standingorder.dto.ProductInfo;
import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonTypeId;
import au.com.peterpal.selecting.standingorders.titles.dto.*;
import au.com.peterpal.selecting.standingorders.titles.entity.*;
import au.com.peterpal.selecting.standingorders.titles.events.*;
import au.com.peterpal.selecting.standingorders.utils.CurrencyCode;
import com.google.common.collect.Lists;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import javax.persistence.EntityNotFoundException;
import javax.transaction.Transactional;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus.*;

@Log4j2
@Service
@RequiredArgsConstructor
public class TitleBL {
  private final TitleRepository titleRepository;
  private final MatchedProductRepository matchedProductRepository;
  private final ProductAggregatedRepository productAggregatedRepository;
  private final StandingOrderBL standingOrderBL;
  private final PendingOrderBL pendingOrderBL;
  private final PendingOrderService pendingOrderService;
  private final TitleService titleService;
  private final EventPublisher eventPublisher;
  private final AllocationService allocationService;
  private final RelatedTitleBL relatedTitleBL;

  public MatchedProduct updateMatchedProductFormat(
      TitleId titleId, UpdateMatchedProductFormatRequest request) {
    MatchedProduct matchedProduct =
        matchedProductRepository
            .findByTitleTitleIdAndMatchedProductId(titleId, request.getMatchedProductId())
            .orElseThrow(
                () ->
                    new EntityNotFoundException(
                        String.format(
                            "Matched Product not found for title: %s and match product id: %s",
                            titleId, request.getMatchedProductId())));

    matchedProduct.setFormat(request.getFormat());
    return matchedProductRepository.saveAndFlush(matchedProduct);
  }

  public void processTitle(ProcessTitle request, String username) {
    Title title = titleService.findTitleById(request.getTitleId());
    if (title.isOriginal() && (title.isNew() || title.isPending())) {
      // validate title
      if (title.getCategory() == null) {
        throw new BusinessException("Title category is blank, please assign one.");
      }

      Map<ProductAggregatedId, ProcessTitle.Product> productMap =
          request.getProducts().stream()
              .collect(
                  HashMap::new, (m, e) -> m.put(e.getProductAggregatedId(), e), HashMap::putAll);

      List<ProductAggregated> products =
          productAggregatedRepository.findAllByProductAggregatedIdIn(
              Lists.newArrayList(productMap.keySet()));
      // product validation
      validateProductSelection(products);

      List<StandingOrderAggregated> selectedStandingOrderAggregatedList =
          title.getStandingOrderAggregatedList().stream()
              .filter(
                  so ->
                      request
                          .getStandingOrderAggregatedIds()
                          .contains(so.getStandingOrderAggregatedId()))
              .collect(Collectors.toList());

      if (title.isNew() || title.isPending()) {
        // find allocations
        List<Allocation> allocations =
            standingOrderBL.getAllocations(
                title.getCategory(),
                selectedStandingOrderAggregatedList.stream()
                    .map(StandingOrderAggregated::getStandingOrder)
                    .collect(Collectors.toList()));

        validateAllocation(allocations, title.getCategory());
        List<ProductInfo> productsToProcess =
            products.stream()
                .map(
                    p -> {
                      ProcessTitle.Product product = productMap.get(p.getProductAggregatedId());
                      return p.toProductInfo()
                          .withPrice(product.getPrice())
                          .withCurrencyCode(CurrencyCode.from(product.getCurrencyCode()));
                    })
                .collect(Collectors.toList());

        // create pending order
        allocations.forEach(
            allocation -> pendingOrderBL.create(allocation, title, productsToProcess).stream());

        acceptAndRejectProducts(title, productMap.keySet());
        acceptAndRejectStandingOrders(
            title,
            selectedStandingOrderAggregatedList.stream()
                .map(StandingOrderAggregated::getStandingOrderAggregatedId)
                .collect(Collectors.toList()));

        title.setTitleStatus(PENDING);
        titleRepository.save(title);
        eventPublisher.publishEvent(
            TitleAccepted.builder().id(title.getTitleId().getId()).username(username).build());
      }
    }
  }

  void validateAllocation(List<Allocation> allocations, Category category) {
    if (CollectionUtils.isEmpty(allocations)) {
      throw new BusinessException(
          String.format(
              "No active allocations found for title with category %s", category.getCode()));
    }

    List<String> allocationsHaveZeroInitialQty =
        allocations.stream()
            .filter(allocation -> allocation.getInitialQuantity() <= 0)
            .map(
                allocation ->
                    String.format(
                        "%s %s (%s)",
                        allocation
                            .getCustomerStandingOrder()
                            .getStandingOrder()
                            .getStandingOrderNumber(),
                        allocation.getCustomerStandingOrder().getCustomer().getCode(),
                        allocation.getCategories().stream()
                            .map(Category::getCode)
                            .collect(Collectors.toList())))
            .collect(Collectors.toList());

    if (CollectionUtils.isNotEmpty(allocationsHaveZeroInitialQty)) {
      throw new BusinessException(
          String.format(
              "Standing Order selection contain zero quantity allocation: %s",
              allocationsHaveZeroInitialQty));
    }
  }

  void validateProductSelection(List<ProductAggregated> products) {
    List<String> invalidProductSelectionMessage =
        products.stream()
            .collect(
                Collectors.groupingBy(
                    p -> p.getMatchedProduct().getFormat(), Collectors.counting()))
            .entrySet()
            .stream()
            .filter(e -> e.getValue() > 1)
            .map(f -> String.format("%s product(s) selected for %s.", f.getValue(), f.getKey()))
            .collect(Collectors.toList());
    if (CollectionUtils.isNotEmpty(invalidProductSelectionMessage)) {
      throw new BusinessException(
          String.format(
              "Only one product can be selected for each format. %s",
              invalidProductSelectionMessage));
    }
  }

  void acceptAndRejectStandingOrders(Title title, List<StandingOrderAggregatedId> selectedIds) {
    title
        .getStandingOrderAggregatedList()
        .forEach(
            s -> {
              if (!selectedIds.contains(s.getStandingOrderAggregatedId())) {
                s.setStatus(StandingOrderAggregatedStatus.REJECTED);
              } else {
                s.setStatus(StandingOrderAggregatedStatus.ACCEPTED);
              }
            });
  }

  void acceptAndRejectProducts(Title title, Set<ProductAggregatedId> selectedIds) {
    title
        .getProductAggregatedList()
        .forEach(
            p -> {
              if (!selectedIds.contains(p.getProductAggregatedId())) {
                p.setStatus(ProductAggregatedStatus.REJECTED);
              } else {
                p.setStatus(ProductAggregatedStatus.ACCEPTED);
              }
            });
  }

  public Title rejectTitle(
      TitleId titleId, RejectTitleRequest rejectTitleRequest, String username) {
    Title title =
        titleRepository
            .findById(titleId)
            .orElseThrow(
                () -> new EntityNotFoundException(String.format("Title not found %s", titleId)));
    return rejectTitle(title, rejectTitleRequest, username);
  }

  private Title rejectTitle(Title title, RejectTitleRequest rejectTitleRequest, String username) {
    TitleId titleId = title.getTitleId();
    title =
        titleService.rejectTitle(
            title,
            RejectionReasonTypeId.of(rejectTitleRequest.getRejectionReasonType()),
            rejectTitleRequest.getOtherReason(),
            username,
            true);

    List<PendingOrderId> pendingOrderIds =
        pendingOrderService
            .findByTitleTitleIdAndOrderStatus(titleId, PendingOrderStatus.NEW)
            .stream()
            .map(PendingOrder::getPendingOrderId)
            .collect(Collectors.toList());
    pendingOrderBL.cancelPendingOrders(pendingOrderIds, username);
    return title;
  }

  public Title deferTitle(
      TitleId titleId, LocalDate deferredDate, boolean manualDeferring, String username) {
    Title title = titleService.deferTitle(titleId, deferredDate, manualDeferring);

    eventPublisher.publishEvent(
        TitleDeferred.builder()
            .id(title.getTitleId().getId())
            .deferredDate(title.getDeferredDate().atStartOfDay())
            .manualDeferred(manualDeferring)
            .username(username)
            .build());

    return title;
  }

  public Title unDeferTitle(TitleId titleId, String username) {
    Title title = titleService.findTitleById(titleId);
    return unDeferTitle(title, username);
  }

  public Title unDeferTitle(Title title, String username) {
    Title updatedTitle = titleService.unDeferTitle(title);

    eventPublisher.publishEvent(
        TitleUnDeferred.builder().id(updatedTitle.getTitleId().getId()).username(username).build());

    return updatedTitle;
  }

  public void rejectPausedTitlesDueAllocationMadeInactive(
      AllocationId allocationId, String username) {
    RejectionReasonTypeId reasonTypeId = RejectionReasonTypeId.of("ALLOCATION_MADE_INACTIVE");

    // handle related title
    titleService
        .findRelatedTitlesByStatusAndTypeAndAllocationId(Lists.newArrayList(PAUSED), allocationId)
        .forEach(
            affectedTitle -> {
              titleService.rejectTitle(affectedTitle.getTitleId(), reasonTypeId, null, username);
              relatedTitleBL.handle(
                  CreateRelatedTitleForPausedAllocation.from(affectedTitle.getTitleId()), null);
            });

    // handle original title
    titleService
        .findOriginalTitlesByStatusAndTypeAndAllocationId(Lists.newArrayList(PAUSED), allocationId)
        .forEach(
            affectedTitle -> {
              if (allocationService.isAllAllocationInactive(
                  affectedTitle.getStandingOrderAggregatedIds())) {
                titleService.rejectTitle(affectedTitle.getTitleId(), reasonTypeId, null, username);
              }
            });
  }

  public void transitionPausedTitlesDueAllocationMadeActive(AllocationId allocationId) {
    Allocation allocationMadeActive = allocationService.findById(allocationId);

    // handle related title
    titleService
        .findRelatedTitlesByStatusAndTypeAndAllocationId(
            Lists.newArrayList(PAUSED, PENDING), allocationId)
        .forEach(
            affectedTitle -> {
              if (affectedTitle.isPaused()) {
                titleService.update(affectedTitle.withTitleStatus(PENDING), "system");
              }
              // always create pending order on this event
              pendingOrderBL.create(
                  allocationMadeActive,
                  affectedTitle,
                  affectedTitle.getProductInfoFromAcceptedProducts());
            });

    // handle original title
    titleService
        .findOriginalTitlesByStatusAndTypeAndAllocationId(
            Lists.newArrayList(PAUSED, PENDING), allocationId)
        .forEach(
            affectedTitle -> {
              if (affectedTitle.isPaused()) {
                titleService.update(affectedTitle.withTitleStatus(NEW), "system");
              } else if (affectedTitle.isPending()) {
                // create pending order only if the title is in pending state
                List<ProductInfo> productsToProcess =
                    affectedTitle.getAcceptedProducts().stream()
                        .map(ProductAggregated::toProductInfo)
                        .collect(Collectors.toList());

                pendingOrderBL.create(allocationMadeActive, affectedTitle, productsToProcess);
              }
            });
  }

  public void deletePendingOrdersDueToAllocationPaused(AllocationId pausedAllocationId) {
    List<Title> affectedTitles =
        titleService.findTitlesByStatusAndTypeAndAllocationId(
            Lists.newArrayList(PENDING),
            Lists.newArrayList(TitleType.ORIGINAL, TitleType.RELATED),
            pausedAllocationId,
            true);

    for (Title affectedTitle : affectedTitles) {
      List<PendingOrder> pendingOrdersWithPausedAllocation =
          pendingOrderService.findAllNewPendingOrderByTitleIdAllocationId(
              affectedTitle.getTitleId(), pausedAllocationId);
      pendingOrderService.deletePendingOrders(pendingOrdersWithPausedAllocation);
      List<PendingOrder> remainingPendingOrders =
          pendingOrderService.findByTitle(affectedTitle.getTitleId());
      log.debug("remaining pending order after deletedL {}", remainingPendingOrders.size());
      if (CollectionUtils.isEmpty(remainingPendingOrders)) {
        titleService.update(affectedTitle.withTitleStatus(PAUSED), "system");
      }
    }
  }

  public Title undoTitle(TitleId titleId, String username) {
    Title title = titleService.findTitleById(titleId);
    return undoTitle(title, username);
  }

  private Title undoTitle(Title title, String username) {
    TitleId titleId = title.getTitleId();
    TitleStatus previousStatus = title.getTitleStatus();
    if (title.isPending() || title.isRejected()) {
      if (title.isPending()) {
        List<PendingOrder> pendingOrders =
            pendingOrderService.findByTitleTitleIdAndOrderStatus(titleId, PendingOrderStatus.NEW);
        pendingOrderBL.deletePendingOrders(pendingOrders);
        log.trace(
            "Deleting pending order due to undo title {} : {}",
            titleId,
            pendingOrders.stream()
                .map(PendingOrder::getPendingOrderId)
                .collect(Collectors.toList()));
      }
      return titleService.undoTitle(title, username);

    } else {
      throw new BusinessException(
          String.format("Cannot undo title with status %s", previousStatus));
    }
  }

  @Transactional
  public List<TitleId> bulkEdit(BulkUpdateTitleRequest bulkUpdateTitleRequest, String username) {
    List<Title> titles;
    if (CollectionUtils.isNotEmpty(bulkUpdateTitleRequest.getIncludedTitleIds())) {
      titles = titleRepository.findAllById(bulkUpdateTitleRequest.getIncludedTitleIds());
    } else {
      titles =
          titleRepository.searchTitle(
              bulkUpdateTitleRequest.getSearchRequest(),
              bulkUpdateTitleRequest.getExcludedTitleIds());
    }
    if (bulkUpdateTitleRequest.getUpdatedStatus() != null) {
      validateTitleStatus(titles, bulkUpdateTitleRequest.getUpdatedStatus());
    }
    if (StringUtils.isNotBlank(bulkUpdateTitleRequest.getUpdatedCategory())) {
      validateTitleCategory(titles, bulkUpdateTitleRequest.getUpdatedStatus());
    }
    for (Title title : titles) {
      if (bulkUpdateTitleRequest.getUpdatedStatus() != null
          && title.getTitleStatus() != bulkUpdateTitleRequest.getUpdatedStatus()) {
        title = updateTitleStatus(title, bulkUpdateTitleRequest, username);
      }

      if (StringUtils.isNotBlank(bulkUpdateTitleRequest.getUpdatedCategory())) {
        updateTitleCategory(title, bulkUpdateTitleRequest.getUpdatedCategory(), username);
      }
    }
    List<TitleId> updatedTitleIds =
        titles.stream().map(Title::getTitleId).collect(Collectors.toList());
    log.info(
        "Updated title for title ids: {} with request {}", updatedTitleIds, bulkUpdateTitleRequest);
    return updatedTitleIds;
  }

  private void updateTitleCategory(Title title, String updatedCategory, String username) {
    Category category = title.getCategory();
    String currentCategory = category != null ? category.getCode() : null;
    if (!updatedCategory.equals(currentCategory)) {
      titleService.updateCategory(title, updatedCategory, username);
    }
  }

  private Title updateTitleStatus(
      Title title, BulkUpdateTitleRequest bulkUpdateTitleRequest, String username) {
    log.trace(
        "Updating title status for tile {} to status {}",
        title.getTitleId(),
        bulkUpdateTitleRequest.getUpdatedStatus());
    TitleStatus updatedStatus = bulkUpdateTitleRequest.getUpdatedStatus();
    if (title.getTitleStatus() == DEFERRED && updatedStatus == NEW) {
      return unDeferTitle(title, username);
    } else if ((title.getTitleStatus() == PENDING || title.getTitleStatus() == REJECTED)
        && updatedStatus == NEW) {
      return undoTitle(title, username);
    } else if (updatedStatus == REJECTED) {
      return rejectTitle(title, bulkUpdateTitleRequest.getRejectTitleRequest(), username);
    } else {
      throw new BusinessException(String.format("Cannot change the status to %s", updatedStatus));
    }
  }

  private void validateTitleCategory(List<Title> titles, TitleStatus updatedStatus) {
    if (updatedStatus != null && updatedStatus != NEW) {
      throw new BusinessException(
          String.format("Cannot change the category with updated status %s", updatedStatus));
    } else if (updatedStatus == null) {
      for (Title title : titles) {
        int pendingOrderCount =
            pendingOrderService.findAllValidPendingOrdersByTitle(title.getTitleId()).size();
        if (!title.isNew() || (title.isPending() && pendingOrderCount > 1)) {
          throw new BusinessException(
              "Assigning category only applied to a NEW title or PENDING title that has no pending orders.");
        }
      }
    }
  }

  private void validateTitleStatus(List<Title> titles, TitleStatus updatedStatus) {

    switch (updatedStatus) {
      case NEW:
        validateTitleStatus(titles, List.of(NEW, PENDING, REJECTED, DEFERRED));
        break;
      case REJECTED:
        validateTitleStatus(titles, List.of(NEW, PENDING, PAUSED, DEFERRED, REJECTED));
        break;
      default:
        throw new BusinessException(String.format("Cannot change the status to %s", updatedStatus));
    }
  }

  private void validateTitleStatus(List<Title> titles, List<TitleStatus> validStatuses) {
    Set<TitleStatus> invalidStatuses =
        titles.stream()
            .map(Title::getTitleStatus)
            .filter(titleStatus -> !validStatuses.contains(titleStatus))
            .collect(Collectors.toSet());
    if (CollectionUtils.isNotEmpty(invalidStatuses)) {
      throw new BusinessException(
          String.format(
              "Cannot change the status of included %s titles",
              invalidStatuses.stream().map(Enum::toString).collect(Collectors.joining(", "))));
    }
  }
}

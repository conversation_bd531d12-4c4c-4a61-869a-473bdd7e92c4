package au.com.peterpal.selecting.standingorders.admin.dto;

import au.com.peterpal.selecting.standingorders.ext.budget.entity.FundType;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.*;

@With
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BudgetResponsePage {
  @NotNull @NonNull private FundId fundId;
  private String fundCode;
  private String fundName;
  private FundType fundType;
  private BigDecimal budgetAmount;
  private BigDecimal budgetAmountYearly;
  private BigDecimal budgetAmountMonthly;
  private BigDecimal onOrderAmount;
  private BigDecimal invoicedAmount;
  private BigDecimal remainingAmountMonthly;
  private BigDecimal utilisedMonthly;
  private BigDecimal remainingAmountYearly;
  private BigDecimal utilisedYearly;
  private String customerCode;
  public static BudgetResponsePage from(
      SearchBudgetDetail searchDetail,
      BigDecimal remainingAmountMonthly,
      BigDecimal utilisedMonthly,
      BigDecimal remainingAmountYearly,
      BigDecimal utilisedYearly,
      BigDecimal budgetAmountYearly,
      BigDecimal budgetAmountMonthly) {
    return BudgetResponsePage.builder()
        .fundId(searchDetail.getFundId())
        .fundCode(searchDetail.getFundCode())
        .fundName(searchDetail.getFundName())
        .fundType(searchDetail.getFundType())
        .budgetAmount(searchDetail.getBudgetAmount())
        .onOrderAmount(searchDetail.getOnOrderAmount())
        .invoicedAmount(searchDetail.getInvoicedAmount())
        .remainingAmountMonthly(remainingAmountMonthly)
        .utilisedMonthly(utilisedMonthly)
        .remainingAmountYearly(remainingAmountYearly)
        .utilisedYearly(utilisedYearly)
        .customerCode(searchDetail.getCustomerCode())
        .budgetAmountYearly(budgetAmountYearly)
        .budgetAmountMonthly(budgetAmountMonthly)
        .build();
  }
}

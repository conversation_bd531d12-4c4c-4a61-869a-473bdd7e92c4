package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationRepository;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.titles.dto.CreateRelatedTitleForPausedAllocation;
import au.com.peterpal.selecting.standingorders.titles.dto.CreateRelatedTitleByStandingOrder;
import au.com.peterpal.selecting.standingorders.titles.entity.*;
import au.com.peterpal.selecting.standingorders.titles.events.TitleCreated;
import au.com.peterpal.selecting.standingorders.titles.events.TitleProcessed;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

@Log4j2
@Service
@RequiredArgsConstructor
public class RelatedTitleBL {

  private final TitleService titleService;
  private final RelatedTitleRepository relatedTitleRepository;
  private final AllocationRepository allocationRepository;
  private final EventPublisher eventPublisher;
  private final TitleNumberGenerator titleNumberGenerator;

  @EventListener
  public void handle(TitleProcessed event) {
    TitleId titleId = TitleId.of(event.getId());
    Title relatedTitle =
        handle(CreateRelatedTitleForPausedAllocation.from(titleId), event.getUsername());
    log.debug("Related title is created? {},  from original title {} ", relatedTitle, titleId);
  }

  public Title handle(CreateRelatedTitleForPausedAllocation request, String username) {
    Title relatedTitle = null;
    Title originalTitle = titleService.findOriginalTitleById(request.getOriginalTitleId());

    List<Allocation> pausedAllocationsWithMatchedCategory =
        findPausedAllocation(originalTitle).stream()
            .filter(allocation -> allocation.containsCategory(originalTitle.getCategory()))
            .collect(Collectors.toList());

    if (CollectionUtils.isNotEmpty(pausedAllocationsWithMatchedCategory)) {
      log.debug(
          "Paused allocation found, related title will be created from original title {}",
          request.getOriginalTitleId());
      relatedTitle = createRelatedTitleFrom(originalTitle, null, username);
      log.debug(
          "Related title is created? {},  from original title {} ",
          relatedTitle,
          request.getOriginalTitleId());
    } else {
      log.trace("No paused allocation.");
    }

    return relatedTitle;
  }

  public Title handle(CreateRelatedTitleByStandingOrder request, String username) {
    Title relatedTitle = null;
    Title originalTitle = titleService.findOriginalTitleById(request.getOriginalTitleId());
    Optional<StandingOrderAggregated> matchedStandingOrder =
        originalTitle.findMatchedStandingOrder(request.getTargetStandingOrderId());
    if (matchedStandingOrder.isPresent()) {
      relatedTitle =
          createRelatedTitleFrom(originalTitle, request.getRelatedTitleStatus(), username);
      log.debug(
          "Related title is created? {},  from original title {} ",
          relatedTitle,
          request.getOriginalTitleId());
    } else {
      log.trace("No matched standing order found.");
    }

    return relatedTitle;
  }

  public Title createRelatedTitleFrom(
      Title originalTitle, TitleStatus relatedTitleStatus, String username) {
    final Title relatedTitle = copyFromOriginalTitle(originalTitle, relatedTitleStatus);
    eventPublisher.publishEvent(TitleCreated.from(relatedTitle, username));
    relatedTitleRepository.saveAndFlush(
        RelatedTitle.builder()
            .relatedTitleId(new RelatedTitleId())
            .originalTitle(originalTitle)
            .relatedTitle(relatedTitle)
            .build());
    return relatedTitle;
  }

  List<Allocation> findPausedAllocation(Title originalTitle) {
    List<StandingOrderId> acceptedStandingOrders =
        Optional.ofNullable(originalTitle)
            .filter(Title::isProcessed)
            .map(Title::getAcceptedStandingOrders)
            .orElse(Lists.newArrayList())
            .stream()
            .map(StandingOrderAggregated::getStandingOrder)
            .map(StandingOrder::getStandingOrderId)
            .collect(Collectors.toList());

    return CollectionUtils.isNotEmpty(acceptedStandingOrders)
        ? allocationRepository.findAllPausedAllocations(acceptedStandingOrders)
        : Lists.newArrayList();
  }

  Title copyFromOriginalTitle(Title originalTitle, TitleStatus relatedTitleStatus) {
    // copyOriginalTitleHeader
    Title relatedTitle = Title.copy(originalTitle, titleNumberGenerator.getNextTitleNumber());
    if (Objects.nonNull(relatedTitleStatus)) {
      relatedTitle.setTitleStatus(relatedTitleStatus);
    }

    // copyMatchProductsAndMatchStandingOrders
    originalTitle
        .getMatchedProducts()
        .forEach(
            originalMatchedProduct -> {
              MatchedProduct matchedProduct =
                  MatchedProduct.copy(originalMatchedProduct, relatedTitle);

              originalMatchedProduct
                  .getMatchedStandingOrders()
                  .forEach(
                      mso ->
                          matchedProduct.addMatchedStandingOrder(
                              MatchedStandingOrder.copy(mso, matchedProduct)));

              relatedTitle.addMatchedProduct(matchedProduct);
            });

    // copyAggregateProducts
    originalTitle
        .getAcceptedProducts()
        .forEach(
            p -> {
              MatchedProduct matchedProduct =
                  relatedTitle
                      .findActiveMatchedProductByIsbn(p.getMatchedProduct().getIsbn())
                      .orElse(null);
              relatedTitle.addProductAggregated(
                  ProductAggregated.copyAcceptedProductAggregated(relatedTitle, matchedProduct));
            });

    // copyAggregateStandingOrders
    originalTitle
        .getAcceptedStandingOrders()
        .forEach(
            soAggregated ->
                relatedTitle.addStandingOrderAggregated(
                    StandingOrderAggregated.copyAcceptedMatchStandingOrderAggregated(
                        soAggregated, relatedTitle)));

    return titleService.update(relatedTitle, "system");
  }

  public void deleteRelatedTitle(Title relatedTitle) {
    relatedTitleRepository.findByRelatedTitleTitleId(relatedTitle.getTitleId());
    titleService.delete(relatedTitle.getTitleId());
  }
}

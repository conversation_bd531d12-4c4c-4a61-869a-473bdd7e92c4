package au.com.peterpal.selecting.standingorders.catalog.model;

/**
 * @docRoot
 * Based on Onix List 15
 */
public enum TitleTypeCode {

	UNDEF ("00", "Undefined", ""),
	TITLE ("01", "Distinctive title(book); cover title (serial)", "The full text of the distinctive title of the item, without abbreviation or abridgement. For books, where the title alone is not distinctive, elements may be taken from a set or series title and part number etc to create a distinctive title. Where the item is an omnibus edition containing two or more works by the same author, and there is no separate combined title, a distinctive title may be constructed by concatenating the individual titles, with suitable punctuation, as in Pride and prejudice / Sense and sensibility / Northanger Abbey ."),
	SERIAL ("02", "ISSN key title of serial", "Serials only"),
	ISSN ("02", "ISSN key title of serial", "Serials only"),
	ORIGLANG ("03", "Title in original language", "Where the subject of the ONIX record is a translated item"),
	ACRONYM ("04", "Title acronym", "For serials: JACM = Journal of the Association for Computing Machinery"),
	ABBREV ("05", "Abbreviated title", "An abbreviated form of Title Type 01"),
	OTHLANG ("06", "Title in other language", "A translation of Title Type 01 into another language"),
	THEMATIC ("07", "Thematic title of journal issue", "Serials only: when a journal issue is explicitly devoted to a specified topic"),
	FORMER ("08", "Former title", "Books or serials: when an item was previously published under another title"),
	DIST ("10", "Distributor's title", "For books: the title carried in a book distributor's title file: frequently incomplete, and may include elements not properly part of the title"),
	ALTCOV ("11", "Alternative title on cover", "An alternative title that appears on the cover of a book"),
	ALTBACK ("12", "Alternative title on back", "An alternative title that appears on the back of a book"),
	EXPANDED ("13", "Expanded title of text book", "An expanded form of the title of a school text book with (eg) grade and type and other details added to make the title meaningful, where otherwise it would comprise only the curriculum subject. This title type is required for submissions to the Spanish ISBN Agency.");

	private final String code;
	private final String description;
	private final String notes;

	TitleTypeCode(String code, String description, String notes) {
		this.code = code;
		this.description = description;
		this.notes = notes;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public String getNotes() {
		return notes;
	}

	public static TitleTypeCode mapOnixCode(String onixCode) {
		for (TitleTypeCode value : TitleTypeCode.values()) {
			if (value.code.equals(onixCode)) {
				return value;
			}
		}
		throw new IllegalArgumentException("Invalid " + TitleTypeCode.class.getSimpleName() + ": " + onixCode);
	}

	@Override
	public String toString() {
		return this.description;
	}
}

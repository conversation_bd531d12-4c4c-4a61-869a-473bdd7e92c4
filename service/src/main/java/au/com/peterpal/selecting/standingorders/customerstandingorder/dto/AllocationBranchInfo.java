package au.com.peterpal.selecting.standingorders.customerstandingorder.dto;

import au.com.peterpal.selecting.standingorders.allocation.dto.AllocationInfo;
import lombok.*;

import java.util.List;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class AllocationBranchInfo {
  private AllocationInfo allocation;
  private List<AllocationInfo> branches;
}

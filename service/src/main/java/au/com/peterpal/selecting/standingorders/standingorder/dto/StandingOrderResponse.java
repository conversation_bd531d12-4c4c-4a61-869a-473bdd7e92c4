package au.com.peterpal.selecting.standingorders.standingorder.dto;

import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.Set;

@With
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StandingOrderResponse {

  @NotNull @NonNull private StandingOrderId standingOrderId;
  private StandingOrderStatus status;
  private String standingOrderNumber;
  private String description;
  private String notes;
  private Set<Customer> customers;
  private Set<Category> allocationCategories;


  public static StandingOrderResponse from(StandingOrder standingOrder) {
    Affirm.of(standingOrder).notNull("StandingOrder must not be null");

    return StandingOrderResponse.builder()
        .description(standingOrder.getDescription())
        .notes(standingOrder.getNotes())
        .standingOrderId(standingOrder.getStandingOrderId())
        .standingOrderNumber(standingOrder.getStandingOrderNumber())
        .status(standingOrder.getStandingOrderStatus())
        .build();
  }
}

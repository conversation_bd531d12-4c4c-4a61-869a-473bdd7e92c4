package au.com.peterpal.selecting.standingorders.customerstandingorder.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.Value;
import lombok.With;

@With
@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class AllocationRequest {

  @NotNull
  @NonNull
  private CustomerId customerId;

  @NotNull
  @NonNull
  private StandingOrderId standingOrderId;

  private FundId fundId;

  private AllocationPreferenceId allocationPreferenceId;

  private List<String> categories;

  @Builder.Default
  private AllocationStatus status = AllocationStatus.ACTIVE;

  private String customerReference;

  private String deliveryInstructions;

  private String notes;

  private String collectionCode;

  @Builder.Default Boolean rematchAllocationAfterCreate = Boolean.FALSE;
}

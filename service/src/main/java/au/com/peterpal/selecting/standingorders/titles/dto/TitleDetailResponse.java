package au.com.peterpal.selecting.standingorders.titles.dto;

import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.Format;
import au.com.peterpal.selecting.standingorders.standingorder.model.*;
import au.com.peterpal.selecting.standingorders.titles.entity.*;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@With
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TitleDetailResponse {
  private TitleId titleId;
  private String titleNumber;
  private String title;
  private String subtitle;
  private String personName;
  private String series;
  private String imprint;
  private String category;
  private TitleStatus titleStatus;
  private LocalDateTime dateAdded;
  private LocalDate deferredDate;
  private LocalDate processedDate;
  private RejectionReasonType rejectionReasonType;
  private String rejectionOtherReason;

  private List<Product> products;
  private List<StandingOrder> standingOrders;
  private Set<Category> allocationCategories;
  public static TitleDetailResponse from(Title title, Map<StandingOrderId, TitleStandingOrderAllocationCategory> allocationCategories) {
    return TitleDetailResponse.builder()
        .titleId(title.getTitleId())
        .title(title.getTitle())
        .subtitle(title.getSubtitle())
        .personName(title.getPersonName())
        .imprint(title.getImprint())
        .category(Optional.ofNullable(title.getCategory()).map(Category::getCode).orElse(null))
        .titleStatus(title.getTitleStatus())
        .dateAdded(title.getDateAdded())
        .processedDate(title.getProcessedDate())
        .deferredDate(title.getDeferredDate())
        .rejectionReasonType(title.getRejectionReasonType())
        .rejectionOtherReason(title.getRejectionOtherReason())
        .products(
            title.getProductAggregatedList().stream()
                .map(TitleDetailResponse::productFrom)
                .sorted(Comparator.comparing(Product::getPublicationDate))
                .collect(Collectors.toList()))
        .standingOrders(
            title.getStandingOrderAggregatedList().stream()
                .map(
                    t ->
                        TitleDetailResponse.standingOrderFrom(
                            t,
                            Optional.ofNullable(
                                    allocationCategories.get(
                                        t.getStandingOrder().getStandingOrderId()))
                                .map(TitleStandingOrderAllocationCategory::getCategories)
                                .orElse(Collections.emptyList())))
                .collect(Collectors.toList()))
        .allocationCategories(
            allocationCategories.values().stream()
                .map(TitleStandingOrderAllocationCategory::getCategories)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet()))
        .titleNumber(title.getTitleNumber())
        .build();
  }

  private static StandingOrder standingOrderFrom(StandingOrderAggregated so, List<Category> categories) {
    return StandingOrder.builder()
        .standingOrderAggregatedId(so.getStandingOrderAggregatedId())
        .status(so.getStatus())
        .standingOrderNumber(so.getStandingOrder().getStandingOrderNumber())
        .standingOrderId(so.getStandingOrder().getStandingOrderId())
        .standingOrderDescription(so.getStandingOrder().getDescription())
        .notes(so.getStandingOrder().getNotes())
        .matchedTerms(so.getMatchedTermTuples())
        .categories(categories)
        .build();
  }

  public static Product productFrom(ProductAggregated productAggregated) {
    return Product.builder()
        .productAggregatedId(productAggregated.getProductAggregatedId())
        .matchedProductId(productAggregated.getMatchedProduct().getMatchedProductId())
        .category(
            Optional.ofNullable(productAggregated.getMatchedProduct().getCategory())
                .map(Category::getCode)
                .orElse(null))
        .productTitle(productAggregated.getMatchedProduct().getProductTitle())
        .subtitle(productAggregated.getMatchedProduct().getSubtitle())
        .series(productAggregated.getMatchedProduct().getSeries())
        .isbn(productAggregated.getMatchedProduct().getIsbn())
        .personName(productAggregated.getMatchedProduct().getPersonName())
        .imprint(productAggregated.getMatchedProduct().getImprint())
        .format(productAggregated.getMatchedProduct().getFormat())
        .status(productAggregated.getStatus())
        .publicationDate(productAggregated.getMatchedProduct().getPublicationDate())
        .build();
  }

  @With
  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  @Builder
  public static class Product {
    ProductAggregatedId productAggregatedId;
    MatchedProductId matchedProductId;
    private String category;
    private Integer catalogueId;
    private String productTitle;
    private String subtitle;
    private String series;
    private String isbn;
    private String personName;
    private String imprint;
    private LocalDate publicationDate;
    private String editionStatement;
    private Integer height;
    private Integer width;
    private String subjectCode;
    private String audience;
    private String price;
    private Format format;
    private ProductAggregatedStatus status;
    private Integer numberOfPages;
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  @Builder
  public static class StandingOrder {
    private StandingOrderAggregatedId standingOrderAggregatedId;
    private StandingOrderAggregatedStatus status;
    private StandingOrderId standingOrderId;
    private String standingOrderNumber;
    private String standingOrderDescription;
    private String notes;
    private List<Category> categories;
    private Set<MatchedTermTuple> matchedTerms;
  }
}

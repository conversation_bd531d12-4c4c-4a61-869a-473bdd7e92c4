package au.com.peterpal.selecting.standingorders.ext.supplier.dto;

import au.com.peterpal.selecting.standingorders.ext.supplier.entity.Supplier;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.SupplierId;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import lombok.*;

import javax.validation.constraints.NotNull;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class SupplierResponse {

  @NotNull @NonNull private SupplierId value;
  private String label;

  public static SupplierResponse from(Supplier supplier) {
    Affirm.of(supplier).notNull("Supplier must not be null");

    return SupplierResponse.builder()
        .label(supplier.getCode())
        .value(supplier.getSupplierId())
        .build();
  }
}

package au.com.peterpal.selecting.standingorders.profile.control;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreferenceId;

public interface CustomerProfileRepositoryCustom {
  ReleasePreference findReleasePreference(
      AllocationPreferenceId allocationPreferenceId,
      ReleasePreferenceId releasePreferenceId);
}

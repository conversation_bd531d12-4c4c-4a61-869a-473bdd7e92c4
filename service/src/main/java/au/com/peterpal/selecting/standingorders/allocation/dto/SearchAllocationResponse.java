package au.com.peterpal.selecting.standingorders.allocation.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import lombok.Data;

@Data
public class SearchAllocationResponse {
  private AllocationId allocationId;
  private StandingOrderId standingOrderId;
  private AllocationPreferenceId allocationPreferenceId;
  private CustomerStandingOrderId customerStandingOrderId;
  private CustomerResponse customer;
  private FundResponse fund;
  private String category;
  private AllocationStatus status;
  private String customerReference;
  private String deliveryInstructions;
  private String notes;

  @Data
  public static class CustomerResponse {
    private CustomerId customerId;
    private String code;
  }

  @Data
  public static class FundResponse {
    private FundId fundId;
    private String code;
  }
}

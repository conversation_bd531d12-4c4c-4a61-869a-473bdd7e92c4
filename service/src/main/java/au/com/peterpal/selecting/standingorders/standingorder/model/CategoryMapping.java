package au.com.peterpal.selecting.standingorders.standingorder.model;

import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@Entity
@Table(name = "category_mapping")
public class CategoryMapping {

  @Id
  private String subject;

  @ManyToOne
  private Category category;

  private Integer priority;
}

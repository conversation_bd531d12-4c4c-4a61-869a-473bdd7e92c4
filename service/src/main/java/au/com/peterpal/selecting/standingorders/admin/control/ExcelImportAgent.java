package au.com.peterpal.selecting.standingorders.admin.control;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import au.com.peterpal.selecting.standingorders.utils.BooleanAffirm;
import au.com.peterpal.selecting.standingorders.utils.StringAffirm;
import java.io.IOException;
import java.io.InputStream;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

@Getter
@Setter
@Log4j2
public abstract class ExcelImportAgent {
  private final static String SPREAD_SHEET_CT = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

  private InputStream input;
  protected Workbook workbook;
  protected ProcessReport report;
  protected boolean test;

  private ExcelImportAgent() {}

  public ExcelImportAgent(MultipartFile file) {
    input = getInputStream(file);
    report = new ProcessReport();
    loadWorkbook(file);
  }

  public ExcelImportAgent importData() {
    return importData(false);
  }

  public ExcelImportAgent importData(boolean test) {
    long startTime = System.currentTimeMillis();
    this.test = test;
    doImport();
    report.setTotalTime(System.currentTimeMillis() - startTime);
    close();
    return this;
  }

  protected abstract void doImport();

  protected String getString(Cell cell, String msg) {
    Affirm.of(cell).notNull(StringAffirm.of(msg).hasText() ? msg : "Cell must not be null");
    BooleanAffirm.of(CellType.STRING.equals(cell.getCellType()))
            .state(String.format("Cell at index %d must be of type STRING", cell.getColumnIndex()));

    return Optional.ofNullable(cell.getStringCellValue())
      .filter(str -> !str.trim().isEmpty())
      .orElseThrow(() ->
              new BusinessException(StringAffirm.of(msg).hasText() ? msg : "String value must not be blank"));
  }

  protected String getString(Cell cell) {
    return Optional.ofNullable(cell)
      .filter(c -> CellType.STRING.equals(c.getCellType()))
      .map(c -> c.getStringCellValue())
      .orElse(null);
  }

  protected String toString(Row row) {
    DataFormatter fmt = new DataFormatter();
    return Optional.ofNullable(row)
      .map(r -> {
        StringBuffer sb = new StringBuffer();
        for (Cell c : r) {
          if (sb.length() > 0) {
            sb.append(" - ");
          }
          sb.append(fmt.formatCellValue(c));
        }
        return sb.toString();
      })
      .orElse("");
  }

  protected int getInt(Cell cell) {
    return Optional.ofNullable(cell)
        .filter(c -> CellType.NUMERIC.equals(c.getCellType()))
        .map(c -> Optional.ofNullable((int)c.getNumericCellValue())
            .orElse(0)
        )
        .orElse(0);
  }

  protected int getInt(Cell cell, String msg) {
    Affirm.of(cell).notNull("Cell must not be null");
    BooleanAffirm.of(CellType.NUMERIC.equals(cell.getCellType()))
            .state(String.format("Cell at index %d must be of type NUMERIC", cell.getColumnIndex()));

    return Optional.ofNullable((int)cell.getNumericCellValue())
      .orElseThrow(() -> new BusinessException(StringAffirm.of(msg).hasText() ? msg : "Numeric value must not be null"));
  }

  protected int getIntValue(Cell cell) {
    return Optional.ofNullable(cell)
        .map(c -> {
          if (CellType.STRING.equals(cell.getCellType())) {
            return Optional.ofNullable(
                Integer.valueOf(StringAffirm.of(c.getStringCellValue()).hasText() ? c.getStringCellValue() : "0")).orElse(0);
          } else if (CellType.NUMERIC.equals(cell.getCellType())) {
            return Optional.ofNullable((int)c.getNumericCellValue()).orElse(0);
          } else {
            log.warn(String.format("Wrong cell type %s, expecting STRING of NUMERIC", cell.getCellType()));
            return 0;
          }
        })
        .orElse(0);
  }

  protected Sheet getFirstSheet() {
    try {
      return workbook.getSheetAt(0);
    } catch(Exception ex) {
      log.error("Importing sheet for allocation preferences", ex);
      throw new BusinessException("Allocation preference file must contain at least one sheet.");
    }
  }

  private void loadWorkbook(MultipartFile file) {
    try {
      if (isXls(file.getOriginalFilename())) {
        workbook = new HSSFWorkbook(input);
      } else if (isXlsx(file.getOriginalFilename())) {
        ZipSecureFile.setMinInflateRatio(0);
        workbook = new XSSFWorkbook(input);
      } else {
        throw new BusinessException("Unsuported file format");
      }
    } catch(IOException ex) {
      log.error("Error creating workbook from input stream ", ex);
      throw new BusinessException("Error creating workbook");
    }
  }

  private InputStream getInputStream(MultipartFile file) {
    return Optional.ofNullable(file)
        .filter(f -> !f.isEmpty())
        .filter(f-> isSpreadSheet(f.getContentType()))
        .map(f -> {
          try {
            return f.getInputStream();
          } catch (IOException ex) {
            log.error("Error reading data for file ", ex);
            throw new BusinessException(String.format("Error reading file %s", f.getOriginalFilename()));
          }
        })
        .get();
  }

  private boolean isSpreadSheet(String contentType) {
    return Optional.ofNullable(contentType)
        .filter(s -> !s.isEmpty())
        .map(s -> s.equals(SPREAD_SHEET_CT))
        .orElseThrow(() -> new BusinessException(String.format("Can not handle content type %s. Expects %s",
            contentType == null ? "null" : contentType.isEmpty() ? "empty" : contentType, SPREAD_SHEET_CT)));
  }

  private boolean isXls(String str) {
    return Optional.ofNullable(str)
        .filter(s -> !s.isEmpty())
        .map(s -> s.endsWith(".xls"))
        .orElse(false);
  }

  private boolean isXlsx(String str) {
    return Optional.ofNullable(str)
        .filter(s -> !s.isEmpty())
        .map(s -> s.endsWith(".xlsx"))
        .orElse(false);
  }

  private void close() {
    try {
      if (workbook != null) {
        workbook.close();
      }
    } catch(Exception ex) {
      log.warn("Exception closing workbook", ex);
    }
  }
}

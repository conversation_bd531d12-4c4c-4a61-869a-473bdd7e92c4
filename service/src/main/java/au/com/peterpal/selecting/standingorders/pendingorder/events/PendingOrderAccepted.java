package au.com.peterpal.selecting.standingorders.pendingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class PendingOrderAccepted extends DomainEvent {

  public static PendingOrderAccepted from(PendingOrderId pendingOrderId, String username) {
    return PendingOrderAccepted.builder().id(pendingOrderId.getId()).username(username).build();
  }
}

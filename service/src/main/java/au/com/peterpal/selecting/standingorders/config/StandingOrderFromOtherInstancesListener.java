package au.com.peterpal.selecting.standingorders.config;

import au.com.peterpal.selecting.standingorders.standingorder.control.MatchingService;
import au.com.peterpal.selecting.standingorders.standingorder.dto.StandingOrderUpdatedMessage;
import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderUpdated;
import java.util.UUID;
import javax.jms.ConnectionFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.context.IntegrationContextUtils;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.dsl.channel.MessageChannels;
import org.springframework.integration.handler.LoggingHandler;
import org.springframework.integration.jms.dsl.Jms;
import org.springframework.messaging.MessageChannel;

@Log4j2
@RequiredArgsConstructor
@Configuration
@EnableIntegration
public class StandingOrderFromOtherInstancesListener {

  @Value("${events.standing-orders.channel:standing-orders-to-standing-orders-events-topic}")
  private String topicName;

  private static final String CLIENT_ID = "standing-orders-receiving:standing-orders";


  private final ConnectionFactory connectionFactory;
  private final MatchingService matchingService;

//  public StandingOrderFromOtherInstancesListener(ConnectionFactory connectionFactory,
//      MatchingService matchingService) {
//    this.connectionFactory = connectionFactory;
//    this.matchingService = matchingService;
//  }

  @Bean
  public MessageChannel updateStandingOrderChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow updatesFromOtherInstancesListener() {
    return IntegrationFlows.from(
            Jms.messageDrivenChannelAdapter(this.connectionFactory)
                .destination(topicName)
                .configureListenerContainer(
                    spec -> {
                      spec.pubSubDomain(true);
                      spec.subscriptionDurable(false);
                      spec.clientId(CLIENT_ID + "_" + UUID.randomUUID());
                    })
                .errorChannel(IntegrationContextUtils.ERROR_CHANNEL_BEAN_NAME))
        .log(LoggingHandler.Level.INFO,
            msg ->
                String.format(
                    "Received standing orders message from other instance -- Payload: %s Headers: %s",
                    msg.getPayload(), msg.getHeaders()))
        .channel(updateStandingOrderChannel())
        .get();
  }

  @Bean
  public IntegrationFlow updateStandingOrders() {
    return IntegrationFlows.from(updateStandingOrderChannel())
        .transform(Transformers.fromJson(StandingOrderUpdatedMessage.class))
        .<StandingOrderUpdatedMessage>handle(
            ((payload, headers) -> matchingService.updateObserversFromOtherInstances(payload)))
        .log(message -> "Standing Order updated: " + message)
        .get();
  }
}

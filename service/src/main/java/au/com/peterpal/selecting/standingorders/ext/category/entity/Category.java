package au.com.peterpal.selecting.standingorders.ext.category.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@RequiredArgsConstructor(staticName = "of")
@Builder
@With
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(
    name = "category")
public class Category {

  @EqualsAndHashCode.Include @ToString.Include @NonNull @NotNull @EmbeddedId
  private CategoryId categoryId;

  @NotEmpty @NotNull private String code;

  @NotEmpty @NotNull private String description;

  @NonNull
  @NotNull
  @Enumerated(EnumType.STRING)
  private CategoryStatus status;

}

package au.com.peterpal.selecting.standingorders.customerstandingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class NoteAdded extends DomainEvent {

  private Allocation allocation;

  public static NoteAdded from(Allocation allocation, String username) {

    return NoteAdded.builder()
        .id(allocation.getAllocationId().getId())
        .allocation(allocation)
        .username(username)
        .build();
  }
}

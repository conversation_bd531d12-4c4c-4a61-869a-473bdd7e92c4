package au.com.peterpal.selecting.standingorders.standingorder.dto;

import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StandingOrderCategoryResponse {
    private StandingOrderId standingOrderId;
    private Category category;

}

package au.com.peterpal.selecting.standingorders.allocation.model;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.UUID;
import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;

@Embeddable
public class ReleaseId extends UuidEntityId {
  public static ReleaseId of(@NotEmpty UUID id) {
    return new ReleaseId(id);
  }

  public static ReleaseId of(@NotEmpty String id) {
    return new ReleaseId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public ReleaseId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public ReleaseId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public ReleaseId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }

}

package au.com.peterpal.selecting.standingorders.ext.customer.boundary;

import au.com.peterpal.selecting.standingorders.ext.branch.control.BranchSyncService;
import au.com.peterpal.selecting.standingorders.ext.branch.control.dto.BranchMessage;
import au.com.peterpal.selecting.standingorders.ext.budget.boundary.dto.BudgetMessage;
import au.com.peterpal.selecting.standingorders.ext.budget.boundary.dto.BudgetTargetMessage;
import au.com.peterpal.selecting.standingorders.ext.budget.control.BudgetSyncService;
import au.com.peterpal.selecting.standingorders.ext.budget.control.BudgetTargetSyncService;
import au.com.peterpal.selecting.standingorders.ext.category.boundary.dto.CategoryMessage;
import au.com.peterpal.selecting.standingorders.ext.category.control.CategorySyncService;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerSyncService;
import au.com.peterpal.selecting.standingorders.ext.customer.control.dto.CustomerMessage;
import au.com.peterpal.selecting.standingorders.ext.fee.control.FeeSyncService;
import au.com.peterpal.selecting.standingorders.ext.fee.dto.FeeMessage;
import au.com.peterpal.selecting.standingorders.ext.fund.boundary.dto.FundMessage;
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundSyncService;
import com.google.common.collect.Lists;
import javax.jms.ConnectionFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.context.IntegrationContextUtils;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.dsl.channel.MessageChannels;
import org.springframework.integration.jms.dsl.Jms;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;

@Log4j2
@Configuration
@EnableIntegration
@RequiredArgsConstructor
public class CustomerEventsSync {

  private static final String MESSAGE_TYPE = "message_type";
  private static final String CLIENT_ID = "standing-orders:customer";
  private static final String CUSTOMER_CREATED = "customer-created";
  private static final String CUSTOMER_CHANGED = "customer-changed";
  private static final String FUND_CREATED = "fund-created";
  private static final String FUND_CHANGED = "fund-changed";

  private static final String BRANCH_CREATED = "location-created";

  private static final String BRANCH_CHANGED = "location-changed";
  private static final String BUDGET_CREATED = "budget-created";
  private static final String BUDGET_CHANGED = "budget-changed";
  private static final String BUDGET_TARGET_CREATED = "budget-target-created";
  private static final String BUDGET_TARGET_CHANGED = "budget-target-changed";
  private static final String CATEGORY_CREATED = "category-created";
  private static final String CATEGORY_CHANGED = "category-changed";
  private static final String FEE_CREATED = "fee-created";
  private static final String FEE_CHANGED = "fee-changed";

  private final ConnectionFactory connectionFactory;
  private final CustomerSyncService customerSyncService;
  private final FundSyncService fundSyncService;
  private final BudgetSyncService budgetSyncService;
  private final BranchSyncService branchSyncService;
  private final BudgetTargetSyncService budgetTargetSyncService;
  private final CategorySyncService categorySyncService;
  private final FeeSyncService feeSyncService;

  @Value("${events.customers.channel:customers-events-topic}")
  private String customerEventsChannelName;

  @Value("${events.customers.channel.subscription:customers-events}")
  private String customerEventsSubscriptionName;

  @Bean
  public MessageChannel customerChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow customerEventListener() {
    return IntegrationFlows.from(
            Jms.messageDrivenChannelAdapter(connectionFactory)
                .destination(customerEventsChannelName)
                .configureListenerContainer(
                    spec -> {
                      spec.pubSubDomain(true);
                      spec.durableSubscriptionName(customerEventsSubscriptionName);
                      spec.recoveryInterval(60000L);
                      spec.clientId(CLIENT_ID);
                    })
                .errorChannel(IntegrationContextUtils.ERROR_CHANNEL_BEAN_NAME))
        .channel(customerChannel())
        .get();
  }

  @Bean
  public IntegrationFlow customerProcessingFlow() {
    return IntegrationFlows.from(customerChannel())
        .log(msg -> String.format("Customer event received: %s", msg))
        .log(msg -> String.format("Routing event: %s", msg.getHeaders().get(MESSAGE_TYPE)))
        .filter(Message.class, m -> Lists.newArrayList(
            CUSTOMER_CREATED,
            CUSTOMER_CHANGED,
            FUND_CREATED,
            FUND_CHANGED,
            BRANCH_CREATED,
            BRANCH_CHANGED,
            BUDGET_CREATED,
            BUDGET_CHANGED,
            BUDGET_TARGET_CREATED,
            BUDGET_TARGET_CHANGED,
            CATEGORY_CREATED,
            CATEGORY_CHANGED,
            FEE_CREATED,
            FEE_CHANGED).contains(m.getHeaders().get(MESSAGE_TYPE, String.class)))
        .<String>route(
            "headers[" + MESSAGE_TYPE + "]",
            mapping ->
                mapping
                    .subFlowMapping(CUSTOMER_CREATED, syncCustomer())
                    .subFlowMapping(CUSTOMER_CHANGED, syncCustomer())
                    .subFlowMapping(FUND_CREATED, syncFund())
                    .subFlowMapping(FUND_CHANGED, syncFund())
                    .subFlowMapping(BRANCH_CREATED, syncBranch())
                    .subFlowMapping(BRANCH_CHANGED, syncBranch())
                    .subFlowMapping(BUDGET_CREATED, syncBudget())
                    .subFlowMapping(BUDGET_CHANGED, syncBudget())
                    .subFlowMapping(BUDGET_TARGET_CREATED, syncBudgetTarget())
                    .subFlowMapping(BUDGET_TARGET_CHANGED, syncBudgetTarget())
                    .subFlowMapping(CATEGORY_CREATED, syncCategory())
                    .subFlowMapping(CATEGORY_CHANGED, syncCategory())
                    .subFlowMapping(FEE_CREATED, syncFee())
                    .subFlowMapping(FEE_CHANGED, syncFee())
                    .defaultOutputToParentFlow())
        .get();
  }

  @Bean
  public IntegrationFlow syncCustomer() {
    return flow ->
        flow.transform(Transformers.fromJson(CustomerMessage.class))
            .<CustomerMessage>handle((msg, headers) -> customerSyncService.handle(msg))
            .log(msg -> "Customer synchronization completed");
  }

  @Bean
  public IntegrationFlow syncFund() {
    return flow ->
        flow.transform(Transformers.fromJson(FundMessage.class))
            .<FundMessage>handle((msg, headers) -> fundSyncService.handle(msg))
            .log(msg -> "Customer fund synchronization completed");
  }

  public IntegrationFlow syncBranch() {
    return flow ->
        flow.transform(Transformers.fromJson(BranchMessage.class))
            .<BranchMessage>handle((msg, headers) -> branchSyncService.handle(msg))
            .log(msg -> "Customer branch synchronization completed");
  }

  public IntegrationFlow syncBudget() {
    return flow ->
        flow.transform(Transformers.fromJson(BudgetMessage.class))
            .<BudgetMessage>handle((msg, headers) -> budgetSyncService.handle(msg))
            .log(msg -> "Customer budget synchronization completed");
  }
  public IntegrationFlow syncBudgetTarget() {
    return flow ->
        flow.transform(Transformers.fromJson(BudgetTargetMessage.class))
            .<BudgetTargetMessage>handle((msg, headers) -> budgetTargetSyncService.handle(msg))
            .log(msg -> "Customer budget target synchronization completed");
  }

  public IntegrationFlow syncCategory() {
    return flow ->
        flow.transform(Transformers.fromJson(CategoryMessage.class))
            .<CategoryMessage>handle((msg, headers) -> categorySyncService.handle(msg))
            .log(msg -> "Customer branch synchronization completed");
  }

  public IntegrationFlow syncFee() {
    return flow ->
        flow.transform(Transformers.fromJson(FeeMessage.class))
            .<FeeMessage>handle((msg, headers) -> feeSyncService.handle(msg))
            .log(msg -> "Fee synchronization completed");
  }
}

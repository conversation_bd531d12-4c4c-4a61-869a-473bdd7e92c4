package au.com.peterpal.selecting.standingorders.gateways.orders;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.dsl.channel.MessageChannels;
import org.springframework.integration.handler.LoggingHandler;
import org.springframework.integration.jms.dsl.Jms;
import org.springframework.messaging.MessageChannel;

import javax.jms.ConnectionFactory;

@Log4j2
@Configuration
@EnableIntegration
@RequiredArgsConstructor
public class OrderServiceOutboundIntegration {

  private final ConnectionFactory connectionFactory;
  private final OrderMessageTransformer orderMessageTransformer;

  @Value("${events.orders.channel:standing-orders-events-topic}")
  private String standingOrderToOrdersIntegrationQueue;

  private static final String MESSAGE_TYPE = "message_type";
  private static final String PENDING_ORDER_SUBMITTED = "pending-order-submitted";

  @Bean
  public MessageChannel pendingOrderSubmittedIntegrationChannel() {
    return MessageChannels.direct().get();
  }

  /**
   * outbound flow that responsible for sending pending-order-submitted message to order-service .
   *
   * @return
   */
  @Bean
  public IntegrationFlow createOrderOutboundFlow() {
    return IntegrationFlows.from(pendingOrderSubmittedIntegrationChannel())
        .enrichHeaders(
            headerEnricherSpec ->
                headerEnricherSpec.header(MESSAGE_TYPE, PENDING_ORDER_SUBMITTED, true))
        .transform(orderMessageTransformer)
        .transform(Transformers.toJson())
        .log(
            LoggingHandler.Level.DEBUG,
            message ->
                String.format("sending %s to %s", message, standingOrderToOrdersIntegrationQueue))
        .handle(
            Jms.outboundAdapter(connectionFactory)
                .destination(standingOrderToOrdersIntegrationQueue))
        .get();
  }
}

package au.com.peterpal.selecting.standingorders.standingorder.model;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@NoArgsConstructor(access = AccessLevel.PROTECTED, force = true)
@AllArgsConstructor(staticName = "of")
@Entity
@Table(name = "subject_map")
public class SubjectMapEntry {

  @Id
  @NonNull
  private String subject;

  private String category;
}

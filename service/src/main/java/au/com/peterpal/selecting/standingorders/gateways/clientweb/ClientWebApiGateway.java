package au.com.peterpal.selecting.standingorders.gateways.clientweb;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.catalog.model.ProductFormCode;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.ClientWebProductInfo;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.ProductData;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.CwProductInfo;
import au.com.peterpal.selecting.standingorders.standingorder.match.LegacyMatcher;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductMatchInfo;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Log4j2
@Component
public class ClientWebApiGateway {

  private final ClientWebApiClient clientWebApiClient;

  public ClientWebApiGateway(ClientWebApiClient clientWebApiClient) {
    this.clientWebApiClient = clientWebApiClient;
  }

  public ClientWebProductInfo getProductInfoById(Integer id) {
    Affirm.of(id).notNull("ProductId must not be null");
    return clientWebApiClient.getProductById(id);
  }

  public CwProductInfo searchProductByIsbn(String isbn) {
    CwProductInfo productInfo = clientWebApiClient.getProductByIsbn(isbn);
    if (Objects.isNull(productInfo)) {
      throw new ResourceNotFoundException(CwProductInfo.class, isbn);
    }
    return productInfo;
  }

  public Optional<CwProductInfo> findProductByIsbn(String isbn) {
    return Optional.ofNullable(clientWebApiClient.getProductByIsbn(isbn));
  }

  public ProductMatchInfo getProductInfoByIsbn(String isbn) {
    CwProductInfo cwProductInfo =
        findProductByIsbn(isbn)
            .orElseThrow(
                () ->
                    new BusinessException(
                        String.format("Can't find product with isbn %s in catalogue.", isbn)));
    ProductMatchInfo productInfo = ProductMatchInfo.from(cwProductInfo);
    return productInfo;
  }

  public ProductData searchProducts(
          String titleWithoutPrefix,
          String personNameInverted,
          String imprintName,
          String seriesTitle,
          Integer pageSize,
          Integer offset) {

    return clientWebApiClient.search(
            StringUtils.hasText(titleWithoutPrefix) ? titleWithoutPrefix : "",
            StringUtils.hasText(personNameInverted) ? personNameInverted : "",
            StringUtils.hasText(imprintName) ? imprintName : "",
            LegacyMatcher.validFormCodes.stream().map(ProductFormCode::getCode).collect(Collectors.toList()),
            StringUtils.hasText(seriesTitle) ? seriesTitle : "",
            pageSize != null ? pageSize : 10,
            offset != null ? offset : 0);
  }
}

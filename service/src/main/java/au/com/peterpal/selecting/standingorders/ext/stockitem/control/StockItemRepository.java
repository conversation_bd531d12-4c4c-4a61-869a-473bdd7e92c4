package au.com.peterpal.selecting.standingorders.ext.stockitem.control;

import au.com.peterpal.selecting.standingorders.ext.stockitem.entity.StockItem;
import au.com.peterpal.selecting.standingorders.ext.stockitem.entity.StockItemId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface StockItemRepository extends JpaRepository<StockItem, StockItemId> {

  Optional<StockItem> findByStockItemId(StockItemId id);

  List<StockItem> findByProductReference(String productRef);

  List<StockItem> findByTitleWithoutPrefixContainingAndAuthorContaining(String title, String author);
}

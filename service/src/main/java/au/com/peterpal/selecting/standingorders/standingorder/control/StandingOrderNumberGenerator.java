package au.com.peterpal.selecting.standingorders.standingorder.control;

import au.com.peterpal.common.sequencenumbergenerator.SequenceNumberService;
import au.com.peterpal.common.sequencenumbergenerator.SequenceStatus;
import java.util.Optional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class StandingOrderNumberGenerator {

  private static final String STANDING_ORDER_NUMBER = "STANDING_ORDER_NUMBER";

  @Value("${standingorders.standingOrderNumberMask:SO-%08d}")
  private String itemNumberMask;

  @Value("${standingorders.standingOrderNumberInitialValue:1}")
  private Long itemNumberInitialValue;

  private final SequenceNumberService sequenceNumberService;

  @EventListener
  public void onApplicationEvent(ContextRefreshedEvent event) {
    Optional<SequenceStatus> optionalStatus = sequenceNumberService.statusOf(1L, STANDING_ORDER_NUMBER);
    if (optionalStatus.isPresent()) {
      SequenceStatus status = optionalStatus.get();
      if (itemNumberInitialValue > status.getNumber() ||
          !itemNumberMask.equals(status.getFormat())) {
        sequenceNumberService.update(1L, STANDING_ORDER_NUMBER,
            itemNumberMask, status.getNumber(), itemNumberInitialValue);
      }
    } else {
      sequenceNumberService.create(1L, STANDING_ORDER_NUMBER, itemNumberMask, itemNumberInitialValue);
    }
  }

  public StandingOrderNumberGenerator(SequenceNumberService sequenceNumberService) {
    this.sequenceNumberService = sequenceNumberService;
  }

  public String getNextItemNumber() {
    return sequenceNumberService.next(1L, STANDING_ORDER_NUMBER);
  }
}

package au.com.peterpal.selecting.standingorders.utils;

import java.util.Optional;

/**
 * Utility class to assists in validating arguments that are strings.
 */
public class StringAffirm {
  private String str;

  public static StringAffirm of(String str) {
    return new StringAffirm(str);
  }

  private StringAffirm(String str) {
    this.str = str;
  }

  /**
   * Affirm that the given string contains valid text content; that is, it must not be {@code null} and must
   * contain at least one non-whitespace character.
   * <pre class="code">
   *     StringAffirm.of(name).hasText("'name' must not be empty");
   * </pre>
   * @param message the exception message to use
   * @throws IllegalArgumentException if the text does not contain valid text
   */
  public void hasText(String message) {
    String str = Optional.ofNullable(message)
        .filter(s -> !s.isEmpty())
        .orElse("String must not be blank");

    if (!hasText()) {
      throw new IllegalArgumentException(message);
    }
  }

  public boolean hasText() {
    return (str != null && !str.isEmpty() && containsText(str));
  }

  private boolean containsText(CharSequence str) {
    int strLen = str.length();
    for (int i = 0; i < strLen; i++) {
      if (!Character.isWhitespace(str.charAt(i))) {
        return true;
      }
    }
    return false;
  }
}

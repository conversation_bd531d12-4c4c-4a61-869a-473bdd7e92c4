package au.com.peterpal.selecting.standingorders.customerstandingorder.dto;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.*;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import lombok.*;

import java.util.UUID;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class CreateRelease {

  private UUID id;
  private ReleaseType releaseType;
  private ActionType actionType;
  private AssignmentRule assignmentRule;
  private SmallFormatPaperbackRule smallFormatPaperbackRule;

  private ReleasePreference releasePreference;
  private Fund fund;
  private Fund hbFund;
  private Fund pbFund;

  @Builder.Default private int quantity = 0;
  @Builder.Default private int hbQuantity = 0;
  @Builder.Default private int pbQuantity = 0;

  public static CreateRelease from(UUID id, ReleasePreference releasePreference) {
    Affirm.of(releasePreference).notNull("ReleasePreference must not be null");

    return CreateRelease.builder()
        .actionType(releasePreference.getActionType())
        .assignmentRule(releasePreference.getInitialAssignmentRule())
        .fund(releasePreference.getFund())
        .hbFund(releasePreference.getHardbackfund())
        .pbFund(releasePreference.getPaperbackfund())
        .releaseType(releasePreference.getReleaseType())
        .smallFormatPaperbackRule(releasePreference.getSmallFormatPaperbackRule())
        .releasePreference(releasePreference)
        .id(id)
        .build();
  }
}

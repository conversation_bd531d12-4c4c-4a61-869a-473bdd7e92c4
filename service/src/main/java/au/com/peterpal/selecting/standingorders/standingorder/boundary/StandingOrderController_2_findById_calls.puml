@startuml

/' diagram meta data start
config=CallConfiguration;
{
  "rootMethod": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController#findById(String)",
  "projectClassification": {
    "searchMode": "OpenProject", // OpenProject, AllProjects
    "includedProjects": "",
    "pathEndKeywords": "*.impl",
    "isClientPath": "",
    "isClientName": "",
    "isTestPath": "",
    "isTestName": "",
    "isMappingPath": "",
    "isMappingName": "",
    "isDataAccessPath": "",
    "isDataAccessName": "",
    "isDataStructurePath": "",
    "isDataStructureName": "",
    "isInterfaceStructuresPath": "",
    "isInterfaceStructuresName": "",
    "isEntryPointPath": "",
    "isEntryPointName": ""
  },
  "graphRestriction": {
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "removeByInheritance": "", // inheritance/annotation based filtering is done in a second step
    "removeByAnnotation": "",
    "removeByClassPackage": "", // cleanup the graph after inheritance/annotation based filtering is done
    "removeByClassName": "",
    "cutMappings": false,
    "cutEnum": true,
    "cutTests": true,
    "cutClient": true,
    "cutDataAccess": true,
    "cutInterfaceStructures": true,
    "cutDataStructures": true,
    "cutGetterAndSetter": true,
    "cutConstructors": true
  },
  "graphTraversal": {
    "forwardDepth": 3,
    "backwardDepth": 3,
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "hideMappings": false,
    "hideDataStructures": false,
    "hidePrivateMethods": true,
    "hideInterfaceCalls": true, // indirection: implementation -> interface (is hidden) -> implementation
    "onlyShowApplicationEntryPoints": false // root node is included
  },
  "details": {
    "aggregation": "GroupByClass", // ByClass, GroupByClass, None
    "showMethodParametersTypes": false,
    "showMethodParametersNames": false,
    "showMethodReturnType": false,
    "showPackageLevels": 2,
    "showCallOrder": false,
    "edgeMode": "MethodsOnly", // TypesOnly, MethodsOnly, TypesAndMethods, MethodsAndDirectTypeUsage
    "showDetailedClassStructure": false
  },
  "rootClass": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController"
}
diagram meta data end '/



digraph g {
    rankdir="LR"
    splines=polyline


'nodes
subgraph cluster_98689 {
   	label=com
	labeljust=l
	fillcolor="#ececec"
	style=filled

   subgraph cluster_1300071644 {
   	label=peterpal
	labeljust=l
	fillcolor="#d8d8d8"
	style=filled

   subgraph cluster_1774816029 {
   	label=StandingOrderController
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderController1189585491XXXfindById1808118735[
	label="+ findById()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderController

null"
	penwidth=4
	fontcolor=darkgreen
];
}

subgraph cluster_433581393 {
   	label=StandingOrderId
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderId843015776XXXof1808118735[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderId

null"
	fontcolor=darkgreen
];
}
}
}

'edges
StandingOrderController1189585491XXXfindById1808118735 -> StandingOrderId843015776XXXof1808118735;

}
@enduml

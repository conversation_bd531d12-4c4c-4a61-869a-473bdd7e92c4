package au.com.peterpal.selecting.standingorders.standingorder.model;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.*;

import lombok.*;
import org.hibernate.annotations.BatchSize;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@Builder
@Entity
@Table(name = "combined_term")
public class CombinedTerm {
  @EqualsAndHashCode.Include
  @NonNull
  @EmbeddedId
  private CombinedTermId combinedTermId;

  @OneToMany(mappedBy = "combinedTerm", fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true)
  @Builder.Default
  @JsonManagedReference
  @BatchSize(size = 10000)
  @ToString.Exclude
  private List<Term> terms = new ArrayList<>();

  public void addTerm(Term term) {
    terms.add(term);
    term.setCombinedTerm(this);
  }

  public void removeTerm(Term term) {
    terms.remove(term);
    term.setCombinedTerm(null);
  }
}
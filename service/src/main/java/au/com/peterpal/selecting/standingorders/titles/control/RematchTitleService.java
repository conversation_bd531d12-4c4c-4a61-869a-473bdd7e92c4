package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.ClientWebApiGateway;
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderBL;
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderService;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.control.MatchingService;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderRepository;
import au.com.peterpal.selecting.standingorders.standingorder.control.TermRepository;
import au.com.peterpal.selecting.standingorders.standingorder.events.MatchFound;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductMatchInfo;
import au.com.peterpal.selecting.standingorders.standingorder.model.*;
import au.com.peterpal.selecting.standingorders.titles.dto.RematchResult;
import au.com.peterpal.selecting.standingorders.titles.entity.*;
import au.com.peterpal.selecting.standingorders.titles.events.TitleRematched;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Log4j2
@Service
@RequiredArgsConstructor
@Transactional
public class RematchTitleService {
  private static final RejectionReasonTypeId REJECT_DUE_PRODUCT_UPDATE =
      RejectionReasonTypeId.of("PRODUCT_UPDATED");
  private static final RejectionReasonTypeId REJECT_DUE_PRODUCT_TITLE_REMATCH =
      RejectionReasonTypeId.of("REMATCHED");
  private static final RejectionReasonTypeId REJECT_DUE_PRODUCT_SO_REMATCH =
      RejectionReasonTypeId.of("STANDING_ORDER_REMATCHED");
  private final MatchAndMergeTitleService matchAndMergeTitleService;
  private final TitleService titleService;
  private final PendingOrderService pendingOrderService;
  private final PendingOrderBL pendingOrderBL;
  private final MatchingService matchingService;
  private final StandingOrderRepository standingOrderRepository;
  private final TermRepository termRepository;
  private final ClientWebApiGateway clientWebApiGateway;
  private final EventPublisher eventPublisher;

  public RematchResult rematchTitleForStandingOrder(
      StandingOrderId standingOrderId, TermId termId, TitleId titleId, ProductMatchInfo info, String username) {
    RematchResult rematchResult = RematchResult.builder().build();
    Map<StandingOrderId, TermId> matchFounds = Maps.newHashMap();
    matchFounds.put(standingOrderId, termId);

    Title title = getTitle(titleId);

    Optional<MatchedProduct> matchedProduct =
        title.findActiveMatchedProductByIsbn(info.getProductReference());
    if (matchedProduct.isPresent()) {
      rematchResult = rematchTitleWithProduct(title, matchFounds, matchedProduct.get(), info, username);
      title = rejectOrMergeTitle(title.getTitleId(), REJECT_DUE_PRODUCT_SO_REMATCH);
    } else {
      log.warn(
          "rematch ignored no product with ISBN {} under title {}",
          info.getProductReference(),
          title.getTitleId());
    }

    RematchResult result = rematchResult.withTitle(title);
    eventPublisher.publishEvent(
        TitleRematched.from(result.getTitle().getTitleId().getId(), username, result));

    return result;
  }

  public RematchResult rematchTitleForProductUpdate(
      TitleId titleId, ProductMatchInfo productUpdate, String username) {
    RematchResult rematchResult = RematchResult.builder().build();
    Title title = titleService.findTitleById(titleId);
    Optional<MatchedProduct> matchedProduct =
        title.findAnyMatchedProductByIsbn(productUpdate.getProductReference());

    if (matchedProduct.isPresent()) {
      Map<StandingOrderId, TermId> matchFounds =
          matchingService.match(productUpdate).stream()
              .collect(
                  Collectors.toMap(
                      matchFound -> StandingOrderId.of(matchFound.getSoId()),
                      MatchFound::getMatchedTermId));

      if (title.isRejected()
          && Optional.ofNullable(title.getRejectionReasonType())
              .map(rt -> rt.getRejectionReasonTypeId().equals(REJECT_DUE_PRODUCT_UPDATE))
              .orElse(false)) {
        title = titleService.undoTitle(title, username);
      }

      if (!title.isRejected() && !title.isProcessed()) {
        rematchResult =
                rematchTitleWithProduct(title, matchFounds, matchedProduct.get(), productUpdate, username);
        title = rejectOrMergeTitle(title.getTitleId(), REJECT_DUE_PRODUCT_UPDATE);
      }

    } else {
      log.warn(
          "rematch ignored no product with ISBN {} under title {}",
          productUpdate.getProductReference(),
          title.getTitleId());
    }

    RematchResult result = rematchResult.withTitle(title);
    eventPublisher.publishEvent(
        TitleRematched.from(result.getTitle().getTitleId().getId(), username, result));

    return result;
  }

  public RematchResult rematchTitle(TitleId titleId, String username) {
    Title title = titleService.findTitleById(titleId);

    ArrayList<MatchedProduct> productsNoLongerMatched = Lists.newArrayList();
    ArrayList<MatchedStandingOrder> standingOrdersNoLongerMatched = Lists.newArrayList();
    ArrayList<MatchedStandingOrder> newMatchedStandingOrders = Lists.newArrayList();
    ArrayList<PendingOrderId> invalidatedPendingOrders = Lists.newArrayList();

    for (MatchedProduct matchedProduct : title.getActiveMatchProducts()) {
      ProductMatchInfo productInfo =
          clientWebApiGateway.getProductInfoByIsbn(matchedProduct.getIsbn());
      Map<StandingOrderId, TermId> matchFounds =
          matchingService.match(productInfo).stream()
              .collect(
                  Collectors.toMap(
                      matchFound -> StandingOrderId.of(matchFound.getSoId()),
                      MatchFound::getMatchedTermId));

      RematchResult rematchResult =
          rematchTitleWithProduct(title, matchFounds, matchedProduct, productInfo, username);

      if (CollectionUtils.isNotEmpty(rematchResult.getProductsNoLongerMatched())) {
        productsNoLongerMatched.addAll(rematchResult.getProductsNoLongerMatched());
      }

      if (CollectionUtils.isNotEmpty(rematchResult.getStandingOrdersNoLongerMatched())) {
        standingOrdersNoLongerMatched.addAll(rematchResult.getStandingOrdersNoLongerMatched());
      }

      if (CollectionUtils.isNotEmpty(rematchResult.getNewMatchedStandingOrders())) {
        newMatchedStandingOrders.addAll(rematchResult.getNewMatchedStandingOrders());
      }

      if (CollectionUtils.isNotEmpty(rematchResult.getInvalidatedPendingOrders())) {
        invalidatedPendingOrders.addAll(rematchResult.getInvalidatedPendingOrders());
      }
    }

    title = rejectOrMergeTitle(title.getTitleId(), REJECT_DUE_PRODUCT_TITLE_REMATCH);
    RematchResult result =
        RematchResult.builder()
            .title(title)
            .productsNoLongerMatched(productsNoLongerMatched)
            .standingOrdersNoLongerMatched(standingOrdersNoLongerMatched)
            .newMatchedStandingOrders(newMatchedStandingOrders)
            .invalidatedPendingOrders(invalidatedPendingOrders)
            .build();
    eventPublisher.publishEvent(
        TitleRematched.from(result.getTitle().getTitleId().getId(), username, result));

    return result;
  }

  RematchResult rematchTitleWithProduct(
          Title title,
          Map<StandingOrderId, TermId> matchFounds,
          MatchedProduct matchedProduct,
          ProductMatchInfo catalogueProduct, String username) {

    // handle standing order no longer match as well as new standing order match
    List<MatchedStandingOrder> standingOrdersNoLongerMatched =
        evaluateNoLongerMatch(matchedProduct, matchFounds);
    standingOrdersNoLongerMatched.forEach(MatchedStandingOrder::makeInactive);
    List<MatchedStandingOrder> newMatchedStandingOrders =
        evaluateNewMatch(matchedProduct, matchFounds);
    newMatchedStandingOrders.forEach(matchedProduct::addMatchedStandingOrder);
    title = titleService.update(title, username);

    // handle aggregation update
    title.removeNoLongerMatchStandingOrderAggregate();
    title.removeNoLongerMatchProductAggregate();
    titleService.update(title, username);

    // handle match product update
    matchedProduct.updateWithCatalogueInfo(catalogueProduct);
    List<MatchedProduct> productsNoLongerMatched = title.inactiveNoLongerMatchedProduct();
    title = titleService.update(title, username);

    // delete related pending orders
    List<PendingOrderId> invalidatedPendingOrders =
        handleInvalidatePendingOrders(
            title,
            standingOrdersNoLongerMatched.stream()
                .map(MatchedStandingOrder::getStandingOrderId)
                .collect(Collectors.toList()), username);

    return RematchResult.of(
        title,
        productsNoLongerMatched,
        standingOrdersNoLongerMatched,
        newMatchedStandingOrders,
        invalidatedPendingOrders);
  }

  List<MatchedStandingOrder> evaluateNoLongerMatch(
      MatchedProduct product, Map<StandingOrderId, TermId> matchedStandingOrderTermMap) {
    List<MatchedStandingOrder> matchedStandingOrdersToRemoved;
    if (MapUtils.isEmpty(matchedStandingOrderTermMap)) {
      matchedStandingOrdersToRemoved = new ArrayList<>(product.getActiveMatchedStandingOrder());

    } else {
      matchedStandingOrdersToRemoved =
          handleOldStandingOrderNoLongerMatch(
              product, new ArrayList<>(matchedStandingOrderTermMap.keySet()));
    }

    return matchedStandingOrdersToRemoved;
  }

  List<MatchedStandingOrder> evaluateNewMatch(
      MatchedProduct product, Map<StandingOrderId, TermId> matchFounds) {

    List<MatchedStandingOrder> newSOMatches = Lists.newArrayList();
    if (!MapUtils.isEmpty(matchFounds)) {
      newSOMatches =
          matchFounds.entrySet().stream()
              .filter(entry -> !product.isStandingOrderExist(entry.getKey()))
              .map(
                  entry ->
                      MatchedStandingOrder.from(
                          product, getStandingOrder(entry.getKey()), getTerm(entry.getValue())))
              .collect(Collectors.toList());
    }
    return newSOMatches;
  }

  List<PendingOrderId> handleInvalidatePendingOrders(
          Title title, List<StandingOrderId> noLongerMatchStandingOrders, String username) {
    List<PendingOrderId> invalidatedPendingOrders = null;
    if (title.getTitleStatus() == TitleStatus.PENDING
        && CollectionUtils.isNotEmpty(noLongerMatchStandingOrders)) {

      List<PendingOrderId> pendingOrderIds =
          pendingOrderService
              .findByTitleIdAndStandingOrderId(title.getTitleId(), noLongerMatchStandingOrders)
              .stream()
              .filter(PendingOrder::isNew)
              .map(PendingOrder::getPendingOrderId)
              .collect(Collectors.toList());

      invalidatedPendingOrders =
          pendingOrderBL.invalidatePendingOrders(pendingOrderIds, username).stream()
              .map(PendingOrder::getPendingOrderId)
              .collect(Collectors.toList());
      log.trace("Pending orders invalidated {}", invalidatedPendingOrders);
    }
    return invalidatedPendingOrders;
  }

  Title rejectOrMergeTitle(TitleId titleId, RejectionReasonTypeId rejectionReasonTypeId) {
    Title title = titleService.findTitleById(titleId);
    if (CollectionUtils.isEmpty(title.getActiveMatchProducts())) {
      return titleService.rejectTitle(title, rejectionReasonTypeId, null, "system", false);
    } else {
      // do aggregation
      return matchAndMergeTitleService.merge(title);
    }
  }

  List<MatchedStandingOrder> handleOldStandingOrderNoLongerMatch(
      MatchedProduct product, List<StandingOrderId> matchedStandingOrderIds) {
    return product.getActiveMatchedStandingOrder().stream()
        .filter(
            matchedStandingOrder ->
                !matchedStandingOrderIds.contains(
                    matchedStandingOrder.getStandingOrder().getStandingOrderId()))
        .collect(Collectors.toList());
  }

  Term getTerm(TermId matchedTermId) {
    return termRepository
        .findById(matchedTermId)
        .orElseThrow(
            () ->
                new ResourceNotFoundException(
                    String.format("Term not found for id %s", matchedTermId)));
  }

  Title getTitle(TitleId titleId) {
    Title title = titleService.findTitleById(titleId);
    if (title.getTitleStatus() == TitleStatus.PROCESSED
        || title.getTitleStatus() == TitleStatus.REJECTED) {
      throw new BusinessException("Rematch title can only be done for NEW or PENDING title");
    }

    return title;
  }

  StandingOrder getStandingOrder(StandingOrderId standingOrderId) {
    return standingOrderRepository.getOne(standingOrderId);
  }
}

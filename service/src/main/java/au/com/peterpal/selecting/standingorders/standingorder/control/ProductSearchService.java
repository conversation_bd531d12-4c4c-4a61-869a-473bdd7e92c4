package au.com.peterpal.selecting.standingorders.standingorder.control;

import au.com.peterpal.selecting.standingorders.gateways.clientweb.ClientWebApiGateway;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.CwProductInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Log4j2
@RequiredArgsConstructor
@Transactional
@Component
public class ProductSearchService {

  private final ClientWebApiGateway clientWebApiGateway;

  public Optional<CwProductInfo> searchProductByIsbn(String isbn) {
    return clientWebApiGateway.findProductByIsbn(isbn);
  }
}

package au.com.peterpal.selecting.standingorders.standingorder.model;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class AdHocProductId extends UuidEntityId {

  public static AdHocProductId of(@NotEmpty UUID id) {
    return new AdHocProductId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public AdHocProductId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public AdHocProductId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public AdHocProductId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

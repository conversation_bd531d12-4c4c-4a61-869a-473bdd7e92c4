package au.com.peterpal.selecting.standingorders.customerstandingorder.dto;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.*;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Data
@Builder
public class CSOInfo {

  @NotBlank private String customerCode;

  @NotBlank private String standingOrderNumber;

  @Builder.Default
  private List<String> categories = new ArrayList<>();

  private String customerReference;

  private String deliveryInstructions;

  private String notes;

  private String fundCode;

  private ReleaseInfo releaseInfo;

  @Data
  @Builder
  public static class ReleaseInfo {

    private UUID id;

    @NotNull private ReleaseType releaseType;

    @NotNull private ActionType actionType;

    private String fundCode;

    private String hardbackFundCode;
    private String paperbackFundCode;

    private int qtyTotal;
    private int qtyHardback;
    private int qtyPaperback;

    private AssignmentRule assignmentRule;
    private SmallFormatPaperbackRule smallFormatPaperbackRule;
  }
}

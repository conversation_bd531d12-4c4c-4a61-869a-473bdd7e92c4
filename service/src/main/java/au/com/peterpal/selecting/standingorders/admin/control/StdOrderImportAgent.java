package au.com.peterpal.selecting.standingorders.admin.control;

import au.com.peterpal.selecting.standingorders.standingorder.commands.CreateStandingOrderCmd;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderBL;
import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderCreated;
import au.com.peterpal.selecting.standingorders.standingorder.model.CombinedTermId;
import au.com.peterpal.selecting.standingorders.standingorder.model.OperationType;
import au.com.peterpal.selecting.standingorders.standingorder.model.Term;
import au.com.peterpal.selecting.standingorders.standingorder.model.TermType;
import au.com.peterpal.selecting.standingorders.utils.StringAffirm;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.web.multipart.MultipartFile;

/**
 * Excel import agent for standing orders.
 *
 * <pre>
 *   SQL to select data to be imported:
 *
 *   select
 *      so.standingOrdNum, so.description, so.notes, sop.standingOrderProfileId,
 *      sop.personNameInverted, sop.seriesTitle, sop.imprintName,
 *      sop.titleWithoutPrefix, so.modifiedDate, so.modifiedBy
 *   from StandingOrder so
 *   inner join StandingOrderProfile sop on so.standingOrdNum = sop.standingOrdNum
 *   inner join StandingOrdCust soc on so.standingOrdNum = soc.standingOrdNum
 *   where so.statusCode = 'ACTIVE'
 *   and soc.customerCode IN (
 * 	  select distinct parentCustomerCode from Cust
 * 	  where parentCustomerCode is not null AND parentCustomerCode <> '' AND statusCode = 'ACTIVE'
 *   )
 *   order by modifiedDate DESC
 * </pre>
 */
@Log4j2
public class StdOrderImportAgent extends ExcelImportAgent {
  private static int STANDING_ORDER_CELL_ID = 0;
  private static int DESCRIPTION_CELL_ID = 1;
  private static int NOTES_CELL_ID = 2;
  private static int PERSON_NAME_CELL_ID = 4;
  private static int CORP_NAME_CELL_ID = 5;
  private static int TITLE_CELL_ID = 6;
  private static int IMPRINT_CELL_ID = 7;
  private static int TITLE_NO_PREFIX_CELL_ID = 8;

  private StandingOrderBL service;
  private Map<String, CreateStandingOrderCmd> cmdMap = new HashMap<>();

  public static final StdOrderImportAgent of(MultipartFile file, StandingOrderBL service) {
    return new StdOrderImportAgent(file, service);
  }

  private StdOrderImportAgent(MultipartFile file, StandingOrderBL service) {
    super(file);
    Optional.ofNullable(service)
        .map(s -> this.service = s)
        .orElseThrow(() -> new IllegalArgumentException("Standing order service must not be null"));
  }

  @Override
  protected void doImport() {
    Sheet sheet = getFirstSheet();

    CreateStandingOrderCmd cmd;
    for (Row row : sheet) {
      if (row.getRowNum() == 0) {
        continue;
      }

      //cmd = null;
      try {
        //cmd = loadSOCommand(row);
        loadSOCommand(row);
      } catch(Exception ex) {
        String rowStr = toString(row);
        log.warn(String.format("Exception loading standing order from %s", rowStr), ex);
        log.warn("Standing order related information will be disregarded");

        /*TODO fix the code when there is exception
        if (cmd != null && cmdMap.containsKey(cmd.getStandingOrderNumber())) {
          cmdMap.remove(cmd);
        }*/
      }
    }

    report.setTotal(cmdMap.size());
    AtomicInteger importCount = new AtomicInteger();
    AtomicInteger errorCount = new AtomicInteger();
    log.debug("standing orders started ");
    List<StandingOrderCreated> standingOrderCreatedList = Lists.newArrayList();
    cmdMap.forEach((k, v) -> {
      try {
        standingOrderCreatedList.add(service.handleImport(v, test));
        importCount.getAndIncrement();
      } catch (Exception ex) {
        log.warn(String.format("Exception creating standing order from %s",v), ex);
        errorCount.getAndIncrement();
        report.getErrors().add(String.format("%s - %s", v.getStandingOrderNumber(), ex.getMessage()));
      }
    });
    log.debug("standing orders to persist: " + standingOrderCreatedList.size());
    service.persistImportData(standingOrderCreatedList);
    log.debug("standing orders to finished: " + standingOrderCreatedList.size());

    report.setImported(importCount.get());
    report.setFailed(errorCount.get());
  }

  private CreateStandingOrderCmd loadSOCommand(Row row) {
    return Optional.ofNullable(row)
      .map(r -> {
        String soNumber = getString(r.getCell(STANDING_ORDER_CELL_ID), "Standing order number must not be null");
        Integer sonumber = SONumberConverter.of().convert(soNumber);
        soNumber = String.format("SO-%08d", sonumber.longValue());
        CreateStandingOrderCmd cmd;
        if (cmdMap.containsKey(soNumber)) {
          cmd = cmdMap.get(soNumber);
        } else {
          cmd = CreateStandingOrderCmd.builder()
                  .username("system")
                  .standingOrderNumber(soNumber)
                  .description(r.getCell(DESCRIPTION_CELL_ID).getStringCellValue())
                  .notes(r.getCell(NOTES_CELL_ID).getStringCellValue())
                  .terms(new ArrayList<>())
                  .build();
          cmdMap.put(cmd.getStandingOrderNumber(), cmd);
        }
        String str = getString(r.getCell(PERSON_NAME_CELL_ID));
        addTerm(cmd, Term.from(str, TermType.PERSON_NAME, OperationType.EXACT_MATCH, CombinedTermId.of(UUID.randomUUID())));

        str = getString(r.getCell(TITLE_CELL_ID));
        addTerm(cmd, Term.from(str, TermType.SERIES_TITLE, OperationType.CONTAINS, CombinedTermId.of(UUID.randomUUID())));

        String imprStr = getString(r.getCell(IMPRINT_CELL_ID));
        String titleStr = getString(r.getCell(TITLE_NO_PREFIX_CELL_ID));
        addTerm(cmd, imprStr, titleStr);
        return cmd;
      })
      .orElseThrow(() -> new IllegalArgumentException("Can not create CreateStandingOrderCmd from null row"));
  }

  private void addTerm(CreateStandingOrderCmd cmd, Term term) {
    if (term != null) {
      cmd.getTerms().add(term);
    }
  }

  void addTerm(CreateStandingOrderCmd cmd, String imprStr, String titleStr) {
    CombinedTermId combinedTermId = CombinedTermId.of(UUID.randomUUID());
    Term iterm = Term.from(imprStr, TermType.IMPRINT_NAME, OperationType.CONTAINS, combinedTermId);
    Term tterm = Term.from(titleStr, TermType.TITLE, OperationType.CONTAINS, combinedTermId);

    if (iterm != null && tterm != null) {
      addTerm(cmd, iterm);
      addTerm(cmd, tterm);
    } else if (iterm != null) {
      addTerm(cmd, iterm);
    } else if (tterm != null){
      addTerm(cmd, tterm);
    } else {
      log.warn(() -> String.format("Not adding terms to SO %s", cmd.getStandingOrderNumber()));
      log.warn(() -> String.format("Imprint name %s; Title %s",
          imprStr == null ? "null" : imprStr, titleStr == null ? "null" : titleStr));
    }
  }

  public static class SONumberConverter {

    private Pattern pattern;

    public static SONumberConverter of() {
      return new SONumberConverter("[1-9]\\d*");
    }

    private SONumberConverter(String patternStr) {
      pattern = Pattern.compile(patternStr);
    }

    public Integer convert(String text) {
      StringAffirm.of(text).hasText("Text to convert must not be blank");

      Matcher matcher = pattern.matcher(text);
      if (matcher.find()) {
        return Integer.valueOf(matcher.group());
      }
      String msg = "String %s can not be converted to a number; does not match pattern [1-9][0-9]*";
      throw new IllegalStateException(String.format(msg, text));
    }
  }
}

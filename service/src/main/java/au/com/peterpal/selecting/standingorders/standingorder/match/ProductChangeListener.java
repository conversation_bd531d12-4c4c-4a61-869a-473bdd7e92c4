package au.com.peterpal.selecting.standingorders.standingorder.match;

import au.com.peterpal.selecting.standingorders.catalog.model.ContributorRoleCode;
import au.com.peterpal.selecting.standingorders.standingorder.events.MatchFound;
import au.com.peterpal.selecting.standingorders.standingorder.model.SOSummary;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import java.util.*;

import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.springframework.util.StringUtils;

@Log4j2
public class ProductChangeListener {

  public static final String NO_AUTHOR = "No Author";

  public static final EnumSet<ContributorRoleCode> VALID_CONTRIBUTOR_CODES =
      EnumSet.of(
          ContributorRoleCode.A01,
          ContributorRoleCode.A05,
          ContributorRoleCode.A07,
          ContributorRoleCode.A08,
          ContributorRoleCode.A09,
          ContributorRoleCode.A12,
          ContributorRoleCode.A13);

  @Getter private final SOSummary summary;

  private ProductMatcher matcher;

  public static ProductChangeListener of(
      SOSummary summary,
      List<String> restrictedPublishers,
      Integer minPublicationDateOffset,
      Integer maxPublicationDateOffset,
      String regexRemoveChar,
      String regexReplaceCharWithSpace) {
    return new ProductChangeListener(
        summary,
        restrictedPublishers,
        minPublicationDateOffset,
        maxPublicationDateOffset,
        regexRemoveChar,
        regexReplaceCharWithSpace);
  }

  private ProductChangeListener(
      SOSummary summary,
      List<String> restrictedPublishers,
      Integer minPublicationDateOffset,
      Integer maxPublicationDateOffset,
      String regexRemoveChar,
      String regexReplaceCharWithSpace) {
    Affirm.of(summary).notNull("Product summary must not be null");
    this.summary = summary;
    matcher =
        LegacyMatcher.of(
            this.summary,
            restrictedPublishers,
            minPublicationDateOffset,
            maxPublicationDateOffset,
            regexRemoveChar,
            regexReplaceCharWithSpace);
  }

  public MatchFound doMatching(ProductMatchInfo info) {
    info = info.trim();
    MatchResult matchResult = matcher.match(info);
    if (matchResult.isMatched()) {
      log.debug("Match found {} to {}", info.getProductReference(), matchResult.getNote());
      return buildMatchFoundEvent(info, matchResult);
    }
    return null;
  }

  MatchFound buildMatchFoundEvent(ProductMatchInfo info, MatchResult mr) {
    return MatchFound.builder()
        .id(UUID.randomUUID())
        .username("system")
        .soId(summary.getId())
        .matchedTermId(mr.getMatchedTermId())
        .number(summary.getNumber())
        .productId(info.getId())
        .subjectCode1(info.getBicSubjectCode1())
        .subjectCode2(info.getBicSubjectCode2())
        .subjectCode3(info.getBicSubjectCode3())
        .subjectCode4(info.getBicSubjectCode4())
        .subjectCode5(info.getBicSubjectCode5())
        .subjectCodeList(info.getSubjectCodeList())
        .note(mr.getNote())
        .productReference(info.getProductReference())
        .formCode(info.getFormCode())
        .formDetails(info.getFormDetails())
        .width(info.getWidth())
        .height(info.getHeight())
        .title(info.getTitle().getWithoutPrefix())
        .subtitle(info.getTitle().getSubtitle())
        .series(info.getSeriesTitle() == null ? "" : info.getSeriesTitle().getText())
        .matchedPersonName(ProductChangeListener.getPersonName(info, mr))
        .imprint(ProductChangeListener.getImprint(info))
        .publicationDate(info.getPublicationDate())
        .editionStatement(info.getEditionStatement())
        .build();
  }

  /**
   * Get person name from MatchResult and ProductMatchInfo.
   *
   * <p>If MatchResult does not have a matched person name, then get it from the ProductInfo. Get
   * the first contributor with role code of A01 and return personNameInverted.
   *
   * @param info product information
   * @param mr the match result
   * @return if matched person name in MatchResult has text return it, else get person name inverted
   *     from product
   */
  public static String getPersonName(ProductMatchInfo info, MatchResult mr) {
    String result = "";
    if (StringUtils.hasText(mr.getMatchedPersonName())) {
      return mr.getMatchedPersonName();
    }

    if (info != null && info.getContributors() != null) {
      try {
        result = info.getPersonName(VALID_CONTRIBUTOR_CODES, NO_AUTHOR);
      } catch (Exception ex) {
        log.warn(
            String.format("Exception %s while getting person name from %s", ex.getMessage(), info));
      }
    }

    return result;
  }

  public static String getImprint(ProductMatchInfo info) {
    String result = "";
    if (info != null && info.getImprintNames() != null) {
      result = String.join(",", info.getImprintNames());
    }
    return result;
  }
}

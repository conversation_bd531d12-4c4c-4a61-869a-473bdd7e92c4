package au.com.peterpal.selecting.standingorders.pendingorder.dto;

import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.ext.order.dto.DuplicateOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;
import java.util.Set;
import javax.validation.constraints.NotNull;
import lombok.*;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class PendingOrderResponse {
  @Value
  @Builder
  @EqualsAndHashCode
  public static class StandingOrder {
    private StandingOrderId standingOrderId;
    private String standingOrderNumber;
    private String standingDescription;
  }

  @Value
  @Builder
  private static class BranchInfo {
    private BranchId  branchId;
    private String code;
    private String name;
  }

  @NotNull @NonNull PendingOrderId pendingOrderId;
  String deliveryInstructions;
  String notes;
  String fundId;
  String fundCode;
  String customerId;
  String customerCode;
  @Builder.Default
  Integer quantity = 0;
  String format;
  String status;
  String customerReference;
  String category;

  String collectionCode;

  BranchInfo branch;
  String orderNumberReference;
  int orderCount;

  BigDecimal price;
  LocalDate publicationDate;;

  DuplicateOrder duplicateOrder;
  String titleNumber;
  Set<StandingOrder> standingOrders;

  public static PendingOrderResponse from(PendingOrder po, Set<StandingOrder> standingOrders) {
    return from(po).withStandingOrders(standingOrders);
  }

  public static PendingOrderResponse from(PendingOrder pendingOrder) {
    Affirm.of(pendingOrder).notNull("PendingOrder must not be null");
    Affirm.of(pendingOrder.getPendingOrderId()).notNull("PendingOrderId must not be null");
    Affirm.of(pendingOrder.getCustomer()).notNull("Customer must not be null");
    Affirm.of(pendingOrder.getRelease()).notNull("Release must not be null");

    Customer customer = pendingOrder.getCustomer();
    Fund fund = pendingOrder.getFund();
    Title title = pendingOrder.getTitle();

    return PendingOrderResponse.builder()
        .pendingOrderId(pendingOrder.getPendingOrderId())
        .deliveryInstructions(pendingOrder.getDeliveryInstructions())
        .notes(pendingOrder.getNotes())
        .fundId(Objects.nonNull(fund) ? fund.getFundId().toString() : null)
        .fundCode(Objects.nonNull(fund) ? fund.getCode() : null)
        .customerId(customer.getCustomerId().toString())
        .customerCode(customer.getCode())
        .quantity(pendingOrder.getQuantity())
        .format(pendingOrder.getFormat().name())
        .status(pendingOrder.getOrderStatus().name())
        .customerReference(pendingOrder.getCustomerReference())
        .category(pendingOrder.getCategory() == null ? null : pendingOrder.getCategory().getCode())
        .collectionCode(pendingOrder.getCollectionCode())
        .orderNumberReference(pendingOrder.getOrderNumber())
        .orderCount(pendingOrder.getOrderCount())
        .duplicateOrder(pendingOrder.getDuplicateOrder())
        .price(pendingOrder.getPrice())
        .publicationDate(pendingOrder.getPublicationDate())
        .titleNumber(Objects.nonNull(title)? title.getTitleNumber() : null)
        .build();
  }
}

package au.com.peterpal.selecting.standingorders.standingorder.match;

import au.com.peterpal.selecting.standingorders.catalog.model.ContributorRoleCode;
import au.com.peterpal.selecting.standingorders.catalog.model.LanguageCode;
import au.com.peterpal.selecting.standingorders.catalog.model.ProductFormCode;
import au.com.peterpal.selecting.standingorders.standingorder.match.ProductMatchInfo.PublisherInfo;
import au.com.peterpal.selecting.standingorders.standingorder.model.*;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import au.com.peterpal.selecting.standingorders.utils.StringHelper;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

/** A product matcher that implements Lucy 4 matching. */
@Log4j2
public class LegacyMatcher implements ProductMatcher {

  public static EnumSet<ProductFormCode> validFormCodes =
      EnumSet.of(
          ProductFormCode.WW,
          ProductFormCode.BA,
          ProductFormCode.BB,
          ProductFormCode.BC,
          ProductFormCode.BK);

  private static EnumSet<ContributorRoleCode> validContributorCodes =
      EnumSet.of(
          ContributorRoleCode.A01,
          ContributorRoleCode.A05,
          ContributorRoleCode.A07,
          ContributorRoleCode.A08,
          ContributorRoleCode.A09,
          ContributorRoleCode.A12,
          ContributorRoleCode.A13);

  private SOSummary soSummary;
  private String note;

  private String matchedPersonName;

  protected LocalDate minPublishDate = LocalDate.now();
  protected LocalDate maxPublishDate = LocalDate.now();
  protected String regexRemoveChar;
  protected String regexReplaceCharWithSpace;

  private List<String> restrictedPublishers;

  public static LegacyMatcher of(
      SOSummary soSummary,
      List<String> restrictedPublishers,
      Integer minPublicationDateOffset,
      Integer maxPublicationDateOffset,
      String regexRemoveChar,
      String regexReplaceCharWithSpace) {

    return new LegacyMatcher(
        soSummary,
        restrictedPublishers,
        minPublicationDateOffset,
        maxPublicationDateOffset,
        regexRemoveChar,
        regexReplaceCharWithSpace);
  }

  private LegacyMatcher(
      SOSummary soSummary,
      List<String> restrictedPublishers,
      Integer minPublicationDateOffset,
      Integer maxPublicationDateOffset,
      String regexRemoveChar,
      String regexReplaceCharWithSpace) {
    Affirm.of(soSummary).notNull("Standing order summary must not be null");
    this.soSummary = soSummary;
    this.restrictedPublishers = restrictedPublishers;

    if (minPublicationDateOffset != null) {
      this.minPublishDate = minPublishDate.minusMonths(minPublicationDateOffset);
    }

    if (maxPublicationDateOffset != null) {
      this.maxPublishDate = maxPublishDate.plusMonths(maxPublicationDateOffset);
    }

    this.regexRemoveChar = StringUtils.defaultString(regexRemoveChar, "");
    this.regexReplaceCharWithSpace = StringUtils.defaultString(regexReplaceCharWithSpace, "");
  }

  @Override
  public MatchResult match(ProductMatchInfo info) {
    if (CollectionUtils.isEmpty(soSummary.getTerms())
        || info == null
        || !isEnglish(info.getLanguageCode())
        || !isValidProductForm(info.getFormCode())
        || !isValidEditionStatement(info.getEditionStatement())
        || !isValidPublicationDate(info.getPublicationDate())
        || hasRestrictedPublisher(info.getPublishers())) {
      return MatchResult.FALSE;
    }
    Map<CombinedTermId, List<Term>> groupedTerms =
        soSummary.getTerms().stream()
            .filter(term -> term.getCombinedTerm() != null)
            .collect(Collectors.groupingBy(term -> term.getCombinedTerm().getCombinedTermId()));

    for (List<Term> terms : groupedTerms.values()) {
      note = "";
      matchedPersonName = "";
      boolean isMatched = terms != null && matchTerm(terms, info);

      if (isMatched) {
        return MatchResult.of(
            true,
            note,
            matchedPersonName,
            terms.get(0).getTermId());
      }
    }
    return MatchResult.FALSE;
  }

  boolean matchTerm(List<Term> terms, ProductMatchInfo info) {
    log.trace("Match SO {} term {} with info {}", soSummary.getId(), terms, info);

    Map<TermType, List<Term>> groupByType =
        terms.stream().collect(Collectors.groupingBy(Term::getType));

    return groupByType.entrySet().stream()
        .allMatch(entry -> matchByTermType(entry.getKey(), entry.getValue(), info));
  }

  private boolean matchByTermType(
      TermType termType, List<Term> groupedTerms, ProductMatchInfo info) {
    switch (termType) {
      case PERSON_NAME:
        return matchPersonName(groupedTerms, info);
      case SERIES_TITLE:
        return matchSeriesTitle(groupedTerms, info);
      case IMPRINT_NAME:
        return matchImprintName(groupedTerms, info);
      case TITLE:
        return matchTitle(groupedTerms, info);
      default:
        return false;
    }
  }

  private boolean matchTitle(List<Term> terms, ProductMatchInfo info) {

    for (Term term : terms) {
      boolean match = matchTitle(term, info);
      if (!match) {
        return false;
      }
    }
    return true;
  }

  private boolean matchSeriesTitle(List<Term> terms, ProductMatchInfo info) {
    for (Term term : terms) {
      boolean match = matchSeriesTitle(term, info);
      if (!match) {
        return false;
      }
    }
    return true;
  }

  boolean matchPersonName(List<Term> terms, ProductMatchInfo info) {
    if (CollectionUtils.isEmpty(info.getContributors())) {
      return false;
    }

    return info.getContributors().stream()
        .filter(ctor -> ctor.getPersonNameInverted() != null)
        .filter(ctor -> validContributorCodes.contains(ctor.getRole()))
        .anyMatch(
            ctor -> {
              String invName = ctor.getPersonNameInverted();
              if (isMatchPersonName(terms, invName)) {
                String termsValue =
                    terms.stream().map(Term::getValue).collect(Collectors.joining(", "));
                String termsOperation =
                    terms.stream()
                        .map(t -> t.getOperation().name())
                        .collect(Collectors.joining(", "));
                note =
                    String.format(
                        "Matched SO %s to product %d on person name rule: %s::%s::%s",
                        soSummary.getNumber(), info.getId(), termsValue, invName, termsOperation);
                log.trace(note);
                matchedPersonName = invName;
                return true;
              }
              return false;
            });
  }

  private boolean isMatchPersonName(List<Term> terms, String invName) {
    return terms.stream()
        .allMatch(term -> isMatch(term, StringHelper.of(term.getValue()).trim(), invName));
  }

  boolean matchSeriesTitle(Term term, ProductMatchInfo info) {
    String name = StringHelper.of(term.getValue()).trim();
    if (info.getSeriesTitle() == null
        || !StringHelper.of(info.getSeriesTitle().getText()).hasText()) {
      return false;
    }

    String title = info.getSeriesTitle().getText();
    boolean match = isMatch(term, name, title);
    if (match) {
      note =
          String.format(
              "Matched SO %s to product %d on series title rule: %s::%s::%s",
              soSummary.getNumber(), info.getId(), title, name, term.getOperation());
      log.trace(note);
    }
    return match;
  }

  boolean matchImprintName(List<Term> terms, ProductMatchInfo info) {
    if (CollectionUtils.isEmpty(info.getImprintNames())) {
      return false;
    }

    boolean isMatched = hasMatchingImprintNames(terms, info.getImprintNames());

    if (isMatched) {
      String termsValues = terms.stream().map(Term::getValue).collect(Collectors.joining(", "));
      String termsOperations =
          terms.stream().map(t -> t.getOperation().name()).collect(Collectors.joining(", "));

      note =
          String.format(
              "Matched SO %s to product %d on imprint name rule: %s::%s::%s",
              soSummary.getNumber(),
              info.getId(),
              termsValues,
              info.getImprintNames(),
              termsOperations);
      log.trace(note);
    }

    return isMatched;
  }

  boolean matchTitle(Term term, ProductMatchInfo info) {
    String name = StringHelper.of(term.getValue()).trim();
    if (!StringHelper.of(name).hasText()
        || info.getTitle() == null
        || !StringHelper.of(info.getTitle().getWithoutPrefix()).hasText()) {
      return false;
    }

    String title = info.getTitle().getWithoutPrefix();
    if (StringUtils.isNotEmpty(info.getTitle().getSubtitle())) {
      title = String.format("%s %s", title, info.getTitle().getSubtitle());
    }

    boolean match = isMatch(term, name, title);
    if (match) {
      note =
          String.format(
              "Matched SO %s to product %d on title without prefix rule: %s:%s",
              soSummary.getNumber(), info.getId(), title, name);
      log.trace(note);
    }
    return match;
  }

  private boolean hasMatchingImprintNames(List<Term> terms, List<String> imprintNames) {
    return imprintNames.stream()
        .anyMatch(imprintName -> hasMatchingImprintName(terms, imprintName));
  }

  private boolean hasMatchingImprintName(List<Term> terms, String imprintName) {
    return terms.stream()
        .allMatch(
            term -> {
              String name = StringHelper.of(term.getValue()).trim();
              return StringHelper.of(name).hasText() && isMatch(term, name, imprintName);
            });
  }

  boolean isMatch(Term term, String termValue, String productInfoValue) {
    return term.getOperation()
        .compareIgnoreCase(stripSpecialChar(termValue), stripSpecialChar(productInfoValue));
  }

  String stripSpecialChar(String inputString) {
    return inputString
        .replaceAll(regexRemoveChar + "+", "")
        .replaceAll(regexReplaceCharWithSpace, " ")
        .replaceAll("\\s+", " ")
        .trim();
  }

  boolean isEnglish(LanguageCode languageCode) {
    return languageCode != null && languageCode.equals(LanguageCode.ENG);
  }

  boolean isValidProductForm(ProductFormCode formCode) {
    return formCode != null && validFormCodes.contains(formCode);
  }

  boolean isValidEditionStatement(String editionStatement) {
    return !StringUtils.containsIgnoreCase(editionStatement, "Large Print")
        && !StringUtils.containsIgnoreCase(editionStatement, "Dyslexic");
  }

  boolean isValidPublicationDate(LocalDate pubDate) {
    return Optional.ofNullable(pubDate)
        .map(
            pd ->
                (pd.isEqual(minPublishDate) || pd.isAfter(minPublishDate))
                    && (pd.isEqual(maxPublishDate) || pd.isBefore(maxPublishDate)))
        .orElse(false);
  }

  boolean hasRestrictedPublisher(List<PublisherInfo> publishers) {
    if (CollectionUtils.isEmpty(restrictedPublishers) || CollectionUtils.isEmpty(publishers)) {
      return false;
    }

    return publishers.stream()
        .map(p -> p.getPublisherName().trim())
        .anyMatch(publisherName -> restrictedPublishers.contains(publisherName));
  }
}

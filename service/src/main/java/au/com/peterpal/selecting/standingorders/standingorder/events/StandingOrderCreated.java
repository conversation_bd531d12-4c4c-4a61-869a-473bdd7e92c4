package au.com.peterpal.selecting.standingorders.standingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.standingorder.commands.CreateStandingOrderCmd;
import au.com.peterpal.selecting.standingorders.standingorder.model.Term;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.UUID;

@Getter
@ToString
@SuperBuilder
public class StandingOrderCreated extends DomainEvent {

  private String standingOrderNumber;

  private String description;

  private String notes;

  @Setter
  private List<Term> terms;

  public static StandingOrderCreated fromCmd(CreateStandingOrderCmd cmd) {
    return Optional.ofNullable(cmd)
        .map(c -> StandingOrderCreated.builder()
            .id(UUID.randomUUID())
            .standingOrderNumber(cmd.getStandingOrderNumber())
            .username(cmd.getUsername())
            .description(cmd.getDescription())
            .notes(cmd.getNotes())
            .terms(cmd.getTerms())
            .build()
        )
        .orElseThrow(() -> new IllegalArgumentException("CreateStandingOrderCmd must not be null"));
  }
  public static StandingOrderCreated fromEvent(StandingOrderCreated cmd) {
    return Optional.ofNullable(cmd)
        .map(c -> StandingOrderCreated.builder()
            .id(UUID.randomUUID())
            .standingOrderNumber(cmd.getStandingOrderNumber())
            .username(cmd.getUsername())
            .description(cmd.getDescription())
            .notes(cmd.getNotes())
            .terms(cmd.getTerms())
            .build()
        )
        .orElseThrow(() -> new IllegalArgumentException("CreateStandingOrderCmd must not be null"));
  }
}

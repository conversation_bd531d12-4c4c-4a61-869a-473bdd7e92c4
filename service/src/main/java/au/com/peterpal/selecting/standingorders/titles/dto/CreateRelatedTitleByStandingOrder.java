package au.com.peterpal.selecting.standingorders.titles.dto;

import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus;
import lombok.*;

import javax.validation.constraints.NotNull;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class CreateRelatedTitleByStandingOrder {
  @NotNull @NonNull TitleId originalTitleId;
  @NotNull @NonNull StandingOrderId targetStandingOrderId;
  TitleStatus relatedTitleStatus;

  public static CreateRelatedTitleByStandingOrder from(
      TitleId titleId, StandingOrderId standingOrderId) {
    return CreateRelatedTitleByStandingOrder.builder()
        .originalTitleId(titleId)
        .targetStandingOrderId(standingOrderId)
        .relatedTitleStatus(TitleStatus.PENDING)
        .build();
  }
}

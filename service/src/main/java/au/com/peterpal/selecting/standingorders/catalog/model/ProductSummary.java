package au.com.peterpal.selecting.standingorders.catalog.model;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Embeddable
public class ProductSummary {

  private String ean;
  private String isbn;
  private String isbn10;
  private String publisherNumber;

  @Basic(optional=false)
  @Builder.Default
  private ProductTitle title = new ProductTitle();

  private Integer editionNumber;

  private String editionStatement;

  private String seriesTitle;

  private String numberWithinSeries;

  private String authors;

  private String authorsInverted;

  private String imprint;

  private String publisher;

  @Enumerated(EnumType.STRING)
  private ProductFormCode form;

  private String formDetailCodes;

  private String contentTypeCodes;

  private String audienceCodes;

  private String audienceDescription;

  @Enumerated(EnumType.STRING)
  private LanguageCode languageCode;

  private String placeOfPublication;

  @Enumerated(EnumType.STRING)
  private CountryCode countryOfPublication;

  @Enumerated(EnumType.STRING)
  @Column(nullable=false)
  @Builder.Default
  private PublishingStatusCode publishingStatus = PublishingStatusCode.UNSPEC;

  private String publishingStatusNote;

  //@Temporal(TemporalType.DATE)
  private OffsetDateTime announcementDate;

  //@Temporal(TemporalType.DATE)
  private OffsetDateTime embargoDate;

  //@Temporal(TemporalType.DATE)
  private OffsetDateTime publicationDate;

  private Integer copyrightYear;

  private Integer yearFirstPublished;

  //@Temporal(TemporalType.DATE)
  private OffsetDateTime outOfPrintDate;

  private String localAgentName;

  @Enumerated(EnumType.STRING)
  private AgentRoleCode localAgentRole;

  @Enumerated(EnumType.STRING)
  private MarketPublishingStatusCode localPublishingStatus;

  //@Temporal(TemporalType.DATE)
  private OffsetDateTime localPublicationDate;

  //@Temporal(TemporalType.DATE)
  private OffsetDateTime localEmbargoDate;

  //@Temporal(TemporalType.DATE)
  private OffsetDateTime publicationMonth;

  @Basic(optional=false)
  @Builder.Default
  private Dewey dewey = new Dewey();

  private String bicSubjectCodes;

  @Basic(optional = true)
  @Builder.Default
  private Boolean coreSelection = false;

  //@Temporal(TemporalType.DATE)
  private OffsetDateTime coreSelectionDate;

  private String annotation;

  private String dimensionsStatement;

  private Float height; // mm

  private Float width; // mm

  private Float thickness; // mm

  private Float weight; // grams

  private Integer numberOfPages;

  private String paginationStatement;

  private String illustrationsNote;

  private String mapScale;

  private Integer numberOfWords;

  private Float durationInMinutes;

  private MediaFile thumbnailImage;

  @Column(precision=9, scale=2)
  private BigDecimal rrpAmount;

  @Enumerated(EnumType.STRING)
  private CurrencyCode rrpCurrencyCode;

  @Enumerated(EnumType.STRING)
  private ProductAvailabilityCode productAvailability;

  private String notForSaleCountries;

  @Basic(optional=false)
  @Builder.Default
  private Integer rank = 0;
}

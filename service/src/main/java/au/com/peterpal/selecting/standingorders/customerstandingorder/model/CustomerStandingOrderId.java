package au.com.peterpal.selecting.standingorders.customerstandingorder.model;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class CustomerStandingOrderId extends UuidEntityId {

  public static CustomerStandingOrderId of(@NotEmpty UUID id) {
    return new CustomerStandingOrderId(id);
  }

  public static CustomerStandingOrderId of(@NotEmpty String id) {
    return new CustomerStandingOrderId(UUID.fromString(id));
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public CustomerStandingOrderId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public CustomerStandingOrderId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public CustomerStandingOrderId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

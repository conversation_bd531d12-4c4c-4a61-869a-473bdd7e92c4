package au.com.peterpal.selecting.standingorders.titles.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import java.util.Optional;

import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

@Getter
@SuperBuilder
public class TitlePending extends DomainEvent {
  private TitleStatus titleStatus;

  public static TitlePending from(Title title) {
    return from(title, null);
  }

  public static TitlePending from(Title title, String user) {
    return Optional.ofNullable(title)
        .map(
            t ->
                TitlePending.builder()
                    .id(title.getTitleId().getId())
                    .titleStatus(title.getTitleStatus())
                    .username(StringUtils.isBlank(user) ? "system" : user)
                    .build())
        .orElseThrow(() -> new IllegalArgumentException("Title must not be null"));
  }
}

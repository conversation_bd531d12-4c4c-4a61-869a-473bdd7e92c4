package au.com.peterpal.selecting.standingorders.titles.dto;

import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus;
import lombok.*;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@ToString
public class BulkUpdateTitleRequest {
    TitleSearchRequest searchRequest;
    List<TitleId> includedTitleIds;
    List<TitleId> excludedTitleIds;

    String updatedCategory;
    TitleStatus updatedStatus;
    RejectTitleRequest rejectTitleRequest;
}

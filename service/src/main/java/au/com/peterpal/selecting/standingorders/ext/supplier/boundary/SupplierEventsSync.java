package au.com.peterpal.selecting.standingorders.ext.supplier.boundary;

import au.com.peterpal.selecting.standingorders.ext.supplier.boundary.dto.SupplierMessage;
import au.com.peterpal.selecting.standingorders.ext.supplier.control.SupplierSyncService;
import java.util.UUID;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.context.IntegrationContextUtils;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
//import org.springframework.integration.dsl.MessageChannels;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.dsl.channel.MessageChannels;
import org.springframework.integration.jms.dsl.Jms;
import org.springframework.messaging.MessageChannel;

import javax.jms.ConnectionFactory;

@Log4j2
@Configuration
@EnableIntegration
public class SupplierEventsSync {

  private static final String CLIENT_ID = "standing-orders:suppliers";

  private final SupplierSyncService supplierSyncService;
  private final ConnectionFactory connectionFactory;

  @Value("${events.suppliers.channel:suppliers-events-topic}")
  private String supplierChannelName;

  @Value("${events.suppliers.channel.subscription:supplier-events}")
  private String supplierEventsSubscriptionName;

  public SupplierEventsSync(
      SupplierSyncService supplierSyncService, ConnectionFactory connectionFactory) {
    this.supplierSyncService = supplierSyncService;
    this.connectionFactory = connectionFactory;
  }

  @Bean
  public MessageChannel supplierChannel() {
    return MessageChannels.direct().get();
  }

  @Bean
  public IntegrationFlow supplierEventListener() {
    return IntegrationFlows.from(
            Jms.messageDrivenChannelAdapter(connectionFactory)
                .destination(supplierChannelName)
                .configureListenerContainer(
                    spec -> {
                      spec.pubSubDomain(true);
                      spec.durableSubscriptionName(supplierEventsSubscriptionName);
                      spec.clientId(CLIENT_ID);
                      spec.recoveryInterval(60000L);
                    })
                .errorChannel(IntegrationContextUtils.ERROR_CHANNEL_BEAN_NAME))
        .channel(supplierChannel())
        .get();
  }

  @Bean
  public IntegrationFlow supplierProcessFlow() {
    return IntegrationFlows.from(supplierChannel())
        .log(msg -> "Supplier event received: " + msg)
        .transform(Transformers.fromJson(SupplierMessage.class))
        .log(msg -> "Processing message: " + msg)
        .<SupplierMessage>handle((msg, headers) -> this.supplierSyncService.handle(msg))
        .log(msg -> "Supplier synchronization completed")
        .get();
  }
}

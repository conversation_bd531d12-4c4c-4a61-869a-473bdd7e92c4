package au.com.peterpal.selecting.standingorders.config;

import au.com.peterpal.selecting.standingorders.standingorder.dto.StandingOrderUpdatedMessage;
import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderCreated;
import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderUpdated;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import javax.jms.ConnectionFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.Transformers;
import org.springframework.integration.dsl.channel.MessageChannels;
import org.springframework.integration.handler.LoggingHandler;
import org.springframework.integration.jms.dsl.Jms;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;

@Log4j2
@Configuration
@RequiredArgsConstructor
@EnableIntegration
public class StandingOrderGatewayService {

  private static final String MESSAGE_TYPE = "message_type";
  private static final String STANDING_ORDER_UPDATED = "standing-order-updated";

  @Value("${events.standing-orders.channel:standing-orders-to-standing-orders-events-topic}")
  private String topicName;

  private final ConnectionFactory connectionFactory;


//  public StandingOrderGatewayService(ConnectionFactory connectionFactory) {
//    this.connectionFactory = connectionFactory;
//  }

  @Bean
  public MessageChannel standingOrdersSyncChannel() {
    return MessageChannels.direct().get();
  }

  @EventListener
  public void on(final StandingOrderCreated standingOrderCreated) {
    Affirm.of(standingOrderCreated).notNull("StandingOrderCreated must not be null");
    log.debug("Sending standingOrderCreated to other instances: " + standingOrderCreated);
    StandingOrderUpdated standingOrderUpdated = StandingOrderUpdated.from(standingOrderCreated);
    Message msg = MessageBuilder.withPayload(StandingOrderUpdatedMessage.getInstanceForTesting(standingOrderUpdated))
        .build();
    standingOrdersSyncChannel().send(msg);
  }

  @EventListener
  public void on(final StandingOrderUpdated standingOrderUpdated) {
    Affirm.of(standingOrderUpdated).notNull("StandingOrderUpdated must not be null");
    Affirm.of(standingOrderUpdated.getId())
        .notNull(String.format("Standing order id must not be null: %s", standingOrderUpdated));
    log.debug("Sending standingOrderCreated to other instances: " + standingOrderUpdated);
    Message msg = MessageBuilder.withPayload(StandingOrderUpdatedMessage.getInstanceForTesting(standingOrderUpdated))
        .build();
    standingOrdersSyncChannel().send(msg);
  }


  @Bean
  public IntegrationFlow standingOrderSyncMessageFlow() {
    return IntegrationFlows.from(standingOrdersSyncChannel())
        .enrichHeaders(
            headerEnricherSpec -> headerEnricherSpec.header(MESSAGE_TYPE, STANDING_ORDER_UPDATED,
                true))
        .transform(Transformers.toJson(), e -> e.transactional(true))
        .log(LoggingHandler.Level.INFO,
            message -> String.format("Publishing %s on %s", message.getPayload(), topicName))
        .handle(Jms.outboundAdapter(connectionFactory)
            .destination(topicName)
            .configureJmsTemplate(
                jmsTemplateSpec -> {
                  jmsTemplateSpec.deliveryPersistent(true);
                  jmsTemplateSpec.pubSubDomain(true);
                  jmsTemplateSpec.sessionTransacted(true);
                }))
        .get();
  }
}

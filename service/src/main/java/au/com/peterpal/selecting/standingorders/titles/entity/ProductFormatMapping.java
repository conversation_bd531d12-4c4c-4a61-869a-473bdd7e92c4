package au.com.peterpal.selecting.standingorders.titles.entity;

import au.com.peterpal.selecting.standingorders.catalog.model.ProductFormCode;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.Format;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@Entity
@Table(name = "product_format_mapping")
public class ProductFormatMapping {

  @Id
  @Enumerated(EnumType.STRING)
  private ProductFormCode formCode;

  @Enumerated(EnumType.STRING)
  private Format format;
}

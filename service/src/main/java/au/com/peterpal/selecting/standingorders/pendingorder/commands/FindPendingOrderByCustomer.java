package au.com.peterpal.selecting.standingorders.pendingorder.commands;

import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import lombok.*;

import javax.validation.constraints.NotNull;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class FindPendingOrderByCustomer {

  @NonNull @NotNull private CustomerId customerId;
}

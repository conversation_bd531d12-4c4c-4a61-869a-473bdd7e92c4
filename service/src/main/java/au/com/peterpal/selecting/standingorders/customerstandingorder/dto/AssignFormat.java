package au.com.peterpal.selecting.standingorders.customerstandingorder.dto;

import au.com.peterpal.selecting.standingorders.customerstandingorder.model.Format;
import lombok.*;

import javax.validation.constraints.NotNull;

@Value
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class AssignFormat {
    @NotNull
    private Format format;
}

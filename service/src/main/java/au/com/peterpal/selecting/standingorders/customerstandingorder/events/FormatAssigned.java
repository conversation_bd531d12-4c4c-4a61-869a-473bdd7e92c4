package au.com.peterpal.selecting.standingorders.customerstandingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.Format;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class FormatAssigned extends DomainEvent {

  private Format format;
  private Allocation allocation;

  public static FormatAssigned from(Allocation allocation, String username) {

    return FormatAssigned.builder()
        .id(allocation.getAllocationId().getId())
        .allocation(allocation)
        .username(username)
        .build();
  }
}

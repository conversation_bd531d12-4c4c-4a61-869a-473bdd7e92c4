package au.com.peterpal.selecting.standingorders.allocationpreference.control;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreferenceId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ReleasePreferenceRepository  extends JpaRepository<ReleasePreference, ReleasePreferenceId> {
   Optional<ReleasePreference> findByAllocationPreference_AllocationPreferenceId(AllocationPreferenceId allocationPreferenceId);
}

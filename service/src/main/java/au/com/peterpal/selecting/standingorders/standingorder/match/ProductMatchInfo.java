package au.com.peterpal.selecting.standingorders.standingorder.match;

import au.com.peterpal.selecting.standingorders.catalog.model.ContributorRoleCode;
import au.com.peterpal.selecting.standingorders.catalog.model.LanguageCode;
import au.com.peterpal.selecting.standingorders.catalog.model.ProductFormCode;
import au.com.peterpal.selecting.standingorders.catalog.model.ProductFormDetail;
import au.com.peterpal.selecting.standingorders.catalog.model.PublishingRoleCode;
import au.com.peterpal.selecting.standingorders.catalog.model.TitleTypeCode;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.EnumSet;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;

import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.CwProductInfo;
import au.com.peterpal.selecting.standingorders.products.entity.ProductId;
import au.com.peterpal.selecting.standingorders.utils.StringHelper;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

@With
@Value
@NoArgsConstructor(force = true)
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductMatchInfo {
  @NotNull
  Integer id;
  ProductId productId;

  String bicSubjectCode1;
  String bicSubjectCode2;
  String bicSubjectCode3;
  String bicSubjectCode4;
  String bicSubjectCode5;

  @NotNull
  String productReference;

  @NotNull
  TitleInfo title;

  @Builder.Default
  ProductFormCode formCode = ProductFormCode.UN;

  @NotNull
  LanguageCode languageCode;

  TitleInfo seriesTitle;
  List<String> imprintNames;
  List<ContributorInfo> contributors;
  List<PublisherInfo> publishers;
  String editionStatement;
  List<String> subjectCodeList;
  LocalDate publicationDate;
  Integer height;
  Integer width;
  List<ProductFormDetail> formDetails;
  LocalDate initialImportDate;

  public String getPersonName(
      EnumSet<ContributorRoleCode> validContributorCodes, String defaultValue) {
    return Optional.ofNullable(getContributors()).orElse(Lists.newArrayList()).stream()
        .filter(c -> validContributorCodes.contains(c.getRole()))
        .min(Comparator.comparing(c -> c.getRole().ordinal()))
        .map(ProductMatchInfo.ContributorInfo::getPersonNameInverted)
        .orElse(defaultValue);
  }

  public String getImprint() {
    return CollectionUtils.isNotEmpty(getImprintNames()) ? String.join(",", getImprintNames()) : "";
  }

  public ProductMatchInfo trim() {
    return ProductMatchInfo.builder()
      .id(id)
      .bicSubjectCode1(StringHelper.of(bicSubjectCode1).trim())
      .productReference(StringHelper.of(productReference).trim())
      .formCode(formCode)
      .languageCode(languageCode)
      .title(title.trim())
      .seriesTitle(seriesTitle == null ? null : seriesTitle.trim())
      .editionStatement(editionStatement == null ? null : editionStatement.trim())
      .publicationDate(publicationDate)
      .height(height)
      .width(width)
      .formDetails(formDetails)
      .imprintNames(Optional.ofNullable(imprintNames)
          .map(l -> l.stream()
              .map(str -> str.trim())
              .collect(Collectors.toList())
          )
          .orElse(null)
      )
      .contributors(Optional.ofNullable(contributors)
          .map(c -> c.stream()
              .map(info -> info.trim())
              .collect(Collectors.toList())
          )
          .orElse(null)
      )
      .publishers(Optional.ofNullable(publishers)
          .map(p -> p.stream()
              .map(info -> info.trim())
              .collect(Collectors.toList())
          )
          .orElse(null)
      )
      .subjectCodeList(Optional.ofNullable(subjectCodeList)
          .map(l -> l.stream()
              .map(str -> str.trim())
              .collect(Collectors.toList())
          )
          .orElse(null)
      )
      .build();
  }

  private List<String> trim(List<String> strList) {
    return Optional.ofNullable(strList)
            .map(l -> l.stream()
                    .map(str -> str.trim())
                    .collect(Collectors.toList())
            )
            .orElse(null);
  }

  public boolean isInitialImportDateAfter(LocalDate date) {
    return Optional.ofNullable(initialImportDate).map(iid -> iid.isAfter(date)).orElse(false);
  }

  public boolean isInitialImportDateBefore(LocalDate date) {
    return Optional.ofNullable(initialImportDate).map(iid -> iid.isBefore(date)).orElse(false);
  }

  @Value
  @Builder
  @NoArgsConstructor(force = true)
  @AllArgsConstructor
  @JsonInclude(JsonInclude.Include.NON_NULL)
  public static class TitleInfo {
    @Builder.Default
    private TitleTypeCode type = TitleTypeCode.TITLE;

    private String text;
    private String prefix;
    private String withoutPrefix;
    private String subtitle;

    public TitleInfo trim() {
      return TitleInfo.builder()
        .text(StringHelper.of(text).trim())
        .prefix(StringHelper.of(prefix).trim())
        .withoutPrefix(StringHelper.of(withoutPrefix).trim())
        .subtitle(StringHelper.of(subtitle).trim())
        .build();
    }
  }

  @With
  @Value
  @Builder
  @NoArgsConstructor(force = true)
  @AllArgsConstructor
  @JsonInclude(JsonInclude.Include.NON_NULL)
  public static class ContributorInfo {
    private ContributorRoleCode role;
    private String personName;
    private String personNameInverted;

    public ContributorInfo trim() {
      return ContributorInfo.builder()
        .role(role)
        .personName(StringHelper.of(personName).trim())
        .personNameInverted(StringHelper.of(personNameInverted).trim())
        .build();
    }
  }

  @Value
  @Builder
  @NoArgsConstructor(force = true)
  @AllArgsConstructor
  @JsonInclude(JsonInclude.Include.NON_NULL)
  public static class PublisherInfo {
    @Builder.Default
    private PublishingRoleCode roleCode = PublishingRoleCode.PUB;

    private String publisherName;

    public PublisherInfo trim() {
      return PublisherInfo.builder()
        .roleCode(roleCode)
        .publisherName(StringHelper.of(publisherName).trim())
        .build();
    }
  }

  public static ProductMatchInfo from(CwProductInfo product) {
    return ProductMatchInfo.builder()
        .id(product.getId())
        .productReference(product.getProductReference())
        .title(
            TitleInfo.builder()
                .text(product.getTitle().getText())
                .withoutPrefix(product.getTitle().getWithoutPrefix())
                .prefix(product.getTitle().getPrefix())
                .subtitle(product.getTitle().getSubtitle())
                .build())
        .bicSubjectCode1(product.getBicSubjectCode1())
        .publicationDate(product.getPublicationDate())
        .editionStatement(product.getEditionStatement())
        .formCode(product.getFormCode())
        .languageCode(product.getLanguageCode())
        .seriesTitle(
            Optional.ofNullable(product.getSeriesTitle())
                .map(t -> TitleInfo.builder().text(t.getText()).build())
                .orElse(null))
        .height(product.getHeight())
        .width(product.getWidth())
        .imprintNames(product.getImprintNames())
        .contributors(
            product.getContributors().stream()
                .map(
                    c ->
                        ContributorInfo.builder()
                            .role(c.getRole())
                            .personName(c.getPersonName())
                            .personNameInverted(c.getPersonNameInverted())
                            .build())
                .collect(Collectors.toList()))
        .publishers(
            product.getPublishers().stream()
                .map(
                    p ->
                        PublisherInfo.builder()
                            .roleCode(p.getRoleCode())
                            .publisherName(p.getPublisherName())
                            .build())
                .collect(Collectors.toList()))
        .subjectCodeList(product.getSubjectCodes())
        .formDetails(product.getFormDetails())
        .build();
  }

  public static ProductMatchInfo buildInstanceForTesting() {
    return ProductMatchInfo.builder()
        .id(453454)
        .productReference("9787509164433")
        .title(
            TitleInfo.builder()
                .text("product.getTitle().getText()")
                .withoutPrefix("product.getTitle().getWithoutPrefix()")
                .prefix("product.getTitle().getPrefix()")
                .subtitle("product.getTitle().getSubtitle()")
                .build())
        .bicSubjectCode1("product.getBicSubjectCode1()")
        .editionStatement("product.getEditionStatement()")
        .build();
  }
}

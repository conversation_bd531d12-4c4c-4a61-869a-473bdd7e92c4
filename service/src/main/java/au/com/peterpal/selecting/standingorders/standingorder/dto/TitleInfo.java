//package au.com.peterpal.selecting.standingorders.standingorder.dto;
//
//import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
//import au.com.peterpal.selecting.standingorders.standingorder.model.Title;
//import au.com.peterpal.selecting.standingorders.standingorder.model.TitleId;
//import au.com.peterpal.selecting.standingorders.standingorder.model.TitleStatus;
//
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import au.com.peterpal.selecting.standingorders.standingorder.model.TitleType;
//import com.fasterxml.jackson.annotation.JsonFormat;
//import java.util.Optional;
//import java.util.UUID;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//@Data
//@NoArgsConstructor
//@AllArgsConstructor
//@Builder
//public class TitleInfo {
//  private TitleId titleId;
//  private Integer productId;
//  private String category;
//  private TitleStatus titleStatus;
//  private TitleType type;
//  private ReleaseType releaseType;
//  private String title;
//  private String subtitle;
//  private String series;
//  private String productRef;
//  private String personName;
//  private String corporateName;
//  private String imprint;
//  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
//  private LocalDateTime dateAdded;
//  private LocalDate publicationDate;
//
//  private boolean hasPendingOrders;
//
//  public static TitleInfo from(Title title) {
//    return Optional.ofNullable(title)
//      .map(ti -> TitleInfo.builder()
//          .titleId(title.getTitleId())
//          .titleStatus(title.getTitleStatus())
//          .productId(title.getProductId())
//          .category(title.getCategory())
//          .releaseType(title.getReleaseType())
//          .type(title.getType())
//          .title(title.getTitle())
//          .subtitle(title.getSubtitle())
//          .series(title.getSeries())
//          .productRef(title.getIsbn())
//          .personName(title.getPersonName())
//          .corporateName(title.getCorporateName())
//          .imprint(title.getImprint())
//          .dateAdded(title.getDateAdded())
//          .publicationDate(title.getPublicationDate())
//          .build())
//      .orElse(null);
//  }
//
//  public Title toTitle() {
//    return Title.builder()
//      .titleId(Optional.ofNullable(titleId).orElse(TitleId.of(UUID.randomUUID())))
//      .category(category)
//      .titleStatus(titleStatus)
//      .type(type)
//      .releaseType(releaseType)
//      .title(title)
//      .subtitle(subtitle)
//      .series(series)
//      .isbn(productRef)
//      .personName(personName)
//      .corporateName(corporateName)
//      .imprint(imprint)
//      .dateAdded(dateAdded)
//      .publicationDate(publicationDate)
//      .productId(0)
//      .build();
//  }
//}

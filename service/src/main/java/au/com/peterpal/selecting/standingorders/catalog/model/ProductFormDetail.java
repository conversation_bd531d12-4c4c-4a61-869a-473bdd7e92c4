package au.com.peterpal.selecting.standingorders.catalog.model;

/**
 * @docRoot
 * Based on Onix 3 List 151
 */
public enum ProductFormDetail {

  A101 ("A101", "CD standard audio format", "CD ‘red book’ format."),
  A102 ("A102", "SACD super audio format", ""),
  A103 ("A103", "MP3 format", "MPEG-1/2 Audio Layer III file."),
  A104 ("A104", "WAV format", "Waveform audio file."),
  A105 ("A105", "Real Audio format", ""),
  A106 ("A106", "WMA", "Windows Media Audio format."),
  A107 ("A107", "AAC", "Advanced Audio Coding format."),
  A108 ("A108", "Ogg/Vorbis", "Vorbis audio format in the Ogg container."),
  A109 ("A109", "Audible", "Audio format proprietary to Audible.com."),
  A110 ("A110", "FLAC", "Free lossless audio codec."),
  A111 ("A111", "AIFF", "Audio Interchangeable File Format."),
  A112 ("A112", "ALAC", "Apple Lossless Audio Codec."),
  A201 ("A201", "DAISY 2: full audio with title only (no navigation)", "Deprecated, as does not meet DAISY 2 standard. Use conventional audiobook codes instead."),
  A202 ("A202", "DAISY 2: full audio with navigation (no text)", ""),
  A203 ("A203", "DAISY 2: full audio with navigation and partial text", ""),
  A204 ("A204", "DAISY 2: full audio with navigation and full text", ""),
  A205 ("A205", "DAISY 2: full text with navigation and partial audio", "Reading systems may provide full audio via text-to-speech."),
  A206 ("A206", "DAISY 2: full text with navigation and no audio", "Reading systems may provide full audio via text-to-speech."),
  A207 ("A207", "DAISY 3: full audio with title only (no navigation)", "Deprecated, as does not meet DAISY 3 standard. Use conventional audiobook codes instead."),
  A208 ("A208", "DAISY 3: full audio with navigation (no text)", ""),
  A209 ("A209", "DAISY 3: full audio with navigation and partial text", ""),
  A210 ("A210", "DAISY 3: full audio with navigation and full text", ""),
  A211 ("A211", "DAISY 3: full text with navigation and some audio", "Reading systems may provide full audio via text-to-speech."),
  A212 ("A212", "DAISY 3: full text with navigation (no audio)", "Reading systems may provide full audio via text-to-speech."),
  A301 ("A301", "Standalone audio", ""),
  A302 ("A302", "Readalong audio", "Audio intended exclusively for use alongside a printed copy of the book. Most often a children’s product. Normally contains instructions such as “turn the page now” and other references to the printed item, and is usually sold packaged together with a printed copy."),
  A303 ("A303", "Playalong audio", "Audio intended for musical accompaniment, eg ‘Music minus one’, etc, often used for music learning. Includes singalong backing audio for musical learning or for Karaoke-style entertainment."),
  A304 ("A304", "Speakalong audio", "Audio intended for language learning, which includes speech plus gaps intended to be filled by the listener."),
  A305 ("A305", "Synchronised audio", "Audio synchronised to text within an e-publication, for example an EPUB3 with audio overlay. Synchronisation at least at paragraph level, and covering the full content."),
  A410 ("A410", "Mono", "Includes 'stereo' where channels are identical."),
  A420 ("A420", "Stereo", ""),
  A421 ("A421", "Stereo 2.1", "Stereo plus low-frequency channel."),
  A441 ("A441", "Surround 4.1", "Five-channel audio (including low-frequency channel)."),
  A451 ("A451", "Surround 5.1", "Six-channel audio (including low-frequency channel)."),
  B101 ("B101", "Mass market (rack) paperback", "In North America, a category of paperback characterized partly by page size (typically 4¼ x 7 1/8 inches) and partly by target market and terms of trade. Use with Product Form code BC."),
  B102 ("B102", "Trade paperback (US)", "In North America, a category of paperback characterized partly by page size and partly by target market and terms of trade. AKA ‘quality paperback’, and including textbooks. Most paperback books sold in North America except ‘mass-market’ (B101) and ‘tall rack’ (B107) are correctly described with this code. Use with Product Form code BC."),
  B103 ("B103", "Digest format paperback", "In North America, a category of paperback characterized by page size and generally used for children’s books; use with Product Form code BC. Note: was wrongly shown as B102 (duplicate entry) in Issue 3."),
  B104 ("B104", "A-format paperback", "In UK, a category of paperback characterized by page size (normally 178 x 111 mm approx); use with Product Form code BC."),
  B105 ("B105", "B-format paperback", "In UK, a category of paperback characterized by page size (normally 198 x 129 mm approx); use with Product Form code BC."),
  B106 ("B106", "Trade paperback (UK)", "In UK, a category of paperback characterized partly by size (usually in traditional hardback dimensions), and often used for paperback originals; use with Product Form code BC (replaces ‘C-format’ from former List 8)."),
  B107 ("B107", "Tall rack paperback (US)", "In North America, a category of paperback characterised partly by page size and partly by target market and terms of trade; use with Product Form code BC."),
  B108 ("B108", "A5 size Tankobon", "210x148mm."),
  B109 ("B109", "JIS B5 size Tankobon", "Japanese B-series size, 257x182mm."),
  B110 ("B110", "JIS B6 size Tankobon", "Japanese B-series size, 182x128mm."),
  B111 ("B111", "A6 size Bunko", "148x105mm."),
  B112 ("B112", "B40-dori Shinsho", "Japanese format, 182x103mm or 173x105mm."),
  B113 ("B113", "Pocket (Sweden, Norway, France)", "A Swedish, Norwegian, French paperback format, of no particular fixed size. Use with Product Form Code BC."),
  B114 ("B114", "Storpocket (Sweden)", "A Swedish paperback format, use with Product Form Code BC."),
  B115 ("B115", "Kartonnage (Sweden)", "A Swedish hardback format, use with Product Form Code BB."),
  B116 ("B116", "Flexband (Sweden)", "A Swedish softback format, use with Product Form Code BC."),
  B117 ("B117", "Mook", "In Japan, a softback book in the format of a magazine but sold like a book. Use with Product Form code BC."),
  B118 ("B118", "Dwarsligger", "Also called ‘Flipback’. A softback book in a specially compact proprietary format with pages printed in landscape on very thin paper and bound along the long (top) edge – see www.dwarsligger.com."),
  B119 ("B119", "46 size", "Japanese format: 188x127mm."),
  B120 ("B120", "46-Henkei size", "Japanese format."),
  B121 ("B121", "A4", "297x210mm."),
  B122 ("B122", "A4-Henkei size", "Japanese format."),
  B123 ("B123", "A5-Henkei size", "Japanese format."),
  B124 ("B124", "B5-Henkei size", "Japanese format."),
  B125 ("B125", "B6-Henkei size", "Japanese format."),
  B126 ("B126", "AB size", "257x210mm."),
  B127 ("B127", "JIS B7 size", "Japanese B-series size, 128x91mm."),
  B128 ("B128", "Kiku size", "Japanese format, 218x152mm or 227x152mm."),
  B129 ("B129", "Kiku-Henkei size", "Japanese format."),
  B130 ("B130", "JIS B4 size", "Japanese B-series size, 364x257mm."),
  B131 ("B131", "Paperback (DE)", "German paperback format, greater than 205mm high, with flaps. Use with Product form code BC."),
  B201 ("B201", "Coloring / join-the-dot book", ""),
  B202 ("B202", "Lift-the-flap book", ""),
  B203 ("B203", "Fuzzy book", "DEPRECATED because of ambiguity – use B210, B214 or B215 as appropriate."),
  B204 ("B204", "Miniature book", "Note: was wrongly shown as B203 (duplicate entry) in Issue 3."),
  B205 ("B205", "Moving picture / flicker book", ""),
  B206 ("B206", "Pop-up book", ""),
  B207 ("B207", "Scented / ‘smelly’ book", ""),
  B208 ("B208", "Sound story / ‘noisy’ book", ""),
  B209 ("B209", "Sticker book", ""),
  B210 ("B210", "Touch-and-feel book", "A book whose pages have a variety of textured inserts designed to stimulate tactile exploration: see also B214 and B215."),
  B211 ("B211", "Toy / die-cut book", "DEPRECATED – use B212 or B213 as appropriate."),
  B212 ("B212", "Die-cut book", "A book which is cut into a distinctive non-rectilinear shape and/or in which holes or shapes have been cut internally. (‘Die-cut’ is used here as a convenient shorthand, and does not imply strict limitation to a particular production process.)."),
  B213 ("B213", "Book-as-toy", "A book which is also a toy, or which incorporates a toy as an integral part. (Do not, however, use B213 for a multiple-item product which includes a book and a toy as separate items.)."),
  B214 ("B214", "Soft-to-touch book", "A book whose cover has a soft textured finish, typically over board."),
  B215 ("B215", "Fuzzy-felt book", "A book with detachable felt pieces and textured pages on which they can be arranged."),
  B221 ("B221", "Picture book", "Children’s picture book: use with applicable Product Form code."),
  B222 ("B222", "‘Carousel’ book", "(aka ‘Star’ book). Tax treatment of products may differ from that of products with similar codes such as Book as toy or Pop-up book)."),
  B301 ("B301", "Loose leaf – sheets and binder", "Use with Product Form code BD."),
  B302 ("B302", "Loose leaf – binder only", "Use with Product Form code BD."),
  B303 ("B303", "Loose leaf – sheets only", "Use with Product Form code BD."),
  B304 ("B304", "Sewn", "AKA stitched; for ‘saddle-sewn’, see code B310."),
  B305 ("B305", "Unsewn / adhesive bound", "Including ‘perfect bound’, ‘glued’."),
  B306 ("B306", "Library binding", "Strengthened cloth-over-boards binding intended for libraries: use with Product form code BB."),
  B307 ("B307", "Reinforced binding", "Strengthened binding, not specifically intended for libraries."),
  B308 ("B308", "Half bound", "Must be accompanied by a code specifiying a material, eg ‘half-bound real leather’."),
  B309 ("B309", "Quarter bound", "Must be accompanied by a code specifiying a material, eg ‘quarter bound real leather’."),
  B310 ("B310", "Saddle-sewn", "AKA ‘saddle-stitched’ or ‘wire-stitched’."),
  B311 ("B311", "Comb bound", "Round or oval plastic forms in a clamp-like configuration: use with Product Form code BE."),
  B312 ("B312", "Wire-O", "Twin loop metal wire spine: use with Product Form code BE."),
  B313 ("B313", "Concealed wire", "Cased over Coiled or Wire-O binding: use with Product Form code BE and Product Form Detail code B312 or B315."),
  B314 ("B314", "Coiled wire bound", "Spiral wire bound. Use with product form code BE. The default if a spiral binding type is not stated. Cf. Comb and Wire-O binding."),
  B315 ("B315", "Trade binding", "Hardcover binding intended for general consumers rather than libraries, use with Product form code BB. The default if a hardcover binding detail is not stated. cf. Library binding."),
  B400 ("B400", "Self-cover", "Covers do not use a distinctive stock, but are the same as the body pages."),
  B401 ("B401", "Cloth over boards", "AKA fabric, linen over boards."),
  B402 ("B402", "Paper over boards", ""),
  B403 ("B403", "Leather, real", ""),
  B404 ("B404", "Leather, imitation", ""),
  B405 ("B405", "Leather, bonded", ""),
  B406 ("B406", "Vellum", ""),
  B407 ("B407", "Plastic", "DEPRECATED – use new B412 or B413 as appropriate."),
  B408 ("B408", "Vinyl", "DEPRECATED – use new B412 or B414 as appropriate."),
  B409 ("B409", "Cloth", "Cloth, not necessarily over boards – cf B401."),
  B410 ("B410", "Imitation cloth", "Spanish ‘simil-tela’."),
  B411 ("B411", "Velvet", ""),
  B412 ("B412", "Flexible plastic/vinyl cover", "AKA ‘flexibound’: use with Product Form code BC."),
  B413 ("B413", "Plastic-covered", ""),
  B414 ("B414", "Vinyl-covered", ""),
  B415 ("B415", "Laminated cover", "Book, laminating material unspecified: use L101 for ‘whole product laminated’, eg a laminated sheet map or wallchart."),
  B416 ("B416", "Card cover", "With card cover (like a typical paperback). As distinct from a self-cover or more elaborate binding."),
  B501 ("B501", "With dust jacket", "Type unspecified."),
  B502 ("B502", "With printed dust jacket", "Used to distinguish from B503."),
  B503 ("B503", "With translucent dust cover", "With translucent paper or plastic protective cover."),
  B504 ("B504", "With flaps", "For paperback with flaps."),
  B505 ("B505", "With thumb index", ""),
  B506 ("B506", "With ribbon marker(s)", "If the number of markers is significant, it can be stated as free text in <ProductFormDescription>."),
  B507 ("B507", "With zip fastener", ""),
  B508 ("B508", "With button snap fastener", ""),
  B509 ("B509", "With leather edge lining", "AKA yapp edge?."),
  B510 ("B510", "Rough front", "With edge trimming such that the front edge is ragged, not neatly and squarely trimmed: AKA deckle edge, feather edge, uncut edge, rough cut."),
  B511 ("B511", "With foldout", "With one or more gatefold or foldout sections bound in."),
  B512 ("B512", "Wide margin", "Pages include extra-wide margin specifically intended for hand-written annotations."),
  B513 ("B513", "With fastening strap", "Book with attached loop for fixing to baby stroller, cot, chair etc."),
  B514 ("B514", "With perforated pages", "With one or more pages perforated and intended to be torn out for use."),
  B601 ("B601", "Turn-around book", "A book in which half the content is printed upside-down, to be read the other way round. Also known as a ‘flip-book’, ‘back-to-back’, (fr.) ‘tête-bêche’ (usually an omnibus of two works)."),
  B602 ("B602", "Unflipped manga format", "Manga with pages and panels in the sequence of the original Japanese, but with Western text."),
  B610 ("B610", "Syllabification", "Text shows syllable breaks."),
  B701 ("B701", "UK Uncontracted Braille", "Single letters only. Was formerly identified as UK Braille Grade 1."),
  B702 ("B702", "UK Contracted Braille", "With some letter combinations. Was formerly identified as UK Braille Grade 2."),
  B703 ("B703", "US Braille", "DEPRECATED- use B704/B705 as appropriate instead."),
  B704 ("B704", "US Uncontracted Braille", ""),
  B705 ("B705", "US Contracted Braille", ""),
  B706 ("B706", "Unified English Braille", ""),
  B707 ("B707", "Moon", "Moon embossed alphabet, used by some print-impaired readers who have difficulties with Braille."),
  D101 ("D101", "Real Video format", "Includes RealVideo packaged within a .rm RealMedia container."),
  D102 ("D102", "Quicktime format", ""),
  D103 ("D103", "AVI format", ""),
  D104 ("D104", "Windows Media Video format", ""),
  D105 ("D105", "MPEG-4", ""),
  D201 ("D201", "MS-DOS", "Use with an applicable Product Form code D*; note that more detail of operating system requirements can be given in a Product Form Feature composite."),
  D202 ("D202", "Windows", "Use with an applicable Product Form code D*; see note on D201."),
  D203 ("D203", "Macintosh", "Use with an applicable Product Form code D*; see note on D201."),
  D204 ("D204", "UNIX / LINUX", "Use with an applicable Product Form code D*; see note on D201."),
  D205 ("D205", "Other operating system(s)", "Use with an applicable Product Form code D*; see note on D201."),
  D206 ("D206", "Palm OS", "Use with an applicable Product Form code D*; see note on D201."),
  D207 ("D207", "Windows Mobile", "Use with an applicable Product Form code D*; see note on D201."),
  D301 ("D301", "Microsoft XBox", "Use with Product Form code DE or DB as applicable."),
  D302 ("D302", "Nintendo Gameboy Color", "Use with Product Form code DE or DB as applicable."),
  D303 ("D303", "Nintendo Gameboy Advanced", "Use with Product Form code DE or DB as applicable."),
  D304 ("D304", "Nintendo Gameboy", "Use with Product Form code DE or DB as applicable."),
  D305 ("D305", "Nintendo Gamecube", "Use with Product Form code DE or DB as applicable."),
  D306 ("D306", "Nintendo 64", "Use with Product Form code DE or DB as applicable."),
  D307 ("D307", "Sega Dreamcast", "Use with Product Form code DE or DB as applicable."),
  D308 ("D308", "Sega Genesis/Megadrive", "Use with Product Form code DE or DB as applicable."),
  D309 ("D309", "Sega Saturn", "Use with Product Form code DE or DB as applicable."),
  D310 ("D310", "Sony PlayStation 1", "Use with Product Form code DE or DB as applicable."),
  D311 ("D311", "Sony PlayStation 2", "Use with Product Form code DE or DB as applicable."),
  D312 ("D312", "Nintendo Dual Screen", ""),
  D313 ("D313", "Sony PlayStation 3", ""),
  D314 ("D314", "Xbox 360", ""),
  D315 ("D315", "Nintendo Wii", ""),
  D316 ("D316", "Sony PlayStation Portable (PSP)", ""),
  E100 ("E100", "Other", "No code allocated for this e-publication format yet."),
  E101 ("E101", "EPUB", "The Open Publication Structure / OPS Container Format standard of the International Digital Publishing Forum (IDPF) [File extension .epub]."),
  E102 ("E102", "OEB", "The Open EBook format of the IDPF, a predecessor of the full EPUB format, still (2008) supported as part of the latter [File extension .opf]. Includes EPUB format up to and including version 2 – but prefer code E101 for EPUB 2, and always use code E101 for EPUB 3."),
  E103 ("E103", "DOC", "Microsoft Word binary document format [File extension .doc]."),
  E104 ("E104", "DOCX", "Office Open XML / Microsoft Word XML document format (ISO/IEC 29500:2008) [File extension .docx]."),
  E105 ("E105", "HTML", "HyperText Mark-up Language [File extension .html, .htm]."),
  E106 ("E106", "ODF", "Open Document Format [File extension .odt]."),
  E107 ("E107", "PDF", "Portable Document Format (ISO 32000-1:2008) [File extension .pdf]."),
  E108 ("E108", "PDF/A", "PDF archiving format defined by ISO 19005-1:2005 [File extension .pdf]."),
  E109 ("E109", "RTF", "Rich Text Format [File extension .rtf]."),
  E110 ("E110", "SGML", "Standard Generalized Mark-up Language."),
  E111 ("E111", "TCR", "A compressed text format mainly used on Psion handheld devices [File extension .tcr]."),
  E112 ("E112", "TXT", "Text file format [File extension .txt]."),
  E113 ("E113", "XHTML", "Extensible Hypertext Markup Language [File extension .xhtml, .xht, .xml, .html, .htm]."),
  E114 ("E114", "zTXT", "A compressed text format mainly used on Palm handheld devices [File extension .pdb – see also E121, E125, E130]."),
  E115 ("E115", "XPS", "XML Paper Specification format [File extension .xps]."),
  E116 ("E116", "Amazon Kindle", "A format proprietary to Amazon for use with its Kindle reading devices or software readers [File extensions .azw, .mobi, .prc]."),
  E117 ("E117", "BBeB", "A Sony proprietary format for use with the Sony Reader and LIBRIé reading devices [File extension .lrf]."),
  E118 ("E118", "DXReader", "A proprietary format for use with DXReader software."),
  E119 ("E119", "EBL", "A format proprietary to the Ebook Library service."),
  E120 ("E120", "Ebrary", "A format proprietary to the Ebrary service."),
  E121 ("E121", "eReader", "A proprietary format for use with eReader (AKA ‘Palm Reader’) software on various hardware platforms [File extension .pdb – see also E114, E125, E130]."),
  E122 ("E122", "Exebook", "A proprietary format with its own reading system for Windows platforms [File extension .exe]."),
  E123 ("E123", "Franklin eBookman", "A proprietary format for use with the Franklin eBookman reader."),
  E124 ("E124", "Gemstar Rocketbook", "A proprietary format for use with the Gemstar Rocketbook reader [File extension .rb]."),
  E125 ("E125", "iSilo", "A proprietary format for use with iSilo software on various hardware platforms [File extension .pdb – see also E114, E121, E130]."),
  E126 ("E126", "Microsoft Reader", "A proprietary format for use with Microsoft Reader software on Windows and Pocket PC platforms [File extension .lit]."),
  E127 ("E127", "Mobipocket", "A proprietary format for use with Mobipocket software on various hardware platforms [File extensions .mobi, .prc]. Includes Amazon Kindle formats up to and including version 7 – but prefer code E116 for version 7, and always use E116 for KF8."),
  E128 ("E128", "MyiLibrary", "A format proprietary to the MyiLibrary service."),
  E129 ("E129", "NetLibrary", "A format proprietary to the NetLibrary service."),
  E130 ("E130", "Plucker", "A proprietary format for use with Plucker reader software on Palm and other handheld devices [File extension .pdb – see also E114, E121, E125]."),
  E131 ("E131", "VitalBook", "A format proprietary to the VitalSource service."),
  E132 ("E132", "Vook", "A proprietary digital product combining text and video content and available to be used online or as a downloadable application for a mobile device – see www.vook.com."),
  E133 ("E133", "Google Edition", "An epublication made available by Google in association with a publisher; readable online on a browser-enabled device and offline on designated ebook readers."),
  E134 ("E134", "Book ‘app’ for iOS", "Epublication packaged as application for iOS (eg Apple iPhone, iPad etc), containing both executable code and content. Use <ProductContentType> to describe content, and <ProductFormFeatureType> to list detailed technical requirements."),
  E135 ("E135", "Book ‘app’ for Android", "Epublication packaged as application for Android (eg Android phone or tablet), containing both executable code and content. Use <ProductContentType> to describe content, and <ProductFormFeatureType> to list detailed technical requirements."),
  E136 ("E136", "Book ‘app’ for other operating system", "Epublication packaged as application, containing both executable code and content. Use where other ‘app’ codes are not applicable. Technical requirements such as target operating system and/or device should be provided eg in <ProductFormFeatureType>. Content type (text or text plus various ‘enhancements’) may be described with <ProductContentType>."),
  E139 ("E139", "CEB", "Founder Apabi’s proprietary basic e-book format."),
  E140 ("E140", "CEBX", "Founder Apabi’s proprietary XML e-book format."),
  E141 ("E141", "iBook", "Apple’s iBook format (a proprietary extension of EPUB), can only be read on Apple iOS devices."),
  E142 ("E142", "ePIB", "Proprietary format used by Barnes and Noble, readable on NOOK devices and Nook reader software."),
  E143 ("E143", "SCORM", "Sharable Content Object Reference Model, standard content and packaging format for e-learning objects."),
  E200 ("E200", "Reflowable", "Use where a particular e-publication type (specified in <EpubType>) has both reflowable and fixed-format variants."),
  E201 ("E201", "Fixed format", "Use where a particular e-publication type (specified in <EpubType>) has both reflowable and fixed-format variants."),
  E202 ("E202", "Readable offline", "All e-publication resources are included within the e-publication package."),
  E203 ("E203", "Requires network connection", "E-publication requires a network connection to access some resources (eg an enhanced e-book where video clips are not stored within the e-publication ‘package’ itself, but are delivered via an internet connection)."),
  E204 ("E204", "Content removed", "Resources (eg images) present in other editions have been removed from this product, eg due to rights issues."),
  E210 ("E210", "Landscape", "Use for fixed-format e-books optimised for landscape display. Also include an indication of the optimal screen aspect ratio."),
  E211 ("E211", "Portrait", "Use for fixed-format e-books optimised for portrait display. Also include an indication of the optimal screen aspect ratio."),
  E221 ("E221", "0.211111111111111", "Use for fixed-format e-books optimised for displays with a 5:4 aspect ratio (eg 1280x1024 pixels etc, assuming square pixels). Note that aspect ratio codes are NOT specific to actual screen dimensions or pixel counts, but to the ratios between two dimensions or two pixel counts."),
  E222 ("E222", "0.16875", "Use for fixed-format e-books optimised for displays with a 4:3 aspect ratio (eg 800x600, 1024x768, 2048x1536 pixels etc)."),
  E223 ("E223", "0.126388888888889", "Use for fixed-format e-books optimised for displays with a 3:2 aspect ratio (eg 960x640, 3072x2048 pixels etc)."),
  E224 ("E224", "0.673611111111111", "Use for fixed-format e-books optimised for displays with a 16:10 aspect ratio (eg 1440x900, 2560x1600 pixels etc)."),
  E225 ("E225", "0.672916666666667", "Use for fixed-format e-books optimised for displays with a 16:9 aspect ratio (eg 1024x576, 1920x1080, 2048x1152 pixels etc)."),
  L101 ("L101", "Laminated", "Whole product laminated (eg laminated map, fold-out chart, wallchart, etc): use B415 for book with laminated cover."),
  P101 ("P101", "Desk calendar", "Use with Product Form code PC."),
  P102 ("P102", "Mini calendar", "Use with Product Form code PC."),
  P103 ("P103", "Engagement calendar", "Use with Product Form code PC."),
  P104 ("P104", "Day by day calendar", "Use with Product Form code PC."),
  P105 ("P105", "Poster calendar", "Use with Product Form code PC."),
  P106 ("P106", "Wall calendar", "Use with Product Form code PC."),
  P107 ("P107", "Perpetual calendar", "Use with Product Form code PC."),
  P108 ("P108", "Advent calendar", "Use with Product Form code PC."),
  P109 ("P109", "Bookmark calendar", "Use with Product Form code PC."),
  P110 ("P110", "Student calendar", "Use with Product Form code PC."),
  P111 ("P111", "Project calendar", "Use with Product Form code PC."),
  P112 ("P112", "Almanac calendar", "Use with Product Form code PC."),
  P113 ("P113", "Other calendar", "A calendar that is not one of the types specified elsewhere: use with Product Form code PC."),
  P114 ("P114", "Other calendar or organiser product", "A product that is associated with or ancillary to a calendar or organiser, eg a deskstand for a calendar, or an insert for an organiser: use with Product Form code PC or PS."),
  P201 ("P201", "Hardback (stationery)", "Stationery item in hardback book format."),
  P202 ("P202", "Paperback / softback (stationery)", "Stationery item in paperback/softback book format."),
  P203 ("P203", "Spiral bound (stationery)", "Stationery item in spiral-bound book format."),
  P204 ("P204", "Leather / fine binding (stationery)", "Stationery item in leather-bound book format, or other fine binding."),
  V201 ("V201", "PAL", "TV standard for video or DVD."),
  V202 ("V202", "NTSC", "TV standard for video or DVD."),
  V203 ("V203", "SECAM", "TV standard for video or DVD."),
  V220 ("V220", "Home use", "Licensed for use in domestic contexts only."),
  V221 ("V221", "Classroom use", "Licensed for use in education.");

  private final String code;
  private final String description;
  private final String notes;

  ProductFormDetail(String code, String description, String notes) {
    this.code = code;
    this.description = description;
    this.notes = notes;
  }

  public String getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public String getNotes() {
    return notes;
  }

  public static ProductFormDetail mapOnixCode(String onixCode) {
    return mapOnixCode(onixCode, false);
  }

  public static ProductFormDetail mapOnixCode(String onixCode, boolean silent) {
    for (ProductFormDetail value : ProductFormDetail.values()) {
      if (value.code.equals(onixCode)) {
        return value;
      }
    }
    if (silent) {
      return null;
    } else {
      throw new IllegalArgumentException(
          "Invalid " + ProductFormDetail.class.getSimpleName() + ": " + onixCode);
    }
  }

  @Override
  public String toString() {
    return getDescription();
  }
}

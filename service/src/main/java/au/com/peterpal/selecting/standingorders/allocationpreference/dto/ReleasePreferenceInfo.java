package au.com.peterpal.selecting.standingorders.allocationpreference.dto;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import lombok.*;

import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;

@With
@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class ReleasePreferenceInfo {

  @NotNull(message = "releaseType is required")
  private ReleaseType releaseType;

  private String actionType;

  private String fundCode;

  private String hbFundCode;

  private String pbFundCode;

  private String initialAssignmentRule;

  private String smallFormatPaperbackRule;

  public void validate() {
    if (StringUtils.isNotBlank(actionType)) {
      if (StringUtils.isBlank(initialAssignmentRule) ||
          StringUtils.isBlank(smallFormatPaperbackRule)) {
        throw new BusinessException("If action type has value, assignment rule and smallFormatPaperbackRule must have a value.");
      }
    }
  }
}

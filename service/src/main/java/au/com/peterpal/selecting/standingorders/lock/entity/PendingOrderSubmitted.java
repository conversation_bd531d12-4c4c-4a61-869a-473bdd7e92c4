package au.com.peterpal.selecting.standingorders.lock.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.*;
import javax.validation.constraints.NotNull;

import lombok.*;

import java.util.UUID;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "pending_order_submitted")
public class PendingOrderSubmitted {

  @EqualsAndHashCode.Include @NonNull @EmbeddedId private PendingOrderSubmittedId pendingOrderSubmittedId;
  @NotNull @NonNull
  private UUID pendingOrderId;
}

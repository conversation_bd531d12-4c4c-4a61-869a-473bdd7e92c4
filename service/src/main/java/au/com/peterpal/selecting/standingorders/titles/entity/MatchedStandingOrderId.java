package au.com.peterpal.selecting.standingorders.titles.entity;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class MatchedStandingOrderId extends UuidEntityId {

  public static MatchedStandingOrderId of(@NotEmpty UUID id) {
    return new MatchedStandingOrderId(id);
  }

  public static MatchedStandingOrderId of(@NotEmpty String id) {
    return new MatchedStandingOrderId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public MatchedStandingOrderId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public MatchedStandingOrderId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public MatchedStandingOrderId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

package au.com.peterpal.selecting.standingorders.lock.control;

import au.com.peterpal.selecting.standingorders.lock.entity.PendingOrderSubmitted;
import au.com.peterpal.selecting.standingorders.lock.entity.PendingOrderSubmittedId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PendingOrderSubmittedRepository extends JpaRepository<PendingOrderSubmitted, PendingOrderSubmittedId> {
}

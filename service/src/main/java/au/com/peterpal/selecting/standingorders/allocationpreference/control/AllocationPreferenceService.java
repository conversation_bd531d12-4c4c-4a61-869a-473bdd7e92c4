package au.com.peterpal.selecting.standingorders.allocationpreference.control;

import au.com.peterpal.common.rest.validation.ResourceConflictException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AllocationPreferenceResponse;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.AllocationPreferenceSearchRequest;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceStatus;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import lombok.extern.log4j.Log4j2;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@Service
public class AllocationPreferenceService {

  private final AllocationPreferenceRepository allocationPreferenceRepository;

  public AllocationPreferenceService(
      AllocationPreferenceRepository allocationPreferenceRepository) {
    this.allocationPreferenceRepository = allocationPreferenceRepository;
  }

  public void save(AllocationPreference allocationPreference) {
    Affirm.of(allocationPreference).notNull("AllocationPreference must not be null");
    Affirm.of(allocationPreference.getCustomer()).notNull("Customer must not be null");

    log.debug(() -> String.format("Save Allocation Preference  %s", allocationPreference));

    try {
      allocationPreferenceRepository.save(allocationPreference);
    } catch (DataIntegrityViolationException e) {
      throw new ResourceConflictException(
          String.format(
              "Allocation preference with category '%s' and customer '%s' already exist.",
              allocationPreference.getCategories(), allocationPreference.getCustomer().getCode()));
    }
  }

  public AllocationPreference findById(AllocationPreferenceId allocationPreferenceId) {
    Affirm.of(allocationPreferenceId).notNull("AllocationPreferenceId must not be null");
    log.debug(() -> String.format("Find Allocation Preference by Id %s", allocationPreferenceId));

    return allocationPreferenceRepository
        .findById(allocationPreferenceId)
        .orElseThrow(
            () ->
                new ResourceNotFoundException(
                    AllocationPreference.class, String.valueOf(allocationPreferenceId)));
  }

  public Page<AllocationPreference> searchAllByCustomer(String customerCode, Pageable pageRequest) {
    Affirm.of(customerCode).notNull("CustomerCode must not be null");
    return allocationPreferenceRepository.searchAllByCustomer(customerCode, pageRequest);
  }

  public List<AllocationPreference> findPreferenceByCustomerAndCategories(
      Customer customer, List<Category> categories) {
    return allocationPreferenceRepository.findByCustomer_CodeAndStatusAndCategories_CodeIn(
        customer.getCode(),
        AllocationPreferenceStatus.ACTIVE,
        categories.stream().map(Category::getCode).collect(Collectors.toList()));
  }

  public List<AllocationPreferenceResponse> search(AllocationPreferenceSearchRequest searchRequest) {
    return allocationPreferenceRepository.search(searchRequest);
  }

  public List<AllocationPreference> findAllBy(List<AllocationPreferenceId> includedAllocationPreferenceIds) {
    return allocationPreferenceRepository.findAllById(includedAllocationPreferenceIds);
  }

  public List<AllocationPreference> saveAll(List<AllocationPreference> allocationPreferences) {
    return allocationPreferenceRepository.saveAll(allocationPreferences);
  }
}

package au.com.peterpal.selecting.standingorders.titles.entity;

import au.com.peterpal.selecting.standingorders.standingorder.model.Term;
import au.com.peterpal.selecting.standingorders.standingorder.model.TermId;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MatchedTermTuple {
  private TermId termId;
  private String type;
  private String operation;
  private String value;

  public static Set<MatchedTermTuple> from(Term term) {
    return term.getCombinedTerm().getTerms().stream()
        .map(termOrAndTerm -> MatchedTermTuple.builder()
            .termId(termOrAndTerm.getTermId())
            .operation(termOrAndTerm.getOperation().getDescription())
            .type(termOrAndTerm.getType().getDescription())
            .value(termOrAndTerm.getValue())
            .build())
        .collect(Collectors.toSet());
  }
}

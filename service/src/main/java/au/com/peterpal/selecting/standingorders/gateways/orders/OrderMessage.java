package au.com.peterpal.selecting.standingorders.gateways.orders;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.ext.supplier.boundary.dto.SupplierStatus;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.Supplier;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.SupplierId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.BranchDistribution;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import au.com.peterpal.selecting.standingorders.utils.CurrencyCode;
import lombok.*;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;
import java.util.Set;

@Builder
@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
public class OrderMessage {
  public static final String SOURCE_CODE = "STANDING ORDER";
  @NotNull @NonNull private PendingOrderId pendingOrderId;
  @NotNull @NonNull private LocalDate submittedDate;
  @NotNull @NonNull private CustomerId customerId;
  @NotNull @NonNull private String customerCode;
  private BranchId branchId;
  private String branchCode;
  @NotNull @NonNull private SupplierId supplierId;
  @NotNull @NotEmpty private String supplierCode;
  @NotNull @NotEmpty private String orderedProductReference;
  @NotNull @NotEmpty private String titleWithoutPrefix;
  private String seriesTitle;
  private String subtitle;
  private String editionStatement;
  private String authorsInverted;
  private String publisher;
  private String publicationDate;
  private Integer publicationYear;
  private String mediaCode;
  private String countryOfPublication;
  @NotNull @NonNull private Integer quantity;
  private String fundCode;
  private FundId fundId;
  private String categoryCode;
  private String collectionCode;
  private String deliveryInstruction;
  private String standingOrderNumber;
  private String customerReference;
  private String notes;

  @Enumerated(EnumType.STRING)
  private ReleaseFormat format;

  private BigDecimal price;
  private CurrencyCode currencyCode;

  private String submittedBy;
  private Set<BranchDistribution> branchDistributions;

  public static OrderMessage from(PendingOrder pendingOrder) {
    Affirm.of(pendingOrder).notNull("pendingOrder must not be null");
    Affirm.of(pendingOrder.getSupplier())
        .notNull(
            String.format(
                "Supplier in pending order %s must not be null to create OrderMessage",
                pendingOrder.getPendingOrderId()));
    if (SupplierStatus.INACTIVE.equals(pendingOrder.getSupplier().getStatus())) {
      throw new BusinessException(
          String.format(
              "Can not create OrderMessage: Inactive supplier %s",
              pendingOrder.getSupplier().getCode()));
    }

    return OrderMessage.builder()
        .pendingOrderId(pendingOrder.getPendingOrderId())
        .submittedDate(LocalDate.now())
        .customerId(pendingOrder.getCustomer().getCustomerId())
        .customerCode(pendingOrder.getCustomer().getCode())
        .supplierId(pendingOrder.getSupplier().getSupplierId())
        .supplierCode(
            Optional.ofNullable(pendingOrder.getSupplier()).map(Supplier::getCode).orElse(null))
        .orderedProductReference(pendingOrder.getOrderedProductReference())
        .quantity(pendingOrder.getQuantity())
        .price(pendingOrder.getPrice())
        .publicationDate(pendingOrder.getPublicationDateFormatted())
        .publicationYear(pendingOrder.getPublicationYear())
        .currencyCode(pendingOrder.getCurrencyCode())
        .fundId(Optional.ofNullable(pendingOrder.getFund()).map(Fund::getFundId).orElse(null))
        .fundCode(Optional.ofNullable(pendingOrder.getFund()).map(Fund::getCode).orElse(null))
        .categoryCode(
            Optional.ofNullable(pendingOrder.getCategory()).map(Category::getCode).orElse(null))
        .collectionCode(pendingOrder.getCollectionCode())
        .deliveryInstruction(pendingOrder.getDeliveryInstructions())
        .standingOrderNumber(
            pendingOrder
                .getRelease()
                .getAllocation()
                .getCustomerStandingOrder()
                .getStandingOrder()
                .getStandingOrderNumber())
        .customerReference(pendingOrder.getCustomerReference())
        .notes(pendingOrder.getNotes())
        .format(pendingOrder.getFormat())
        .branchDistributions(pendingOrder.getBranchDistributions())
        .submittedBy(pendingOrder.getSubmittedBy())
        .build();
  }
}

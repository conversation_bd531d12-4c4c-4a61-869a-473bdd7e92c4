package au.com.peterpal.selecting.standingorders.titles.entity;

import au.com.peterpal.selecting.standingorders.standingorder.model.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.Set;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder(toBuilder = true)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "matched_standing_order")
public class MatchedStandingOrder {

  @EqualsAndHashCode.Include @NonNull @EmbeddedId
  private MatchedStandingOrderId matchedStandingOrderId;

  @OneToOne
  @JoinColumn(name = "standingOrderId", nullable = false)
  private StandingOrder standingOrder;

  @AttributeOverrides({@AttributeOverride(name = "id", column = @Column(name = "term_id"))})
  private TermId termId;

  @Type(type = "json")
  @Column(columnDefinition = "json")
  private Set<MatchedTermTuple> matchedTermTuples;

  @ManyToOne
  @JoinColumn(name = "matchedProductId", nullable = false)
  private MatchedProduct matchedProduct;

  @Builder.Default
  @Enumerated(EnumType.STRING)
  private MatchedStandingOrderStatus status = MatchedStandingOrderStatus.ACTIVE;

  @JsonIgnore
  public boolean isActive() {
    return status == MatchedStandingOrderStatus.ACTIVE;
  }

  @JsonIgnore
  public boolean isInactive() {
    return status == MatchedStandingOrderStatus.INACTIVE;
  }

  public void makeInactive() {
    setStatus(MatchedStandingOrderStatus.INACTIVE);
  }

  @JsonIgnore
  public StandingOrderId getStandingOrderId() {
    return getStandingOrder().getStandingOrderId();
  }

  public static MatchedStandingOrder from(
      MatchedProduct product, StandingOrder standingOrder, Term term) {
    return MatchedStandingOrder.builder()
        .matchedStandingOrderId(new MatchedStandingOrderId())
        .status(MatchedStandingOrderStatus.ACTIVE)
        .standingOrder(standingOrder)
        .matchedProduct(product)
        .termId(term.getTermId())
        .matchedTermTuples(MatchedTermTuple.from(term))
        .build();
  }

  public static MatchedStandingOrder copy(
      MatchedStandingOrder originalMatchedStandingOrder,
      MatchedProduct matchedProduct) {
    return MatchedStandingOrder.builder()
        .matchedStandingOrderId(new MatchedStandingOrderId())
        .standingOrder(originalMatchedStandingOrder.getStandingOrder())
        .matchedProduct(matchedProduct)
        .termId(originalMatchedStandingOrder.getTermId())
        .matchedTermTuples(originalMatchedStandingOrder.getMatchedTermTuples())
        .build();
  }
}

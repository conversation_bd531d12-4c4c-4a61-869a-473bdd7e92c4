package au.com.peterpal.selecting.standingorders.catalog.model;

/**
 * @docRoot
 * Based on Onix List 39
 *
 */
public enum MediaFileFormatCode {

	GIF ("02", "GIF", ""),
	JPG ("03", "JPEG", ""),
	PDF ("04", "PDF", ""),
	TIF ("05", "TIF", ""),
	RA ("06", "RealAudio 28.8", ""),
	MP3 ("07", "MP3", ""),
	MPG4 ("08", "MPEG-4", "MPEG-4 video file");

	private final String code;
	private final String description;
	private final String notes;

	MediaFileFormatCode(String code, String description, String notes) {
		this.code = code;
		this.description = description;
		this.notes = notes;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public String getNotes() {
		return notes;
	}

	public static MediaFileFormatCode mapOnixCode(String onixCode) {
		for (MediaFileFormatCode value : MediaFileFormatCode.values()) {
			if (value.code.equals(onixCode)) {
				return value;
			}
		}
		throw new IllegalArgumentException("Invalid " + MediaFileFormatCode.class.getSimpleName() + ": " + onixCode);
	}

	@Override
	public String toString() {
		return this.description;
	}
}

package au.com.peterpal.selecting.standingorders.allocationpreference.model;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class ReleasePreferenceId extends UuidEntityId {

  public static ReleasePreferenceId of(@NotEmpty UUID id) {
    return new ReleasePreferenceId(id);
  }

  public static ReleasePreferenceId of(@NotEmpty String id) {
    return new ReleasePreferenceId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public ReleasePreferenceId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public ReleasePreferenceId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public ReleasePreferenceId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}

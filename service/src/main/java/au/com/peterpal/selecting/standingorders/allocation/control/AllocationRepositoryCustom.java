package au.com.peterpal.selecting.standingorders.allocation.control;

import au.com.peterpal.selecting.standingorders.allocation.commands.AllocationSearchResponse;
import au.com.peterpal.selecting.standingorders.allocation.commands.SearchAllocationsByStandingOrder;
import au.com.peterpal.selecting.standingorders.allocation.dto.SearchAllocationRequest;
import au.com.peterpal.selecting.standingorders.allocation.dto.SearchAllocationResponse;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface AllocationRepositoryCustom {
  Page<Allocation> search(SearchAllocationsByStandingOrder searchRequest);

  Page<AllocationSearchResponse> search(SearchAllocationRequest searchRequest, Pageable pageable);

  List<SearchAllocationResponse.CustomerResponse> findCustomersWithAllocation();

  List<Allocation> getAllocations(StandingOrderId soId, long limit);

  List<Allocation> getAllocations(StandingOrderId soId);
  List<Allocation> findAllPausedAllocations(List<StandingOrderId> standingOrderIds);

  List<Allocation> getAllocationsByStandingOrderIds(List<StandingOrderId> standingOrderIds);

  List<Allocation> searchAllocation(
      SearchAllocationRequest searchRequest, List<AllocationId> excludedAllocationIds);
}

package au.com.peterpal.selecting.standingorders.admin.control;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.CSOInfo;
import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.CSOInfo.ReleaseInfo;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import au.com.peterpal.selecting.standingorders.utils.StringAffirm;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class AllocationImportAgentForCustomer {

  public void handle(List<CSOInfo> customerCsoList, Map<String, StandingOrder> allStandingOrders,
      Map<String, CustomerStandingOrder> customerStandingOrderMap, Map<String, Category> categoryCacheMap,
      Map<String, CustomerStandingOrder> csoList, Customer customer, boolean test, String username,
      Map<String, Fund> fundMap, Integer errorCount, ProcessReport report) {
    CustomerStandingOrder cso = null;
    for (CSOInfo csoInfo : customerCsoList) {
      try {
        cso = handle(allStandingOrders, customerStandingOrderMap, categoryCacheMap, csoList,
            customer, csoInfo, test,            "lucy-so", fundMap);
        if (cso.getStandingOrder() == null) {
          log.error(String.format("cso.getStandingOrder() == null: %s", cso));
        }
        csoList.put(cso.getCustomer().getCode() + "|" + cso.getStandingOrder().getStandingOrderNumber(), cso);
      } catch (Exception ex) {
        log.warn(String.format("Exception creating customer standing order from %s", csoInfo),
            ex);
        log.warn(String.format("Exception creating customer standing order from %s", cso));
        errorCount++;
        report.getErrors().add(String.format("%s, %s - %s",
            csoInfo.getCustomerCode(), csoInfo.getStandingOrderNumber(), ex.getMessage()));
      }
    }
  }

  private CustomerStandingOrder handle(Map<String, StandingOrder> allStandingOrders,
      Map<String, CustomerStandingOrder> customerStandingOrderMap,
      Map<String, Category> categoryCacheMap,
      Map<String, CustomerStandingOrder> csoList,
      Customer customer, @NonNull CSOInfo info,
      boolean test, String username, Map<String, Fund> fundMap) {

    String csoMapKey = info.getCustomerCode() + "|" + info.getStandingOrderNumber();
    CustomerStandingOrder cso;
    if (csoList.containsKey(csoMapKey)) {
      cso = csoList.get(csoMapKey);
      cso.addAllocation(getAllocation(info, fundMap, categoryCacheMap));
      return cso;
    } else if (customerStandingOrderMap.containsKey(info.getStandingOrderNumber())) {
      cso = customerStandingOrderMap.get(info.getStandingOrderNumber());
      cso.addAllocation(getAllocation(info, fundMap, categoryCacheMap));
      return cso;
    } else {
      if (allStandingOrders.get(info.getStandingOrderNumber()) == null) {
        log.error(String.format("stadning order does not exit in map: '%s'",
            info.getStandingOrderNumber()));
      }
      cso = handle(info, test, username, customer,
          allStandingOrders.get(info.getStandingOrderNumber()), fundMap, categoryCacheMap);
    }
    return cso;
  }

  private CustomerStandingOrder handle(CSOInfo info, boolean test, String username,
      Customer customer, StandingOrder standingOrder, Map<String, Fund> fundMap,
      Map<String, Category> categoryCacheMap) {
    Affirm.of(info).notNull("Customer standing order info must not be null");
    CustomerStandingOrder cso = getCustomerStandingOrder(customer, standingOrder);

    cso.addAllocation(getAllocation(info, fundMap, categoryCacheMap));

    return cso;
  }

  private CustomerStandingOrder getCustomerStandingOrder(Customer customer,
      StandingOrder standingOrder) {
    return CustomerStandingOrder.builder()
        .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
        .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
        .customer(customer)
        .standingOrder(standingOrder)
        .build();
  }

  private Allocation getAllocation(CSOInfo info, Map<String, Fund> fundMap, Map<String, Category> categoryCacheMap) {
    Fund fund = null;
    if (StringAffirm.of(info.getFundCode()).hasText()) {
      String msg = String.format("Fund code %s for customer %s not found", info.getFundCode(),
          info.getCustomerCode());
      fund = fundMap.get(info.getCustomerCode() + "|" + info.getFundCode());
      if (fund == null) {
        throw new ResourceNotFoundException(msg);
      }
    }

    List<Category> categories = info.getCategories().stream()
        .filter(categoryCacheMap::containsKey)
        .map(categoryCacheMap::get)
        .collect(Collectors.toList());

    Allocation allocation =
        Allocation.builder()
            .allocationId(AllocationId.of(UUID.randomUUID()))
            .categories(categories)
            .fund(fund)
            .customerReference(info.getCustomerReference())
            .deliveryInstructions(info.getDeliveryInstructions())
            .notes(info.getNotes())
            .build();

    return allocation
        .addRelease(getRelease(info.getReleaseInfo(), info.getCustomerCode(), fundMap));
  }

  private Release getRelease(ReleaseInfo info, String customerCode, Map<String, Fund> fundMap) {
    Fund fund = null;
    if (StringAffirm.of(info.getFundCode()).hasText() && customerCode != null) {
      fund = fundMap.get(customerCode + "|" + info.getFundCode());
    }

    Fund nbFund = null;
    if (StringAffirm.of(info.getHardbackFundCode()).hasText() && customerCode != null) {
      fund = fundMap.get(customerCode + "|" + info.getHardbackFundCode());
    }

    Fund pbFund = null;
    if (StringAffirm.of(info.getPaperbackFundCode()).hasText() && customerCode != null) {
      fund = fundMap.get(customerCode + "|" + info.getPaperbackFundCode());
    }

    return Release.builder()
        .releaseId(ReleaseId.of(UUID.randomUUID()))
        .releaseType(info.getReleaseType())
        .actionType(info == null ? null : info.getActionType())
        .initialAssignmentRule(info == null ? null : info.getAssignmentRule())
        .smallFormatPaperbackRule(info == null ? null : info.getSmallFormatPaperbackRule())
        .fund(fund)
        .hardbackfund(nbFund)
        .paperbackfund(pbFund)
        .quantity(info == null ? 0 : info.getQtyTotal())
        .hardbackQuantity(info == null ? 0 : info.getQtyHardback())
        .paperbackQuantity(info == null ? 0 : info.getQtyPaperback())
        .build();
  }
}

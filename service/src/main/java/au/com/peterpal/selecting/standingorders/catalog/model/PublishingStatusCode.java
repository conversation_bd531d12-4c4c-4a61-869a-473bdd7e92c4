package au.com.peterpal.selecting.standingorders.catalog.model;

/**
 *
 * Based on Onix List 64
 */
public enum PublishingStatusCode {

  UNSPEC ("00", "Unspecified", "Status is not specified (as distinct from unknown): the default if the <PublishingStatus> element is not sent. Also to be used in applications where the element is considered mandatory, but the sender of the ONIX message chooses not to pass on status information."),
  CAN ("01", "Cancelled", "The product was announced, and subsequently abandoned; the <PublicationDate> element must not be sent."),
  FORTH ("02", "Forthcoming", "Not yet published, must be accompanied by expected date in <PublicationDate>."),
  POSTPONED ("03", "Postponed indefinitely", "The product was announced, and subsequently postponed with no expected publication date; the<Publication Date> element must not be sent."),
  ACTIVE ("04", "Active", "The product was published, and is still active in the sense that the publisher will accept orders for it, though it may or may not be immediately available, for which see <SupplyDetail>."),
  NLOP ("05", "No longer our product", "Ownership of the product has been transferred to another publisher (with details of acquiring publisher if possible in PR.19)."),
  OS ("06", "Out of stock indefinitely", "The product was active, but is now inactive in the sense that (a) the publisher will not accept orders for it, though stock may still be available elsewhere in the supply chain, and (b) there are no current plans to bring it back into stock. Code 06 does not specifically imply that returns are or are not still accepted."),
  OP ("07", "Out of print", "The product was active, but is now permanently inactive in the sense that (a) the publisher will not accept orders for it, though stock may still be available elsewhere in the supply chain, and (b) the product will not be made available again under the same ISBN. Code 07 normally implies that the publisher will not accept returns beyond a specified date."),
  INACTIVE ("08", "Inactive", "The product was active, but is now permanently or indefinitely inactive in the sense that the publisher will not accept orders for it, though stock may still be available elsewhere in the supply chain. Code 08 covers both of codes 06 and 07, and may be used where the distinction between those values is either unnecessary or meaningless."),
  UNKNOWN ("09", "Unknown", "The sender of the ONIX record does not know the current publishing status."),
  REM ("10", "Remaindered", "The product is no longer available from the current publisher, under the current ISBN, at the current price. It may be available to be traded through another channel. A Publishing Status code 10 'Remaindered' usually but not always means that the publisher has decided to sell off excess inventory of the book. Copies of books that are remaindered are often made available in the supply chain at a reduced price. However, such remainders are often sold under a product identifier that differs from the ISBN on the full-priced copy of the book. A Publishing Status code 10 'Remaindered' on a given product record may or may not be followed by a Publishing Status code 06 'Out of Stock Indefinitely' or 07 'Out of Print': the practise varies from one publisher to another. Some publishers may revert to a Publishing Status code 04 �Active� if a desired inventory level on the product in question has subsequently been reached. No change in rights should ever be inferred from this (or any other) Publishing Status code value."),
  WITHDRAWN ("11", "Withdrawn from sale", "Withdrawn, typically for legal reasons or to avoid giving offence."),
  RECALLED ("12", "Recalled", "Recalled for reasons of consumer safety. Deprecated, use code 15 instead."),
  ACTIVE_BUT_NO_SOLD_SEPARATELY ("13", "Active, but not sold separately", "The product is published and active but, as a publishing decision, its constituent parts are not sold separately – only in an assembly or as part of a pack, eg with Product composition code 01. Also use with Product composition codes 30, 31 where depending on product composition and pricing, items in the pack may or may not be saleable separately at retail."),
  RECALLED_SAFETY ("15", "Recalled", "Recalled for reasons of consumer safety."),
  WITHDRAWN_TEMP ("16", "Temporarily withdrawn from sale", "Withdrawn temporarily, typically for quality or technical reasons. In ONIX 3.0, must be accompanied by expected availability date coded ‘22’ within the <PublishingDate> composite, except in exceptional circumstances where no date is known."),
  WITHDRAWN_PERM ("17", "Permanently withdrawn from sale", "Withdrawn permanently from the market. Effectively synonymous with ‘Out of print’ (code 07), but specific to downloadable and online digital products (where no ‘stock’ would remain in the supply chain)."),
  ACTIVE_BUT_NO_SOLD_AS_SET ("18", "Active, but not sold as set", "The various constituent parts of a product are published and active but, as a publishing decision, they are not sold together as a single product – eg with Product composition code 11 – and are only available as a number of individual items. Only for use in ONIX 3.0 or later.")
  ;
  private final String code;
  private final String description;
  private final String notes;

  PublishingStatusCode(String code, String description, String notes) {
    this.code = code;
    this.description = description;
    this.notes = notes;
  }

  public String getCode() {
    return code;
  }

  public String getDescription() {
    return description;
  }

  public String getNotes() {
    return notes;
  }

  public static PublishingStatusCode mapOnixCode(String onixCode) {
    for (PublishingStatusCode value : PublishingStatusCode.values()) {
      if (value.code.equals(onixCode)) {
        return value;
      }
    }
    throw new IllegalArgumentException("Invalid " + PublishingStatusCode.class.getSimpleName() + ": " + onixCode);
  }

  @Override
  public String toString() {
    return this.description;
  }
}

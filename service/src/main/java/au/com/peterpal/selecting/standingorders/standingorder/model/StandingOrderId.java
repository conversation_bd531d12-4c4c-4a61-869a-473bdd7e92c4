package au.com.peterpal.selecting.standingorders.standingorder.model;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class StandingOrderId extends UuidEntityId {

  public static StandingOrderId of(@NotEmpty UUID id) {
    return new StandingOrderId(id);
  }

  public static StandingOrderId of(@NotEmpty String id) {
    return new StandingOrderId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public StandingOrderId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public StandingOrderId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public StandingOrderId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}
package au.com.peterpal.selecting.standingorders.allocation.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;

import java.time.LocalDate;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;
import org.apache.commons.lang3.StringUtils;

@Value
@Builder
@AllArgsConstructor
public class SearchAllocationRequest {

  AllocationStatus status;
  String customerCode;
  List<String> categories;
  String fund;
  String customerReference;
  String description;
  String term;
  AllocationDateType dateType;
  LocalDate startDate;
  LocalDate endDate;
  String keyword;
  KeywordEntity keywordEntity;
  String sortByKey;
  String sortByDirection;

  public String getTerm() {
    return StringUtils.trimToEmpty(term).replaceAll("[ ]+", " ");
  }

  @JsonIgnore
  public String getTermWithAddedOrRemovedSpaceAfterComma() {
    String termOpposite = StringUtils.contains(getTerm(), ", ") ?
        StringUtils.replace(getTerm(), ", ", ",") :
        StringUtils.replace(getTerm(), ",", ", ");
    return termOpposite.toLowerCase();
  }

  public String getCustomerCode() {
    return StringUtils.trim(customerCode);
  }

  public List<String> getCategories() {
    return categories;
  }

  public String getFund() {
    return StringUtils.trim(fund);
  }

  public String getCustomerReference() {
    return StringUtils.trim(customerReference);
  }

  public String getDescription() {
    return StringUtils.trim(description);
  }
}

package au.com.peterpal.selecting.standingorders.standingorder.model;

import au.com.peterpal.selecting.standingorders.utils.Affirm;
import lombok.Getter;

@Getter
public enum OperationType {
  EXACT_MATCH("EXACT_MATCH", "Exact Match"),
  STARTS_WITH("STARTS_WITH", "Starts With"),
  ENDS_WITH("ENDS_WITH", "Ends With"),
  CONTAINS("CONTAINS", "Contains");

  private final String description;
  private final String code;

  OperationType(String code, String description) {
    this.code = code;
    this.description = description;
  }

  public static OperationType of(StringBuilder builder) {
    Affirm.of(builder).notNull("Can not create OperationType from null builder");
    OperationType ot = OperationType.EXACT_MATCH;
    String str = builder.toString();
    str = str.trim();
    if (str.startsWith("%") && str.endsWith("%")) {
      ot = OperationType.CONTAINS;
    } else if (str.endsWith("%")) {
      ot = OperationType.STARTS_WITH;
    } else if (str.startsWith("%")) {
      ot = OperationType.ENDS_WITH;
    }
    return ot;
  }

  public boolean compareIgnoreCase(String termValue, String actualValue) {
    return compare(termValue, actualValue, true);
  }

  public boolean compare(
      String termValue, String actualValue, boolean ignoreCase) {
    String tValue = termValue;
    String aValue = actualValue;

    if (ignoreCase) {
      tValue = termValue.toLowerCase();
      aValue = actualValue.toLowerCase();
    }

    switch (this) {
      case EXACT_MATCH:
        return ignoreCase ? actualValue.equalsIgnoreCase(termValue) : actualValue.equals(termValue);
      case STARTS_WITH:
        return aValue.startsWith(tValue);
      case ENDS_WITH:
        return aValue.endsWith(tValue);
      case CONTAINS:
        return aValue.matches(".*\\b" + tValue + "\\b.*");
    }
    return false;
  }
}

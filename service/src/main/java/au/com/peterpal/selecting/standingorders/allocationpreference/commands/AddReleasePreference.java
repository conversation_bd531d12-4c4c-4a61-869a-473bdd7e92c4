package au.com.peterpal.selecting.standingorders.allocationpreference.commands;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.dto.ReleasePreferenceInfo;
import lombok.*;

import javax.validation.constraints.NotNull;

@With
@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class AddReleasePreference {

  @NonNull @NotNull private AllocationPreferenceId allocationPreferenceId;
  @NonNull @NotNull private ReleasePreferenceInfo preference;
}

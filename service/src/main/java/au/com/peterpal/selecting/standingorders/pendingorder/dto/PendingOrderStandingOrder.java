package au.com.peterpal.selecting.standingorders.pendingorder.dto;

import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PendingOrderStandingOrder {
  private PendingOrder pendingOrder;
  private StandingOrderId standingOrderId;
  private String standingOrderNumber;
  private String standingDescription;
}

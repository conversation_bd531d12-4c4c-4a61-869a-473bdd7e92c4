package au.com.peterpal.selecting.standingorders.titles.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Setter
@Getter
@SuperBuilder
public class TitleAccepted extends DomainEvent {

  public static TitleAccepted from(UUID titleId, String username) {
    return TitleAccepted.builder().id(titleId).username(username).build();
  }
}

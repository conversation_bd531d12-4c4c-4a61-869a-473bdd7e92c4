package au.com.peterpal.selecting.standingorders.customerstandingorder.control;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationService;
import au.com.peterpal.selecting.standingorders.allocation.dto.AllocationInfo;
import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.AllocationPreferenceRepository;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.ReleasePreferenceRepository;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreferenceId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.ext.category.control.CategoryRepository;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.*;
import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.CSOInfo.ReleaseInfo;
import au.com.peterpal.selecting.standingorders.customerstandingorder.events.AllocationCreated;
import au.com.peterpal.selecting.standingorders.customerstandingorder.events.AllocationUpdated;
import au.com.peterpal.selecting.standingorders.customerstandingorder.events.CategoryAssigned;
import au.com.peterpal.selecting.standingorders.customerstandingorder.events.CustomerStandingOrderCreated;
import au.com.peterpal.selecting.standingorders.customerstandingorder.events.CustomerStandingOrderUpdated;
import au.com.peterpal.selecting.standingorders.customerstandingorder.events.FormatAssigned;
import au.com.peterpal.selecting.standingorders.customerstandingorder.events.NoteAdded;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.Format;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.Branch;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerService;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.boundary.dto.FundStatus;
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundService;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderBL;
import au.com.peterpal.selecting.standingorders.standingorder.control.StandingOrderRepository;
import au.com.peterpal.selecting.standingorders.standingorder.events.StandingOrderUpdated;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import au.com.peterpal.selecting.standingorders.utils.StringAffirm;
import com.google.common.collect.Lists;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Log4j2
@Service
@RequiredArgsConstructor
@Transactional
public class CustomerStandingOrderBL {

  private final CustomerStandingOrderRepository customerStandOrderRepo;
  private final CustomerService customerService;
  private final StandingOrderRepository standingOrderRepository;
  private final StandingOrderBL standingOrderService;
  private final AllocationService allocationService;
  private final FundService fundService;
  private final AllocationPreferenceRepository allocationPreferenceRepository;
  private final ReleaseService releaseService;
  private final ReleasePreferenceRepository relPrefRepo;
  private final EventPublisher publisher;
  private final CategoryRepository categoryRepository;
  private final MessageChannel rematchAllocationOutboundChannel;

  @Value("${spring.jpa.properties.hibernate.jdbc.batch_size}")
  private int batchSize;

  // TODO MOVE PROCESSING ALLOCATIONS TO AllocationBL to make this class a bit more tidy

  public CustomerStandingOrder handle(List<CustomerStandingOrder> csoList, @NonNull CSOInfo info,
      boolean test, String username) {
    Optional<CustomerStandingOrder> existingCSOOptional = csoList.stream().filter(
        cso -> cso.getStandingOrder().getStandingOrderNumber().equals(info.getStandingOrderNumber())
            && cso.getCustomer().getCode().equals(info.getCustomerCode())).findAny();

    CustomerStandingOrder cso;
    if (existingCSOOptional.isPresent()) {
      cso = existingCSOOptional.get();
      cso.addAllocation(getAllocation(info));
    } else {
      cso = handle(info, test, username);
    }

    return cso;
  }

  CustomerStandingOrder handle(CSOInfo info, boolean test, String username) {
    Affirm.of(info).notNull("Customer standing order info must not be null");
    log.info(() -> String.format("Handling customer standing order info %s", info));

    Customer customer = customerService.findByCustomerCode(info.getCustomerCode());
    StandingOrder standingOrder = standingOrderService.findByNumber(info.getStandingOrderNumber());
    CustomerStandingOrder cso = getCustomerStandingOrder(customer, standingOrder);
    cso.addAllocation(getAllocation(info));

    return cso;
  }

  public void save(List<CustomerStandingOrder> list, String username) {
    if (list == null || list.isEmpty()) {
      return;
    }
    for (int i = 0; i < list.size(); i = i + batchSize) {
      int toIndex = i + batchSize > list.size() ? list.size() : i + batchSize;
      List<CustomerStandingOrder> slice = list.subList(i, toIndex);
      log.debug(
          String.format("Saving batch of %d customer standing orders; total size %d", batchSize,
              list.size()));
      customerStandOrderRepo.saveAll(slice);
    }
    log.info("Customer standing orders saved");
  }

  public void saveSubList(List<CustomerStandingOrder> list, String username) {
    log.debug(String.format("Saving batch of %d customer standing orders; total size %d", batchSize,
        list.size()));
    customerStandOrderRepo.saveAll(list);
  }

  public int getBatchSize() {
    return batchSize;
  }

  public List<AllocationInfo> getBranchAllocations(@NonNull CustomerId customerId,
      @NonNull StandingOrderId standingOrderId, @NonNull String categoryCode, String username) {
    String msg = "getBranchDistributions for customerId, soId, category, user: %s, %s, %s, %s";
    log.debug(String.format(msg, customerId, standingOrderId, categoryCode, username));

    Customer customer = customerService.findById(customerId);
    StandingOrder so = standingOrderService.findById(standingOrderId);
    Category category = categoryRepository.findByCode(categoryCode);
    CustomerStandingOrder cso = customerStandOrderRepo
        .findByCustomerAndStandingOrder(customer, so)
        .orElseThrow();
    return AllocationInfo.from(cso.getAllocations(Lists.newArrayList(category), true));
  }

  public AllocationBranchInfo getAllocationAndBranches(CustomerId customerId,
      StandingOrderId standingOrderId, String categoryCode, AllocationId allocId, String username) {
    List<AllocationInfo> branches = getBranchAllocations(customerId, standingOrderId, categoryCode,
        username);
    Allocation alloc;
    try {
      alloc = allocationService.findById(allocId);
    } catch (Exception ex) {
      log.warn(() -> String.format("Could not find allocations for id %s", allocId));
      throw new BusinessException("Could not find allocation");
    }

    return AllocationBranchInfo.of(AllocationInfo.from(alloc),
        branches.stream()
            .filter(
                b -> b.getBaParentId() != null && b.getBaParentId().getId().equals(allocId.getId()))
            .sorted(Comparator.comparing(a -> a.getBranch().getCode()))
            .collect(Collectors.toList()));
  }

  public AllocationBranchInfo getAllocationWithBranches(
      StandingOrderId standingOrderId, AllocationId parentAllocationId) {
    standingOrderService.findById(standingOrderId);
    Allocation parentAllocation = allocationService.findById(parentAllocationId);
    List<Allocation> branchAllocations =
        allocationService.findAllByBaParentAllocationId(parentAllocationId);

    return AllocationBranchInfo.of(
        AllocationInfo.from(parentAllocation), AllocationInfo.from(branchAllocations));
  }

  public Map<String, CustomerStandingOrder> findAllStandingOrderForACustomer(Customer customer) {
    return customerStandOrderRepo.findByCustomerAndCustomerStandingOrderStatus(customer,
            StandingOrderStatus.ACTIVE).stream()
        .collect(Collectors.toMap(e -> e.getStandingOrder().getStandingOrderNumber(), e -> e));
  }

  public Map<String, Fund> findAllFunds() {
    return fundService.findAll().stream().filter(e -> e.getStatus() == FundStatus.ACTIVE)
        .collect(Collectors.toMap(e -> e.getCustomer().getCode() + "|" + e.getCode(), e -> e));
  }

  private CustomerStandingOrder getCustomerStandingOrder(Customer customer,
      StandingOrder standingOrder) {
    return CustomerStandingOrder.builder()
        .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
        .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
        .customer(customer)
        .standingOrder(standingOrder)
        .build();
  }

  private CustomerStandingOrder getCustomerStandingOrder(
      Customer customer, StandingOrder standingOrder, boolean throwException) {
    Affirm.of(customer).notNull("Customer must not be null");
    Affirm.of(standingOrder).notNull("Standing order must not be null");
    if (throwException) {
      return customerStandOrderRepo
          .findByCustomerAndStandingOrder(customer, standingOrder)
          .orElseThrow(() -> new ResourceNotFoundException(CustomerStandingOrder.class,
              String.format("customerId %s, standingOrderId %", customer.getCustomerId(),
                  standingOrder.getStandingOrderId())));
    } else {
      return getCustomerStandingOrder(customer, standingOrder);
    }
  }

  private CustomerStandingOrder getCustomerStandingOrder(CustomerId customerId,
      StandingOrderId standingOrderId) {
    Affirm.of(customerId).notNull("Customer id must not be null");
    Affirm.of(standingOrderId).notNull("Standing order id must not be null");

    Customer customer = customerService.findById(customerId);
    StandingOrder standingOrder = standingOrderService.findById(standingOrderId);
    return getCustomerStandingOrder(customer, standingOrder, true);
  }

  private Allocation getAllocation(CSOInfo info) {
    Fund fund = null;
    if (StringAffirm.of(info.getFundCode()).hasText()) {
      String msg = String.format("Fund code %s for customer %s not found", info.getFundCode(),
          info.getCustomerCode());
      fund =
          fundService
              .findByCodeAndCustomer(info.getFundCode(), info.getCustomerCode())
              .orElseThrow(() -> new ResourceNotFoundException(msg));
    }

    List<Category> categories = categoryRepository.findByCodeIn(info.getCategories());

    Allocation allocation =
        Allocation.builder()
            .allocationId(AllocationId.of(UUID.randomUUID()))
            .categories(categories)
            .fund(fund)
            .customerReference(info.getCustomerReference())
            .deliveryInstructions(info.getDeliveryInstructions())
            .notes(info.getNotes())
            .build();

    return allocation
        .addRelease(getRelease(info.getReleaseInfo(), info.getCustomerCode()));
  }

  private Release getRelease(ReleaseInfo info, String customerCode) {
    Fund fund = null;
    if (StringAffirm.of(info.getFundCode()).hasText() && customerCode != null) {
      fund = fundService.findByCodeAndCustomer(info.getFundCode(), customerCode)
          .orElse(null);
    }

    Fund nbFund = null;
    if (StringAffirm.of(info.getHardbackFundCode()).hasText() && customerCode != null) {
      fund = fundService.findByCodeAndCustomer(info.getHardbackFundCode(), customerCode)
          .orElse(null);
    }

    Fund pbFund = null;
    if (StringAffirm.of(info.getPaperbackFundCode()).hasText() && customerCode != null) {
      fund = fundService.findByCodeAndCustomer(info.getPaperbackFundCode(), customerCode)
          .orElse(null);
    }

    return Release.builder()
        .releaseId(ReleaseId.of(UUID.randomUUID()))
        .releaseType(info.getReleaseType())
        .actionType(info == null ? null : info.getActionType())
        .initialAssignmentRule(info == null ? null : info.getAssignmentRule())
        .smallFormatPaperbackRule(info == null ? null : info.getSmallFormatPaperbackRule())
        .fund(fund)
        .hardbackfund(nbFund)
        .paperbackfund(pbFund)
        .quantity(info == null ? 0 : info.getQtyTotal())
        .hardbackQuantity(info == null ? 0 : info.getQtyHardback())
        .paperbackQuantity(info == null ? 0 : info.getQtyPaperback())
        .build();
  }

  public CustomerStandingOrder handle(UUID customerStandingOrderId) {
    return customerStandOrderRepo
        .findById(CustomerStandingOrderId.of(customerStandingOrderId))
        .orElseThrow(
            () ->
                new ResourceNotFoundException(
                    CustomerStandingOrder.class, String.valueOf(customerStandingOrderId)));
  }

  public CreateAllocationResponse createAllocation(AllocationRequest allocationRequest, String username) {
    Affirm.of(allocationRequest).notNull("allocationRequest must not be null");

    Customer customer = customerService.findById(allocationRequest.getCustomerId());
    StandingOrder standingOrder = standingOrderService.findById(
        allocationRequest.getStandingOrderId());
    Fund fund = null;
    if (allocationRequest.getFundId() != null) {
      fund = fundService.getFund(allocationRequest.getFundId().getId());
    }

    List<Category> categories = categoryRepository.findByCodeIn(allocationRequest.getCategories());

    CustomerStandingOrder cso = customerStandOrderRepo.findByCustomerAndStandingOrder(customer,
            standingOrder)
        .orElseGet(() -> CustomerStandingOrder.builder()
            .customerStandingOrderId(CustomerStandingOrderId.of(UUID.randomUUID()))
            .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
            .standingOrder(standingOrder)
            .customer(customer)
            .build()
        );

    Allocation alloc = Allocation.builder()
        .allocationId(AllocationId.of(UUID.randomUUID()))
        .categories(categories)
        .status(allocationRequest.getStatus())
        .allocationPreference(Optional.ofNullable(allocationRequest.getAllocationPreferenceId())
            .map(id -> allocationPreferenceRepository.findById(id).orElse(null))
            .orElse(null)
        )
        .fund(fund)
        .customerReference(allocationRequest.getCustomerReference())
        .deliveryInstructions(allocationRequest.getDeliveryInstructions())
        .notes(allocationRequest.getNotes())
        .collectionCode(allocationRequest.getCollectionCode())
        .dateAdded(LocalDateTime.now())
        .dateUpdated(LocalDateTime.now())
        .build();

    alloc.addRelease(Release.builder()
        .releaseId(ReleaseId.of(UUID.randomUUID()))
        .releaseType(ReleaseType.INITIAL)
        .build());

    cso.addAllocation(alloc);
    updateCustomerStandingOrder(cso, username);
    publisher.publishEvent(AllocationCreated.from(alloc, username));
    log.debug(cso);

    return CreateAllocationResponse.of(cso.getCustomerStandingOrderId(), alloc.getAllocationId());
  }

  @Transactional
  public AllocationUpdated handle(UpdateAllocationInfo updateInfo, AllocationId allocationId,
      String username) {
    Affirm.of(updateInfo).notNull("UpdateAllocationInfo must not be null");
    Affirm.of(allocationId).notNull("AllocationId must not be null");

    Allocation allocation = allocationService.findById(allocationId);

    boolean isActiveToPaused = isActiveToPaused(updateInfo.getStatus(), allocation.getStatus());
    boolean isPausedToActive = isPausedToActive(updateInfo.getStatus(), allocation.getStatus());
    boolean isPausedToInactive = isPausedToInactive(updateInfo.getStatus(), allocation.getStatus());

    AllocationPreference allocationPreference =
        this.getAllocationPreferenceById(updateInfo.getAllocationPreferenceId());

    Fund fund = Optional.ofNullable(updateInfo.getFundId())
        .map(fundService::getFund)
        .orElse(null);

    Fund hbFund = Optional.ofNullable(updateInfo.getHbFundId())
        .map(fundService::getFund)
        .orElse(null);

    Fund pbFund = Optional.ofNullable(updateInfo.getPbFundId())
        .map(fundService::getFund)
        .orElse(null);

    List<Category> categories = CollectionUtils.isNotEmpty(updateInfo.getCategories()) ?
        categoryRepository.findByCodeIn(updateInfo.getCategories())
        : new ArrayList<>();

    CustomerStandingOrder customerStandingOrder =
        Optional.ofNullable(updateInfo.getCustomerStandingOrderId())
            .map(uuid -> getCustomerStandingOrderById(CustomerStandingOrderId.of(uuid)))
            .orElseThrow(() -> new ResourceNotFoundException(CustomerStandingOrder.class,
                updateInfo.getCustomerStandingOrderId() == null ? "null" :
                    updateInfo.getCustomerStandingOrderId().toString()));

    List<Allocation> branches = customerStandingOrder.getAllocations(allocation.getCategories(),
        true);
    boolean hasBranches = !branches.isEmpty();

    allocation = allocation.toBuilder()
        .customerStandingOrder(customerStandingOrder)
        .allocationPreference(allocationPreference)
        .fund(fund)
        .categories(categories)
        .customerReference(updateInfo.getCustomerReference())
        .deliveryInstructions(updateInfo.getDeliveryInstructions())
        .notes(updateInfo.getNotes())
        .status(updateInfo.getStatus())
        .collectionCode(updateInfo.getCollectionCode())
        .dateUpdated(LocalDateTime.now())
        .build();

    Release initialRelease = allocation.findRelease(ReleaseType.INITIAL).orElse(null);
    boolean quantityUpdated = false;
    if (Objects.nonNull(initialRelease)) {
      if (!hasBranches) {
        initialRelease.setHardbackQuantity(updateInfo.getReleaseHBQuantity(initialRelease.getHardbackQuantity()));
        initialRelease.setPaperbackQuantity(updateInfo.getReleasePBQuantity(initialRelease.getPaperbackQuantity()));
        initialRelease.setQuantity(updateInfo.getReleaseQuantity(initialRelease.getQuantity()));
        quantityUpdated = true;
      }
      initialRelease.setHardbackfund(Optional.ofNullable(hbFund).orElse(initialRelease.getHardbackfund()));
      initialRelease.setPaperbackfund(Optional.ofNullable(pbFund).orElse(initialRelease.getPaperbackfund()));
      initialRelease.setActionType(
              Optional.ofNullable(updateInfo.getActionType()).orElse(initialRelease.getActionType()));
      initialRelease.setInitialAssignmentRule(
              Optional.ofNullable(updateInfo.getAssignmentRule())
                      .orElse(initialRelease.getInitialAssignmentRule()));
      initialRelease.setSmallFormatPaperbackRule(
              Optional.ofNullable(updateInfo.getSmallFormatPaperbackRule())
                      .orElse(initialRelease.getSmallFormatPaperbackRule()));

    }
    allocation = allocationService.save(allocation);

    boolean performRematch =
        updateInfo.getRematchAllocationAfterUpdate()
            && quantityUpdated
            && updateInfo.hasQtyUpdate();
    AllocationUpdated allocationUpdated =
        AllocationUpdated.from(
            allocation, isActiveToPaused, isPausedToActive, isPausedToInactive, performRematch, username);

    List<AllocationUpdated> branchAllocationUpdatedEvents = Lists.newArrayList();
    if (hasBranches) {
      branches.forEach(
          branchAllocation ->
              branchAllocation
                  .getReleases()
                  .forEach(
                      branchRelease -> {
                        branchRelease.setInitialAssignmentRule(
                            Optional.ofNullable(updateInfo.getAssignmentRule())
                                .orElse(branchRelease.getInitialAssignmentRule()));
                        branchRelease.setActionType(
                            Optional.ofNullable(updateInfo.getActionType())
                                .orElse(branchRelease.getActionType()));
                      }));

      allocationService.saveAll(branches);
      branchAllocationUpdatedEvents =
          branches.stream()
              .map(b -> AllocationUpdated.from(b, false, false, false, false, username))
              .collect(Collectors.toList());
    }

    publisher.publishEvent(allocationUpdated);
    branchAllocationUpdatedEvents.forEach(publisher::publishEvent);

    if (performRematch) {
      performRematch(allocation);
    }

    updateCustomerStandingOrderStatus(customerStandingOrder.getCustomerStandingOrderId(), username);

    return allocationUpdated;
  }

  public void updateCustomerStandingOrderStatus(List<CustomerStandingOrder> customerStandingOrders, String username) {
    for (CustomerStandingOrder customerStandingOrder : customerStandingOrders) {
      updateCustomerStandingOrderStatus(customerStandingOrder.getCustomerStandingOrderId(), username);
    }
  }
  public void updateCustomerStandingOrderStatus(
      CustomerStandingOrderId customerStandingOrderId, String username) {
    customerStandOrderRepo
        .findById(customerStandingOrderId)
        .ifPresent(
            customerStandingOrder -> {
              if (customerStandingOrder.isAllAllocationInActive()) {
                customerStandingOrder.setCustomerStandingOrderStatus(StandingOrderStatus.INACTIVE);
              } else {
                customerStandingOrder.setCustomerStandingOrderStatus(StandingOrderStatus.ACTIVE);
              }
              updateCustomerStandingOrder(customerStandingOrder, username);
            });
  }

  CustomerStandingOrder updateCustomerStandingOrder(CustomerStandingOrder customerStandingOrder, String username) {
    customerStandingOrder = customerStandOrderRepo.save(customerStandingOrder);
    updateStandingOrderStatus(customerStandingOrder.getStandingOrder(), username);
    return customerStandingOrder;
  }

  void updateStandingOrderStatus(StandingOrder standingOrder, String username) {
    boolean allInactive =
        customerStandOrderRepo
            .findAllByStandingOrderStandingOrderId(standingOrder.getStandingOrderId())
            .stream()
            .allMatch(cso -> cso.getCustomerStandingOrderStatus() == StandingOrderStatus.INACTIVE);
    if (allInactive) {
      standingOrder.setStandingOrderStatus(StandingOrderStatus.INACTIVE);
    } else {
      standingOrder.setStandingOrderStatus(StandingOrderStatus.ACTIVE);
    }
    standingOrderService.updateStandingOrder(standingOrder, username);
  }

  private boolean isActiveToPaused(
      AllocationStatus updateTo, AllocationStatus currentStatus) {
    return currentStatus == AllocationStatus.ACTIVE && updateTo == AllocationStatus.PAUSED;
  }

  private boolean isPausedToActive(
      AllocationStatus updateTo, AllocationStatus currentStatus) {
    return currentStatus == AllocationStatus.PAUSED && updateTo == AllocationStatus.ACTIVE;
  }

  private boolean isPausedToInactive(
      AllocationStatus updateTo, AllocationStatus currentStatus) {
    return currentStatus == AllocationStatus.PAUSED && updateTo == AllocationStatus.INACTIVE;
  }

  private Integer getQuantity(AssignmentRule assignmentRule, Integer quantity,
      Integer defaultValue) {
    if (Objects.isNull(assignmentRule)) {
      return defaultValue;
    }
    if (Objects.requireNonNull(assignmentRule) == AssignmentRule.SPLIT) {
      return Optional.ofNullable(quantity).orElse(defaultValue);
    }
    return 0;
  }

  public NoteAdded handleNoteAdded(AllocationId allocationId, String notes, String username) {
    Affirm.of(allocationId).notNull("AllocationId must not be null");
    Allocation allocation = allocationService.findById(allocationId);
    allocation.setNotes(notes);
    NoteAdded noteAdded = NoteAdded.from(allocation, username);
    publisher.publishEvent(noteAdded);
    return noteAdded;
  }

  @EventListener
  public void on(final NoteAdded noteAdded) {

    Allocation allocation = noteAdded.getAllocation();
    allocation.setDateUpdated(LocalDateTime.now());
    allocationService.save(allocation);
  }

  @EventListener
  public void on(final AllocationCreated allocationCreated) {
    Optional.ofNullable(allocationCreated).ifPresent(e -> updateSOStatus(e.getStandingOrderId()));
  }

  @EventListener
  public void on(final AllocationUpdated allocationUpdated) {
    Optional.ofNullable(allocationUpdated).ifPresent(e -> updateSOStatus(e.getStandingOrderId()));
  }

  private void updateSOStatus(StandingOrderId soId) {
    Optional.ofNullable(soId).ifPresent(id -> {
      standingOrderRepository.findById(id)
          .ifPresent(so -> {
            StandingOrderStatus newStatus = allocationService.getNewSOStatus(
                so.getStandingOrderId());
            if (!newStatus.equals(so.getStandingOrderStatus())) {
              so.setStandingOrderStatus(newStatus);
              so = standingOrderRepository.save(so);
              publisher.publishEvent(StandingOrderUpdated.from(so, "system"));
            }
          });
    });
  }

  public UUID handle(CreateCustomerStandingOrder preference, String username) {
    Affirm.of(preference).notNull("CreateStandingOrder must not be null");
    Affirm.of(preference.getCustomerId()).notNull("CustomerId must not be null");
    Affirm.of(preference.getStandingOrderId()).notNull("StandingOrderId must not be null");

    Customer customer = customerService.findById(CustomerId.of(preference.getCustomerId()));

    StandingOrder standingOrder = this.getStandingOrderById(preference.getStandingOrderId());

    List<AllocationPreference> allocationPreferenceList =
        allocationPreferenceRepository.findAllByCustomer_CustomerId(
            CustomerId.of(preference.getCustomerId()));

    List<CreateOrUpdateAllocation> createOrUpdateAllocations =
        this.createAllocations(
            allocationPreferenceList,
            CustomerStandingOrderId.of(UUID.randomUUID())); // random because cso is yet to create

    CustomerStandingOrderCreated event =
        CustomerStandingOrderCreated.from(
            customer, standingOrder, createOrUpdateAllocations, username);

    updateCustomerStandingOrder(CustomerStandingOrder.from(event), username);
    publisher.publishEvent(event);

    return event.getId();
  }

  public CustomerStandingOrderUpdated handle(
      CustomerStandingOrderRequest request,
      CustomerStandingOrderId customerStandingOrderId,
      String username) {
    Affirm.of(request).notNull("CustomerStandingOrderRequest must not be null");
    Affirm.of(customerStandingOrderId).notNull("CustomerStandingOrderId must not be null");

    StandingOrder standingOrder = this.getStandingOrderById(request.getStandingOrderId());
    Customer customer = customerService.findById(CustomerId.of(request.getCustomerId()));

    CustomerStandingOrder customerStandingOrder =
        this.getCustomerStandingOrderById(customerStandingOrderId);

    CustomerStandingOrderUpdated customerStandingOrderUpdated =
        CustomerStandingOrderUpdated.from(
            customer.getCustomerId(),
            customerStandingOrder,
            standingOrder.getStandingOrderId(),
            username);

    updateCustomerStandingOrder(CustomerStandingOrder.from(customerStandingOrder, customer, standingOrder), username);

    publisher.publishEvent(customerStandingOrderUpdated);

    return customerStandingOrderUpdated;
  }

  public FormatAssigned handle(AllocationId allocationId, Format format, String username) {
    Affirm.of(allocationId).notNull("AllocationId must not be null");
    Affirm.of(format).notNull("Format must not be null");

    Allocation allocation = allocationService.findById(allocationId);
    FormatAssigned formatAssigned = FormatAssigned.from(allocation, username);
    publisher.publishEvent(formatAssigned);
    return formatAssigned;
  }

  public CategoryAssigned handle(@NonNull AllocationId allocationId,
      @NotEmpty List<String> categoryCodes, String username) {
    Allocation allocation = allocationService.findById(allocationId);
    List<Category> categories = categoryRepository.findByCodeIn(categoryCodes);
    allocation.setCategories(categories);
    CategoryAssigned categoryAssigned = CategoryAssigned.from(allocation, categories, username);
    publisher.publishEvent(categoryAssigned);
    return categoryAssigned;
  }

  @EventListener
  public void on(final CategoryAssigned categoryAssigned) {
    Allocation allocation = categoryAssigned.getAllocation();
    allocation.setDateUpdated(LocalDateTime.now());
    this.allocationService.save(allocation);
  }

  @EventListener
  public void on(final FormatAssigned formatAssigned) {
    log.debug(() -> String.format("Assigning allocation format %s", formatAssigned));
    Allocation allocation = formatAssigned.getAllocation();
    allocation.setDateUpdated(LocalDateTime.now());
    this.allocationService.save(allocation);
  }

  public void updateReleases(AllocationId allocationId,
      @NotEmpty(message = "List of releases cannot be empty.")
      List<@Valid ReleaseRequest> releases, String username) {
    Affirm.of(allocationId).notNull("AllocationId must not be null");

    Allocation allocation = allocationService.findById(allocationId);
    if (releases.stream().anyMatch(r -> !r.isValidQuantity())) {
      throw new BusinessException("Total quantity for Allocation must be greater than zero.");
    }

    CustomerStandingOrder cso = allocation.getCustomerStandingOrder();
    List<Allocation> bd =
        cso.getAllocations(allocation.getCategories(), true).stream()
            .filter(
                ba ->
                    ba.getBaParent() != null
                        && ba.getBaParent()
                            .getAllocationId()
                            .getId()
                            .equals(allocation.getAllocationId().getId())
                        && ba.getStatus() == AllocationStatus.ACTIVE)
            .collect(Collectors.toList());

    int bdQty = bd.stream().map(Allocation::getInitialQuantity).reduce(0, Integer::sum);
    int bdHbqty = bd.stream().map(Allocation::getInitialHBQuantity).reduce(0, Integer::sum);
    int bdPbqty = bd.stream().map(Allocation::getInitialPBQuantity).reduce(0, Integer::sum);
    boolean updateHbPbQty = isUpdateHbPbQty(bd, bdQty, releases);

    allocation.getReleases().stream().forEach(r -> {
      ReleaseId rid = r.getReleaseId();
      Optional.ofNullable(ReleaseRequest.get(releases, rid))
          .ifPresent(rr -> {
            r.setPreference(get(rr.getPreferenceId()));
            r.setReleaseType(rr.getReleaseType());
            r.setActionType(rr.getActionType());
            r.setInitialAssignmentRule(rr.getAssignmentRule());
            r.setSmallFormatPaperbackRule(rr.getPbRule());
            r.setQuantity(updateHbPbQty ? AssignmentRule.SPLIT.equals(rr.getAssignmentRule()) ? rr.getHbQty()
                + rr.getPbQty() : rr.getQty() : bdQty);
            r.setHardbackQuantity(updateHbPbQty ? AssignmentRule.FIRST_AVAILABLE.equals(rr.getAssignmentRule()) ? 0
                : rr.getHbQty() : bdHbqty);
            r.setPaperbackQuantity(updateHbPbQty ? AssignmentRule.FIRST_AVAILABLE.equals(rr.getAssignmentRule()) ? 0
                : rr.getPbQty() : bdPbqty);
            r.setFund(get(rr.getFundId()));
            r.setHardbackfund(get(rr.getHbFundId()));
            r.setPaperbackfund(get(rr.getPbFundId()));
          });
    });
    allocation.setDateUpdated(LocalDateTime.now());
    Allocation allocationSaved = allocationService.save(allocation);
    boolean performRematch = releases.stream().allMatch(ReleaseRequest::getRematchAllocationAfterUpdate);
    publisher.publishEvent(
        AllocationUpdated.from(
            allocationSaved,
            false,
            false,
            false,
                performRematch,
            username));

    if (performRematch) {
      performRematch(allocation);
    }
  }

  private void performRematch(Allocation allocation) {
    rematchAllocationOutboundChannel.send(
            MessageBuilder.withPayload(allocation.getAllocationId()).build());
  }

  private boolean isUpdateHbPbQty(List<Allocation> bd, int bdQty, List<ReleaseRequest> releases) {
    if (releases.stream().anyMatch(r -> !r.isQtyValid())) {
      return false;
    }
    if (bd.isEmpty()) {
      return true;
    }
    int releaseQty = releases.stream().map(ReleaseRequest::getQty).reduce(0, Integer::sum);

    return releaseQty == bdQty;
  }

  @Transactional
  public List<AllocationInfo> updateBranchAllocations(List<BranchAllocationInfo> branches, Boolean rematchAllocationAfterUpdate, String username) {
    Affirm.of(branches).notNull("Branch list can not be null");
    List<AllocationInfo> saved = new ArrayList<>();
    branches.forEach(b -> {
      Allocation allocation;
      if (CollectionUtils.isNotEmpty(b.getCategories())) {
        if (b.getAllocationId() != null) {
          // existing allocation
          allocation = allocationService.findById(AllocationId.of(b.getAllocationId()));
          allocation = allocation.toBuilder()
              .fund(Optional.ofNullable(b.getFundId())
                  .map(fundService::getFund)
                  .orElse(null)
              )
              .status(b.getStatus())
              .notes(b.getNotes())
              .dateUpdated(LocalDateTime.now())
              .build();
          updateReleases(b.getReleases(), allocation.getReleases());
        } else {
          // new allocation
          Branch branch = customerService.getBranch(BranchId.of(b.getBranchId()));
          CustomerStandingOrder cso = getCustomerStandingOrder(CustomerId.of(b.getCustomerId()),
              StandingOrderId.of(b.getStandingOrderId()));
          Allocation baParent = allocationService.findById(AllocationId.of(b.getBaParentId()));
          List<Category> categories = categoryRepository.findByCodeIn(b.getCategories());
          allocation = Allocation.builder()
              .allocationId(AllocationId.of(UUID.randomUUID()))
              .branch(branch)
              .categories(categories)
//              .categories(b.getCategories())
              .fund(Optional.ofNullable(b.getFundId())
                  .map(fundService::getFund)
                  .orElse(null)
              )
              .allocationPreference(null)
              .notes(b.getNotes())
              .status(AllocationStatus.ACTIVE)
              .customerStandingOrder(cso)
              .baParent(baParent)
              .dateAdded(LocalDateTime.now())
              .dateUpdated(LocalDateTime.now())
              .build();
          allocation.setReleases(create(allocation, b.getReleases()));
        }
        saved.add(AllocationInfo.from(allocationService.save(allocation)));
      } else {
        log.warn(String.format("Found blank category in branch allocation info %s", b));
      }
    });
    if (!saved.isEmpty()) {
      Allocation alloc = allocationService.updateAllocationQtys(AllocationId.of(saved.get(0).getBaParentId().getId()));
      publisher.publishEvent(
          AllocationUpdated.from(
              alloc, false, false, false, rematchAllocationAfterUpdate, username));

      if (rematchAllocationAfterUpdate) {
        performRematch(alloc);
      }
    }
    return saved;
  }

  private List<Release> create(Allocation allocation, List<ReleaseRequest> srcList) {
    ReleaseRequest sr =
        Optional.ofNullable(srcList)
            .flatMap(
                l -> l.stream().filter(rr -> ReleaseType.INITIAL == rr.getReleaseType()).findAny())
            .orElse(null);

    Optional<Release> parentRelease =
        allocation.getBaParent().getReleases().stream()
            .filter(r -> ReleaseType.INITIAL == r.getReleaseType())
            .findFirst();

    return Lists.newArrayList(
        Release.builder()
            .releaseId(ReleaseId.of(UUID.randomUUID()))
            .allocation(allocation)
            .actionType(parentRelease.map(Release::getActionType).orElse(null))
            .releaseType(ReleaseType.INITIAL)
            .smallFormatPaperbackRule(null)
            .initialAssignmentRule(
                parentRelease.map(Release::getInitialAssignmentRule).orElse(null))
            .quantity(sr != null ? sr.getQty() : 0)
            .hardbackQuantity(sr != null ? sr.getHbQty() : 0)
            .paperbackQuantity(sr != null ? sr.getPbQty() : 0)
            .fund(sr != null ? get(sr.getFundId()) : null)
            .hardbackfund(sr != null ? get(sr.getHbFundId()) : null)
            .paperbackfund(sr != null ? get(sr.getPbFundId()) : null)
            .build());
  }

  private void updateReleases(List<ReleaseRequest> srcList, List<Release> targetList) {
    targetList.forEach(r -> Optional.ofNullable(srcList)
        .map(l -> l.stream()
            .filter(
                rr -> rr.getReleaseType() != null && rr.getReleaseType().equals(r.getReleaseType()))
            .findAny()
            .orElse(null)
        )
        .ifPresent(sr -> {
          r.setQuantity(sr.getQty());
          r.setHardbackQuantity(sr.getHbQty());
          r.setPaperbackQuantity(sr.getPbQty());
          r.setFund(get(sr.getFundId()));
          r.setHardbackfund(get(sr.getHbFundId()));
          r.setPaperbackfund(get(sr.getPbFundId()));
        })
    );
  }

  private ReleasePreference get(ReleasePreferenceId relPrefId) {
    return Optional.ofNullable(relPrefId)
        .map(rpid -> relPrefRepo.findById(rpid).orElse(null))
        .orElse(null);
  }

  private Fund get(FundId fundId) {
    return Optional.ofNullable(fundId)
        .map(f -> fundService.getFund(fundId.getId()))
        .orElse(null);
  }

  private List<CreateOrUpdateAllocation> createAllocations(
      List<AllocationPreference> allocPrefs, CustomerStandingOrderId csoId) {

    List<CreateOrUpdateAllocation> result = new ArrayList<>();
    allocPrefs.forEach(allocationPreference -> {

      Allocation allocation = allocationService.findByCsoIdAndAllocationPrefId(
          csoId, allocationPreference.getAllocationPreferenceId());

      CreateOrUpdateAllocation cmd =
          CreateOrUpdateAllocation.builder()
              .id(Optional.ofNullable(allocation)
                  .map(al -> al.getAllocationId().getId())
                  .orElse(UUID.randomUUID()))
              .allocationPreference(allocationPreference)
              .categories(allocationPreference.getCategories())
              .customerReference(allocationPreference.getCustomerReference())
              .deliveryInstructions(allocationPreference.getDeliveryInstructions())
              .notes(allocationPreference.getNotes())
              .fund(allocationPreference.getFund())
              .build();

      cmd.addRelease(
          getCreateRelease(allocationPreference.getReleasePreference(ReleaseType.INITIAL),
              allocation));

      result.add(cmd);
    });

    return result;
  }

  private CreateRelease getCreateRelease(ReleasePreference releasePreference,
      Allocation allocation) {

    Release release = Optional.ofNullable(allocation)
        .map(al -> releaseService.findByReleasePrefIdAndAllocationId(
            releasePreference.getReleasePreferenceId(), al.getAllocationId()))
        .orElse(null);

    ReleaseId id = Optional.ofNullable(release)
        .map(Release::getReleaseId)
        .orElse(ReleaseId.of(UUID.randomUUID()));

    return CreateRelease.from(id.getId(), releasePreference);
  }

  private StandingOrder getStandingOrderById(UUID standingOrderId) {

    return standingOrderRepository
        .findById(StandingOrderId.of(standingOrderId))
        .orElseThrow(
            () -> new ResourceNotFoundException(StandingOrder.class, standingOrderId.toString()));
  }

  private AllocationPreference getAllocationPreferenceById(UUID allocationPreferenceId) {
    return this.allocationPreferenceRepository
        .findById(AllocationPreferenceId.of(allocationPreferenceId))
        .orElse(null);
  }

  private CustomerStandingOrder getCustomerStandingOrderById(
      CustomerStandingOrderId customerStandingOrderId) {
    return this.customerStandOrderRepo
        .findById(customerStandingOrderId)
        .orElseThrow(() -> new ResourceNotFoundException(
            CustomerStandingOrder.class, customerStandingOrderId.toString()));
  }

  private Integer getQty(Integer qty) {
    return qty == null ? 0 : qty;
  }

}

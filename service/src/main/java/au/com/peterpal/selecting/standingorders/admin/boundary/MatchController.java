package au.com.peterpal.selecting.standingorders.admin.boundary;

import au.com.peterpal.selecting.standingorders.standingorder.control.MatchingService;
import au.com.peterpal.selecting.standingorders.standingorder.model.EmptySOSummary;
import au.com.peterpal.selecting.standingorders.standingorder.model.SOSummary;
import au.com.peterpal.selecting.standingorders.standingorder.model.SubjectMapEntry;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Log4j2
@RequiredArgsConstructor
@RequestMapping("/api/match")
@SecurityRequirement(name = "BearerAuth")
public class MatchController {

  private final MatchingService matchingService;

  @GetMapping("/product-observers/size")
  @Operation(summary = "Return product observer list size")
  public int getNumberOfObservers() {
    return matchingService.observerMapSize();
  }

  @GetMapping("/product-observers/load-time")
  @Operation(summary = "Time it took to load all product observers")
  public LoadTimeInfo getLoadTime() {
    return LoadTimeInfo.of(matchingService.getLoadTime());
  }

  @GetMapping("/product-observers/{id}")
  @Operation(summary = "Find product observer by standing order id")
  public SOSummary findObserver(@PathVariable UUID id) {
    return matchingService.findObserver(id)
        .orElse(new EmptySOSummary());
  }

  @AllArgsConstructor(staticName = "of")
  public static class LoadTimeInfo {

    @Getter
    private long millis;

    @JsonProperty("toSeconds")
    public long toSeconds() {
      return TimeUnit.MILLISECONDS.toSeconds(millis);
    }

    @JsonProperty("toMinutes")
    public long toMinutes() {
      return TimeUnit.MILLISECONDS.toMinutes(millis);
    }
  }

  @GetMapping("/subject-map")
  @Operation(summary = "Return the current subject map")
  public List<SubjectMapEntry> getSubjectMap() {
    return matchingService.subjectMap();
  }

  @PostMapping("/subject-map")
  @Operation(summary = "Replace the current subject map")
  public List<SubjectMapEntry> addSubjectMapEntry(@RequestBody @Valid List<SubjectMapEntry> request) {
    return matchingService.addSubjectMapEntries(request);
  }

  @DeleteMapping("/subject-map")
  @Operation(summary = "Remove the current subject map")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteAll() {
    matchingService.deleteAllSubjectMapEntries();
  }

  @DeleteMapping("/subject-map/subject")
  @Operation(summary = "Remove codes from current subject map")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteSubjectMapEntry(@RequestParam("code") List<String> codeList) {
    matchingService.deleteSubjectMapEntries(codeList);
  }
}

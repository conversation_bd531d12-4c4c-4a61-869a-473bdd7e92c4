package au.com.peterpal.selecting.standingorders.catalog.model;

import java.util.Date;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Data;

@Data
@Embeddable
public class MediaFile {

	@Enumerated(EnumType.STRING)
	private MediaFileTypeCode fileType;

	@Enumerated(EnumType.STRING)
	private MediaFileFormatCode fileFormat;

	private Integer imageResolution;

	@Enumerated(EnumType.STRING)
	private MediaFileLinkTypeCode linkType;

	private String link;

	@Temporal(TemporalType.DATE)
	private Date fileDate;

}

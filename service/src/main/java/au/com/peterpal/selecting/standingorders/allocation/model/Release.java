package au.com.peterpal.selecting.standingorders.allocation.model;

import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.CreateRelease;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.SmallFormatPaperbackRule;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.extern.log4j.Log4j2;

import java.util.Optional;

@Data
@Builder
@With
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Log4j2
@Entity
@Table(name = "release")
public class Release {

  @NonNull
  @EqualsAndHashCode.Include
  @EmbeddedId
  private ReleaseId releaseId;

  @JsonIgnore
  @ManyToOne(fetch = FetchType.LAZY)
  private Allocation allocation;

  @ManyToOne
  @JoinColumn(name = "releasePreferenceId")
  private ReleasePreference preference;

  @Enumerated(EnumType.STRING)
  private ReleaseType releaseType;

  @Enumerated(EnumType.STRING)
  private ActionType actionType;

  @Enumerated(EnumType.STRING)
  @Builder.Default
  private AssignmentRule initialAssignmentRule = AssignmentRule.FIRST_AVAILABLE;

  @Enumerated(EnumType.STRING)
  private SmallFormatPaperbackRule smallFormatPaperbackRule;

  @ManyToOne
  private Fund fund;

  @ManyToOne
  @JoinColumn(name = "hard_back_fund_id")
  private Fund hardbackfund;

  @ManyToOne
  @JoinColumn(name = "paper_back_fund_id")
  private Fund paperbackfund;

  private Integer quantity;

  @Column(name = "hard_back_quantity")
  private Integer hardbackQuantity;

  @Column(name = "paper_back_quantity")
  private Integer paperbackQuantity;

  public static Release from(CreateRelease createRelease) {
    return Release.builder()
      .actionType(createRelease.getActionType())
      .fund(createRelease.getFund())
      .hardbackfund(createRelease.getHbFund())
      .paperbackfund(createRelease.getPbFund())
      .hardbackQuantity(createRelease.getHbQuantity())
      .releaseId(ReleaseId.of(createRelease.getId()))
      .initialAssignmentRule(createRelease.getAssignmentRule())
      .paperbackQuantity(createRelease.getPbQuantity())
      .preference(createRelease.getReleasePreference())
      .quantity(createRelease.getQuantity())
      .releaseType(createRelease.getReleaseType())
      .smallFormatPaperbackRule(createRelease.getSmallFormatPaperbackRule())
      .build();
  }

  public Integer getQuantity(ReleaseFormat format) {
    if (format == null) {
      log.warn("Returning zero quantity because release format is null");
      return 0;
    }

    Integer qty = 0;
    if (format.equals(ReleaseFormat.HB)) {
      qty = hardbackQuantity;
    } else if (format.equals(ReleaseFormat.PB)) {
      qty = paperbackQuantity;
    } else if (format.equals(ReleaseFormat.SFPB)) {
      qty = quantity;
    }

    return qty;
  }

  public Fund getFund(ReleaseFormat format) {
    if (format == null) {
      return null;
    }
    switch (format) {
      case HB:
        return Optional.ofNullable(hardbackfund).orElse(fund);
      case PB:
        return Optional.ofNullable(paperbackfund).orElse(fund);
      default:
        return fund;
    }
  }
}

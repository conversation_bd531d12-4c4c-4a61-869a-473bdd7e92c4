package au.com.peterpal.selecting.standingorders.pendingorder.control;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class POCreatorFactory {

  private final Map<AssignmentRule, POCreator> creatorMap;

  public Optional<POCreator> getCreator(final AssignmentRule type) {
    if (!creatorMap.containsKey(type)) {
      return Optional.empty();
    }
    return Optional.of(creatorMap.get(type));
  }
}

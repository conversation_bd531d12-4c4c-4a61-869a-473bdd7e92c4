package au.com.peterpal.selecting.standingorders.ext.category.boundary;

import au.com.peterpal.selecting.standingorders.ext.category.control.CategoryRepository;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

@RestController
@Log4j2
@RequiredArgsConstructor
@RequestMapping("/api/category")
@SecurityRequirement(name = "BearerAuth")
public class CategoryController {

  private final CategoryRepository categoryRepository;

  @GetMapping("/{id}")
  @Operation(summary = "Get category")
  public Category getCategory(@PathVariable CategoryId id) {
    return categoryRepository.findById(id).orElse(null);
  }

  @GetMapping
  @Operation(summary = "Get all categories")
  public List<Category> getAll() {
    return categoryRepository.findAll();
  }
  
}

package au.com.peterpal.selecting.standingorders.allocation.model;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.CreateOrUpdateAllocation;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.Branch;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.persistence.CascadeType;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.ToString;
import lombok.With;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder(toBuilder = true)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "allocation")
public class Allocation {

  @NonNull
  @EqualsAndHashCode.Include
  @EmbeddedId
  private AllocationId allocationId;

  @JsonIgnore
  @ManyToOne(fetch = FetchType.LAZY)
  private CustomerStandingOrder customerStandingOrder;

  @ManyToOne
  @JoinColumn(name = "allocationPreferenceId")
  private AllocationPreference allocationPreference;

  @NonNull
  @NotNull
  @Builder.Default
  @Enumerated(EnumType.STRING)
  private AllocationStatus status = AllocationStatus.ACTIVE;

  @Builder.Default
  @ManyToMany
  private List<Category> categories = new ArrayList<>();

  @ManyToOne
  @JoinColumn(name = "branchId")
  private Branch branch;

  private String customerReference;

  private String deliveryInstructions;

  private String notes;

  @Builder.Default
  @OneToMany(mappedBy = "allocation", cascade = CascadeType.ALL, orphanRemoval = true)
  private List<Release> releases = new ArrayList<>();

  @ManyToOne
  @JoinColumn(name = "fundId")
  private Fund fund;

  private String collectionCode;

  @ManyToOne
  @JoinColumns({
      @JoinColumn(name = "baParentId", referencedColumnName = "id")
  })
  @Builder.Default
  @JsonIgnore
  private Allocation baParent = null;
  private LocalDateTime dateAdded;

  private LocalDateTime dateUpdated;

  public Allocation addRelease(Release release) {
    return Optional.ofNullable(release)
        .map(r -> {
          r.setAllocation(this);
          releases.add(r);
          return this;
        })
        .orElseThrow(() -> new IllegalArgumentException("Release must not be null"));
  }

  public static Allocation from(
      CreateOrUpdateAllocation event, CustomerStandingOrder customerStandingOrder) {

    Allocation allocation = Allocation.builder()
        .allocationId(AllocationId.of(event.getId()))
        .customerReference(event.getCustomerReference())
        .categories(event.getCategories())
        .deliveryInstructions(event.getDeliveryInstructions())
        .notes(event.getNotes())
        .allocationPreference(event.getAllocationPreference())
        .fund(event.getFund())
        .customerStandingOrder(customerStandingOrder)
        .build();

    event
        .getReleases()
        .forEach(createRelease -> allocation.addRelease(Release.from(createRelease)));

    return allocation;
  }

  public static Allocation from(CreateOrUpdateAllocation event) {

    Allocation allocation = Allocation.builder()
        .allocationId(AllocationId.of(event.getId()))
        .customerReference(event.getCustomerReference())
        .deliveryInstructions(event.getAllocationPreference().getDeliveryInstructions())
        .notes(event.getAllocationPreference().getNotes())
        .allocationPreference(event.getAllocationPreference())
        .fund(event.getFund())
        .categories(event.getCategories())
        .build();

    event
        .getReleases()
        .forEach(createRelease -> allocation.addRelease(Release.from(createRelease)));

    return allocation;
  }

//  public static Allocation from(
//      AllocationUpdated event,
//      List<Release> releases,
//      CustomerStandingOrder customerStandingOrder,
//      AllocationPreference allocationPreference,
//      Fund fund) {
//
//    return Allocation.builder()
//        .allocationId(AllocationId.of(event.getId()))
//        .customerReference(event.getCustomerReference())
//        .deliveryInstructions(event.getDeliveryInstructions())
//        .notes(event.getNotes())
////        @TODO
//        .categories(Lists.asList(event.getCategory()))
//        .allocationPreference(allocationPreference)
//        .fund(fund)
//        .customerStandingOrder(customerStandingOrder)
//        .status(event.getStatus())
//        .releases(releases)
//        .build();
//  }

  public static List<Allocation> from(List<CreateOrUpdateAllocation> allocations) {

    List<Allocation> allocationsList = new ArrayList<>();
    allocations.forEach(allocation -> allocationsList.add(Allocation.from(allocation)));

    return allocationsList;
  }

  public static List<Allocation> from(
      CustomerStandingOrder customerStandingOrder, List<CreateOrUpdateAllocation> allocations) {

    List<Allocation> allocationsList = new ArrayList<>();
    allocations.forEach(
        allocation -> allocationsList.add(Allocation.from(allocation, customerStandingOrder)));

    return allocationsList;
  }

  public Release getRelease(ReleaseType rt) {
    if (releases.isEmpty()) {
      throw new IllegalStateException("Can not get release from empty list of releases");
    }
    return releases.stream()
        .filter(r -> r.getReleaseType().equals(rt))
        .findFirst()
        .orElseThrow(() -> new ResourceNotFoundException(
            String.format("Allocation %s does not have release of type %s", this.allocationId,
                rt)));
  }

  public Optional<Release> findRelease(ReleaseType rt) {
    return releases.stream().filter(r -> r.getReleaseType().equals(rt)).findAny();
  }

  public Integer getInitialQuantity() {
    if (releases.isEmpty()) {
      return 0;
    }
    Release r = getRelease(ReleaseType.INITIAL);
    return Optional.ofNullable(r.getQuantity()).orElse(0);
  }

  public Integer getInitialHBQuantity() {
    if (releases.isEmpty()) {
      return 0;
    }
    Release r = getRelease(ReleaseType.INITIAL);
    return Optional.ofNullable(r.getHardbackQuantity()).orElse(0);
  }

  public Integer getInitialPBQuantity() {
    if (releases.isEmpty()) {
      return 0;
    }
    Release r = getRelease(ReleaseType.INITIAL);
    return Optional.ofNullable(r.getPaperbackQuantity()).orElse(0);
  }

  public boolean isBranchAllocation() {
    return branch != null;
  }

  public boolean isNotBranchAllocation() {
    return !isBranchAllocation();
  }

  public boolean containsCategory(Category category) {
    if (category == null) {
      return false;
    }
    return categories.contains(category);
  }

  public List<String> getCategoryCodes() {
    return categories == null ? new ArrayList<>() :
        categories.stream().map(e -> e.getCode()).collect(Collectors.toList());
  }
}

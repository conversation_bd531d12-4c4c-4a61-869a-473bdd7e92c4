package au.com.peterpal.selecting.standingorders.standingorder.control;

import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonStatus;
import au.com.peterpal.selecting.standingorders.standingorder.model.RejectionReasonType;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Log4j2
@Service
@RequiredArgsConstructor
@Transactional
public class RejectionReasonTypeService {

    private final RejectionReasonTypeRepository rejectionReasonTypeRepository;

  public List<RejectionReasonType> findActiveAndDisplayable(Sort sort) {
    return rejectionReasonTypeRepository.findAllByStatusAndDisplayable(
        RejectionReasonStatus.ACTIVE, Boolean.TRUE, sort);
  }
}

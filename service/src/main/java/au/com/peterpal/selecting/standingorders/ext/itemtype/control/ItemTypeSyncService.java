package au.com.peterpal.selecting.standingorders.ext.itemtype.control;

import au.com.peterpal.common.utils.ext.SyncService;
import au.com.peterpal.selecting.standingorders.ext.itemtype.boundary.dto.ItemTypeMessage;
import au.com.peterpal.selecting.standingorders.ext.itemtype.model.ItemType;
import au.com.peterpal.selecting.standingorders.ext.itemtype.model.ItemTypeStatus;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@Service
@Log4j2
public class ItemTypeSyncService extends
    SyncService<ItemTypeRepository, ItemTypeMessage, ItemType> {

  public ItemTypeSyncService(ItemTypeRepository itemTypeRepository) {
    super(itemTypeRepository);
  }

  @Override
  public ItemTypeMessage handle(ItemTypeMessage msg) {
    return ItemTypeStatus.INACTIVE.equals(msg.getStatus()) ? msg : super.handle(msg);
  }

  @Override
  protected Object getId(ItemTypeMessage msg) {
    return msg.getItemTypeId();
  }

  @Override
  protected ItemTypeMessage update(ItemType existing, ItemTypeMessage msg) {
    if (ItemTypeStatus.INACTIVE.equals(msg.getStatus())) {
      repo.deleteById(existing.getItemTypeId());
    } else if (ItemTypeStatus.ACTIVE.equals(msg.getStatus())) {
      existing.setCode(msg.getCode());
      existing.setName(msg.getName());
      save(existing);
    } else {
      throw new IllegalStateException(String.format("Invalid Item type status code %s", msg.getStatus()));
    }
    return msg;
  }

  @Override
  protected ItemTypeMessage create(ItemTypeMessage msg) {
    if (ItemTypeStatus.ACTIVE.equals(msg.getStatus())) {
      save(ItemType.builder()
        .itemTypeId(msg.getItemTypeId())
        .code(msg.getCode())
        .name(msg.getName())
        .build());
    }
    return msg;
  }
}

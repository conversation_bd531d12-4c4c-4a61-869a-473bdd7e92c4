package au.com.peterpal.selecting.standingorders.standingorder.control;

import au.com.peterpal.selecting.standingorders.standingorder.model.CombinedTerm;
import au.com.peterpal.selecting.standingorders.standingorder.model.CombinedTermId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CombinedTermRepository extends JpaRepository<CombinedTerm, CombinedTermId> {}

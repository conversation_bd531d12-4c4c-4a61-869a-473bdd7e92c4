package au.com.peterpal.selecting.standingorders.ext.budget.control;

import au.com.peterpal.common.utils.ext.SyncService;
import au.com.peterpal.selecting.standingorders.ext.budget.boundary.dto.BudgetTargetMessage;
import au.com.peterpal.selecting.standingorders.ext.budget.entity.BudgetTarget;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class BudgetTargetSyncService
    extends SyncService<BudgetTargetRepository, BudgetTargetMessage, BudgetTarget> {

  private final CustomerService customerService;

  public BudgetTargetSyncService(
      CustomerService customerService, BudgetTargetRepository budgetTargetRepository) {
    super(budgetTargetRepository);
    this.customerService = customerService;
  }

  @Override
  protected Object getId(BudgetTargetMessage msg) {
    return msg.getBudgetTargetId();
  }

  @Override
  protected BudgetTargetMessage update(BudgetTarget existing, BudgetTargetMessage msg) {
    existing.setCustomer(customerService.findById(msg.getCustomerId()));
    existing.setStartDate(msg.getStartDate());
    existing.setEndDate(msg.getEndDate());
    existing.setJulTarget(msg.getJulTarget());
    existing.setAugTarget(msg.getAugTarget());
    existing.setSepTarget(msg.getSepTarget());
    existing.setOctTarget(msg.getOctTarget());
    existing.setNovTarget(msg.getNovTarget());
    existing.setDecTarget(msg.getDecTarget());
    existing.setJanTarget(msg.getJanTarget());
    existing.setFebTarget(msg.getFebTarget());
    existing.setMarTarget(msg.getMarTarget());
    existing.setAprTarget(msg.getAprTarget());
    existing.setMayTarget(msg.getMayTarget());
    existing.setJunTarget(msg.getJunTarget());
    save(existing);
    return msg;
  }

  @Override
  protected BudgetTargetMessage create(BudgetTargetMessage msg) {
    BudgetTarget budgetTarget =
        BudgetTarget.builder()
            .budgetTargetId(msg.getBudgetTargetId())
            .customer(customerService.findById(msg.getCustomerId()))
            .startDate(msg.getStartDate())
            .endDate(msg.getEndDate())
            .julTarget(msg.getJulTarget())
            .augTarget(msg.getAugTarget())
            .sepTarget(msg.getSepTarget())
            .octTarget(msg.getOctTarget())
            .novTarget(msg.getNovTarget())
            .decTarget(msg.getDecTarget())
            .janTarget(msg.getJanTarget())
            .febTarget(msg.getFebTarget())
            .marTarget(msg.getMarTarget())
            .aprTarget(msg.getAprTarget())
            .mayTarget(msg.getMayTarget())
            .junTarget(msg.getJunTarget())
            .build();
    save(budgetTarget);
    return msg;
  }
}

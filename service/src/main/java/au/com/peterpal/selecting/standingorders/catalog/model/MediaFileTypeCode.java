package au.com.peterpal.selecting.standingorders.catalog.model;

/**
 * @docRoot
 * Based on Onix List 38
 */
public enum MediaFileTypeCode {

	TNCOV ("07", "Front cover thumbnail", ""),
	HQCOV ("06", "Front cover high quality image", ""),
	COV ("04", "Front cover image", "Quality unspecified: if sending both a standard quality and a high quality image, use 04 for standard quality and 06 for high quality"),
	PROD ("01", "Whole product", "Link to a location where the whole product may be found – used for epublications"),
	SWDEMO ("02", "Software demo", ""),
	CONT ("08", "Contributor image", ""),
	SERIESIMG ("10", "Series image", ""),
	SERIESLOGO ("11", "Series logo", ""),
	PRODLOGO ("12", "Product logo", "Use only for a logo which is specific to an individual product"),
	PUBLOGO ("17", "Publisher logo", ""),
	IMPLOGO ("18", "Imprint logo", ""),
	IPIMG ("23", "Inside page image", ""),
	VIDEO ("29", "Video segment", ""),
	AUDIO ("30", "Audio segment", "");

	private final String code;
	private final String description;
	private final String notes;

	MediaFileTypeCode(String code, String description, String notes) {
		this.code = code;
		this.description = description;
		this.notes = notes;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public String getNotes() {
		return notes;
	}

	public static MediaFileTypeCode mapOnixCode(String onixCode) {
		for (MediaFileTypeCode value : MediaFileTypeCode.values()) {
			if (value.code.equals(onixCode)) {
				return value;
			}
		}
		throw new IllegalArgumentException("Invalid " + MediaFileTypeCode.class.getSimpleName() + ": " + onixCode);
	}

	@Override
	public String toString() {
		return this.description;
	}
}

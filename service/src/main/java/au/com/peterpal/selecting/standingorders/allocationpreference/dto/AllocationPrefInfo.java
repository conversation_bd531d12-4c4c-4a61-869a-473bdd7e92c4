package au.com.peterpal.selecting.standingorders.allocationpreference.dto;

import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.SmallFormatPaperbackRule;

import java.util.List;
import java.util.UUID;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AllocationPrefInfo {

  private UUID id;

  @NotBlank
  private String customerCode;

  @NotNull
  private List<String> categories;

  private String customerReference;

  private String deliveryInstructions;

  private String notes;

  private String fundCode;

  private ReleasePrefInfo relPrefInfo;

  //@Builder.Default
  //private List<ReleasePrefInfo> relInfoList = new ArrayList<>();

  @Data
  @Builder
  public static class ReleasePrefInfo {

    private UUID id;

    @NotNull
    private ReleaseType releaseType;

    @NotNull
    private ActionType actionType;

    private String fundCode;

    private String hardbackFundCode;
    private String paperbackFundCode;

    private int qtyTotal;
    private int qtyHardback;
    private int qtyPaperback;

    private AssignmentRule assignmentRule;
    private SmallFormatPaperbackRule smallFormatPaperbackRule;
  }
}

package au.com.peterpal.selecting.standingorders.standingorder.dto;

import au.com.peterpal.selecting.standingorders.standingorder.model.CombinedTermId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import java.util.*;
import java.util.stream.Collectors;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StandingOrderDTO {
  private UUID standingOrderId;
  private String standingOrderNumber;
  private String standingOrderStatus;
  private String description;
  private String notes;
  private List<CombinedTermDTO> combinedTerms;

  public static List<StandingOrderDTO> from(List<StandingOrder> standingOrders) {
    return Optional.ofNullable(standingOrders).stream()
        .flatMap(Collection::stream)
        .map(StandingOrderDTO::from)
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  public static StandingOrderDTO from(StandingOrder standingOrder) {
    Map<CombinedTermId, List<TermDTO>> termsGroupedByCombinedTermId =
        standingOrder.getTerms().stream()
            .collect(
                Collectors.groupingBy(
                    term -> term.getCombinedTerm().getCombinedTermId(),
                    Collectors.mapping(
                        term ->
                            TermDTO.builder()
                                .termId(term.getTermId())
                                .type(term.getType() != null ? term.getType().name() : null)
                                .operation(term.getOperation().name())
                                .value(term.getValue())
                                .build(),
                        Collectors.toList())));

    List<CombinedTermDTO> combinedTerms =
        termsGroupedByCombinedTermId.entrySet().stream()
            .map(
                entry ->
                    CombinedTermDTO.builder()
                        .combinedTermId(entry.getKey())
                        .terms(entry.getValue())
                        .build())
            .collect(Collectors.toList());

    return StandingOrderDTO.builder()
        .standingOrderId(standingOrder.getStandingOrderId().getId())
        .standingOrderNumber(standingOrder.getStandingOrderNumber())
        .standingOrderStatus(standingOrder.getStandingOrderStatus().name())
        .description(standingOrder.getDescription())
        .notes(standingOrder.getNotes())
        .combinedTerms(combinedTerms)
        .build();
  }
}

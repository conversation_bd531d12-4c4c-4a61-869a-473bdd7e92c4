package au.com.peterpal.selecting.standingorders.pendingorder.control;

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.CopyPendingOrderDto;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.POCreateInfo;
import au.com.peterpal.selecting.standingorders.pendingorder.model.BranchDistribution;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import au.com.peterpal.selecting.standingorders.standingorder.dto.ProductInfo;
import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

public class FirstAvailableRule extends AbstractPOCreator {

  @Override
  protected List<PendingOrder> createPOList(POCreateInfo info) {
    if (info.getProducts().isEmpty()) {
      return Collections.EMPTY_LIST;
    } else {
      ProductInfo pi =
          info.getProducts().stream()
              .max(
                  Comparator.comparing(
                      p -> Optional.ofNullable(p.getPubDate()).orElse(LocalDate.MIN)))
              .get();

      Allocation allocation = info.getAllocation();
      Release release = info.getRelease();
      Title title = info.getTitle();
      return Arrays.asList(
          PendingOrder.builder()
              .pendingOrderId(PendingOrderId.of(UUID.randomUUID()))
              .customerReference(getCustomerRef(allocation.getCustomerReference(), info.getPref()))
              .orderStatus(PendingOrderStatus.NEW)
              .customer(allocation.getCustomerStandingOrder().getCustomer())
              .fund(getFund(pi.getFormat(), release, allocation, info.getPref()))
              .orderedProductReference(pi.getIsbn())
              .format(pi.getFormat())
              .release(release)
              .quantity(release.getQuantity())
              .category(title.getCategory())
              .collectionCode(allocation.getCollectionCode())
              .deliveryInstructions(
                  getDeliveryInst(allocation.getDeliveryInstructions(), info.getPref()))
              .notes(getNotes(allocation.getNotes(), info.getPref()))
              .title(title)
              .price(pi.getPrice())
              .publicationDate(pi.getPubDate())
              .currencyCode(pi.getCurrencyCode())
              .branchDistributions(
                  buildBranchDistributions(
                      info.getBranchAllocations(), pi.getFormat(), title.getCategory()))
              .build());
    }
  }

  Set<BranchDistribution> buildBranchDistributions(
      List<Allocation> branchAllocations, ReleaseFormat format, Category category) {

    if (CollectionUtils.isNotEmpty(branchAllocations)) {
      return branchAllocations.stream()
          .filter(a -> a.findRelease(ReleaseType.INITIAL).isPresent())
          .filter(a -> a.getRelease(ReleaseType.INITIAL).getActionType() != ActionType.IGNORE)
          .map(
              allocation -> {
                Release release = allocation.getRelease(ReleaseType.INITIAL);
                String fundCode =
                    Optional.ofNullable(release.getFund(format)).map(Fund::getCode).orElse(null);
                Integer quantity = release.getQuantity();
                return BranchDistribution.from(allocation, category, fundCode, quantity);
              })
          .collect(Collectors.toSet());
    } else {
      return Sets.newHashSet();
    }
  }

  @Override
  public List<PendingOrder> copy(CopyPendingOrderDto request) {
    ArrayList<PendingOrder> copiedPendingOrders = Lists.newArrayList();
    Release release = request.getAllocation().getRelease(ReleaseType.INITIAL);

    CopyPendingOrderDto.PendingOrderDetail pendingOrderDetail =
        request.getPendingOrderDetailMap().values().stream().findFirst().get();

    if (release.getQuantity() > pendingOrderDetail.getQuantityTaken()) {
      int deltaQuantity = release.getQuantity() - pendingOrderDetail.getQuantityTaken();
      PendingOrder newPendingOrder =
          PendingOrder.builder()
              .pendingOrderId(PendingOrderId.of(UUID.randomUUID()))
              .customerReference(pendingOrderDetail.getCustomerReference())
              .orderStatus(PendingOrderStatus.NEW)
              .customer(request.getAllocation().getCustomerStandingOrder().getCustomer())
              .fund(pendingOrderDetail.getFund())
              .orderedProductReference(pendingOrderDetail.getIsbn())
              .format(pendingOrderDetail.getFormat())
              .release(release)
              .quantity(deltaQuantity)
              .category(pendingOrderDetail.getCategory())
              .collectionCode(pendingOrderDetail.getCollectionCode())
              .deliveryInstructions(pendingOrderDetail.getDeliveryInstruction())
              .notes(pendingOrderDetail.getNotes())
              .title(request.getTitle())
              .price(pendingOrderDetail.getPrice())
              .publicationDate(pendingOrderDetail.getPublicationDate())
              .currencyCode(pendingOrderDetail.getCurrencyCode())
              .supplier(pendingOrderDetail.getSupplier())
              .build();

      if (CollectionUtils.isNotEmpty(request.getBranchAllocations())) {
        copyBranchDistributions(
            newPendingOrder,
            pendingOrderDetail.getBrantchQtyTakenMap(),
            request.getBranchAllocations());
      }
      copiedPendingOrders.add(newPendingOrder);
    }

    return copiedPendingOrders;
  }

  void copyBranchDistributions(
      PendingOrder targetPendingOrder,
      Map<AllocationId, Integer> qtyTakenMap,
      List<Allocation> branchAllocations) {
    targetPendingOrder.setBranchDistributions(Sets.newHashSet());

    // only copy as many as the delta
    Integer outstandingQty = targetPendingOrder.getQuantity();
    for (Allocation branchAllocation : branchAllocations) {
      Integer releaseQuantity = branchAllocation.getRelease(ReleaseType.INITIAL).getQuantity();
      Integer qtyTaken = qtyTakenMap.computeIfAbsent(branchAllocation.getAllocationId(), s -> 0);
      Integer availableQty = releaseQuantity - qtyTaken;

      // fully taken, need to be skipped
      if (releaseQuantity > qtyTaken) {
        int branchQuantity = outstandingQty >= availableQty ? availableQty : outstandingQty;
        outstandingQty -= branchQuantity;

        targetPendingOrder
            .getBranchDistributions()
            .add(
                BranchDistribution.from(
                    branchAllocation,
                    targetPendingOrder.getCategory(),
                    Optional.of(targetPendingOrder)
                        .map(PendingOrder::getFund)
                        .map(Fund::getCode)
                        .orElse(null),
                    branchQuantity));

        if (outstandingQty == 0) {
          break;
        }
      }
    }
  }
}

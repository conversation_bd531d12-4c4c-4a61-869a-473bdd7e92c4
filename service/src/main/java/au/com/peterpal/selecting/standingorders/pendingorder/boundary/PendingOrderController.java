package au.com.peterpal.selecting.standingorders.pendingorder.boundary;

import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.pendingorder.control.PendingOrderBL;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.*;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.*;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

@Log4j2
@RequiredArgsConstructor
@RestController
@SecurityRequirement(name = "BearerAuth")
@RequestMapping("/api/pending-orders")
public class PendingOrderController {

  private final PendingOrderBL pendingOrderBL;

  @PutMapping("/update")
  public UUID update(
      @RequestBody @Valid UpdatePendingOrderRequest request,
      @RequestHeader(value = "username", defaultValue = "", required = false) String username) {
    return pendingOrderBL.handle(request, username);
  }

  @PutMapping(path = "bulk-update")
  public List<PendingOrderId> bulkUpdate(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestBody @Valid BulkUpdatePendingOrderRequest bulkUpdatePendingOrderRequest) {
    log.debug("Received request from user {}: {}", username, bulkUpdatePendingOrderRequest);
    return pendingOrderBL.bulkUpdate(bulkUpdatePendingOrderRequest, username);
  }

  @PostMapping("/submit")
  public SubmitPendingOrderResponse submit(
      @RequestHeader(value = "username", defaultValue = "", required = false) String username,
      @RequestBody @Valid SubmitPendingOrderRequest request) {
    return pendingOrderBL.submitPendingOrders(request, username);
  }

  @GetMapping("/find-by-customer")
  public Page<PendingOrderResponse> findPendingOrderByCustomer(
      @RequestParam(value = "customerId", required = false) String id,
      @PageableDefault(sort = "customer.code", direction = Sort.Direction.DESC)
          Pageable pageRequest) {

    log.debug(() -> String.format("Finding pending orders by customer id %s", id));
    CustomerId customerId = Optional.ofNullable(id).map(CustomerId::of).orElse(null);
    return pendingOrderBL.handle(PendingOrderRequest.of(customerId), pageRequest);
  }

  @GetMapping("/find-by-title")
  public List<PendingOrderResponse> findPendingOrderByTitle(
      @RequestParam TitleId titleId,
      @RequestParam(required = false) PendingOrderStatus status,
      @RequestParam(required = false) String category) {
    PendingOrderByTitleRequest request = new PendingOrderByTitleRequest(titleId, status, category);
    log.debug("Finding pending orders by {}", request);
    return pendingOrderBL.handle(request);
  }

  @GetMapping("/find-products-detail-by-title")
  public List<ProductDetailDto> findProductsByTitle(
      @RequestParam(value = "titleId") TitleId titleId) {
    log.debug("Find products by {}", titleId);
    return pendingOrderBL.findProductsByTitle(titleId);
  }

  @PutMapping("/update-product-detail")
  public void updateProductDetail(
      @RequestBody UpdateProductDetailRequest request,
      @RequestHeader(defaultValue = "", required = false) String username) {
    pendingOrderBL.handle(request, username);
  }

  @PostMapping("/add-order-number")
  public void addOrderNumber(@RequestBody @Valid AddOrderNumberRequest request) {
    log.debug(() -> String.format("Request: %s", request));
    pendingOrderBL.addOrderNumber(request.getPendingOrderId(), request.getOrderNumber());
  }
}

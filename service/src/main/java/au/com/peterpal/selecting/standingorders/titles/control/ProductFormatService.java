package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.selecting.standingorders.catalog.model.ProductFormCode;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.Format;
import au.com.peterpal.selecting.standingorders.titles.entity.ProductFormatMapping;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
@Log4j2
@Transactional
public class ProductFormatService {

  private final ProductFormatMappingRepository productFormatMappingRepository;

  public Format map(ProductFormCode formCode) {
    log.trace(String.format("find product format by formCode %s", formCode));
    return productFormatMappingRepository
        .findById(formCode)
        .map(ProductFormatMapping::getFormat)
        .orElse(null);
  }
}

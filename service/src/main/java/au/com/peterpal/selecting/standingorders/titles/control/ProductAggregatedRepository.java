package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.selecting.standingorders.titles.entity.ProductAggregated;
import au.com.peterpal.selecting.standingorders.titles.entity.ProductAggregatedId;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ProductAggregatedRepository
    extends JpaRepository<ProductAggregated, ProductAggregatedId> {

  List<ProductAggregated> findAllByProductAggregatedIdIn(
      List<ProductAggregatedId> productAggregatedIds);
}

package au.com.peterpal.selecting.standingorders.config;

import java.util.Optional;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class SpringContext implements ApplicationContextAware {

  private static ApplicationContext context;

  /**
   * Returns the Spring managed bean instance of the given class type if it exists.
   *
   * @param beanClass
   * @param <T>
   * @return
   */
  public static <T extends Object> T getBean(Class<T> beanClass) {
    return Optional.ofNullable(context)
        .map(c -> c.getBean(beanClass))
        .orElse(null);
  }

  @Override
  public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    SpringContext.context = applicationContext;
  }
}

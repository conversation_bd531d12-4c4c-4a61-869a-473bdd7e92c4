package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.common.sequencenumbergenerator.SequenceNumberService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class TitleNumberGenerator {

  private static final String TITLE_NUMBER = "TITLE_NUMBER";

  @Value("${standingorders.titleNumberMask:TM-%08d}")
  private String titleNumberMask;

  @Value("${standingorders.titleNumberInitialValue:1}")
  private Long titleNumberInitialValue;

  private final SequenceNumberService sequenceNumberService;

  @EventListener
  public void onApplicationEvent(ContextRefreshedEvent event) {
      sequenceNumberService.create(1L, TITLE_NUMBER, titleNumberMask, titleNumberInitialValue);
  }

  public TitleNumberGenerator(SequenceNumberService sequenceNumberService) {
    this.sequenceNumberService = sequenceNumberService;
  }

  public String getNextTitleNumber() {
    return sequenceNumberService.next(1L, TITLE_NUMBER);
  }
}

package au.com.peterpal.selecting.standingorders.standingorder.dto;

import lombok.*;

import java.util.List;

@With
@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class UpdateStandingOrderRequest {

  private String description;

  private String notes;

  private String status;

  private List<TermsRequest> terms;
}

package au.com.peterpal.selecting.standingorders.pendingorder.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.standingorder.dto.ProductInfo;
import java.util.List;
import javax.validation.constraints.NotNull;

import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Builder
@Getter
public class POCreateInfo {
  @NonNull
  @NotNull
  private Title title;

  @NonNull
  @NotNull
  private List<ProductInfo> products;

  @NonNull
  @NotNull
  private Release release;

  @NonNull
  @NotNull
  private Allocation allocation;

  private List<Allocation> branchAllocations;

  private AllocationPreference pref;
}

package au.com.peterpal.selecting.standingorders.admin.dto;

import au.com.peterpal.selecting.standingorders.ext.budget.entity.FundType;
import java.time.LocalDate;
import java.util.List;

import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import lombok.Builder;
import lombok.ToString;
import lombok.Value;

@Value
@Builder
@ToString
public class SearchOrderRequest {
  String customerCode;
  String fundCode;
  FundType fundType;
  LocalDate orderDate;
  String staff;

  List<CategoryId> categoryIds;
}

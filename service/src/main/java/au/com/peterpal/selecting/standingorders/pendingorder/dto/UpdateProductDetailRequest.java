package au.com.peterpal.selecting.standingorders.pendingorder.dto;

import au.com.peterpal.selecting.standingorders.ext.supplier.entity.SupplierId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import au.com.peterpal.selecting.standingorders.utils.CurrencyCode;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.Value;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

@Value
@AllArgsConstructor(staticName = "of")
public class UpdateProductDetailRequest {
  @NotNull @NonNull TitleId titleId;
  @NotNull @NonNull String isbn;
  SupplierId supplierId;
  BigDecimal price;
  CurrencyCode currencyCode;
  LocalDate publicationDate;
}

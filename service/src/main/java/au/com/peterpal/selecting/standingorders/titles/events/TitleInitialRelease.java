package au.com.peterpal.selecting.standingorders.titles.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@SuperBuilder
public class TitleInitialRelease extends DomainEvent {

  private LocalDateTime deferredDate;

  private ReleaseType releaseType;

  public static TitleInitialRelease from(UUID titleId, String username) {
    return TitleInitialRelease.builder()
        .id(titleId)
        .releaseType(ReleaseType.INITIAL)
        .deferredDate(LocalDateTime.now())
        .username(username)
        .build();
  }
}

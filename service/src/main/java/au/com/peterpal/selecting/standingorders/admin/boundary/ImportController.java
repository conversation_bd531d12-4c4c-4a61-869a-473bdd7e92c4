package au.com.peterpal.selecting.standingorders.admin.boundary;

import au.com.peterpal.selecting.standingorders.admin.control.Importer;
import au.com.peterpal.selecting.standingorders.admin.control.ProcessReport;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@Log4j2
@RequiredArgsConstructor
@SecurityRequirement(name = "BearerAuth")
@RequestMapping("/api")
public class ImportController {

  private final Importer importer;

  @RequestMapping(path= "/upload-standing-orders", method = RequestMethod.POST, consumes = "multipart/form-data")
  public ProcessReport uploadStandingOrders(@RequestParam("file") @ApiParam(value="File", required=true) MultipartFile file,
      @RequestParam(value = "test", required = false, defaultValue = "false") boolean doTest) {
    return importer.importStandingOrders(file, doTest);
  }

  @RequestMapping(path= "/upload-preferences", method = RequestMethod.POST, consumes = "multipart/form-data")
  public ProcessReport uploadPreferences(@RequestParam("file") @ApiParam(value="File", required=true) MultipartFile file,
      @RequestParam(value = "test", required = false, defaultValue = "false") boolean doTest) {
    return importer.importAllocationPrefs(file, doTest);
  }

  @RequestMapping(path= "/upload-allocations", method = RequestMethod.POST, consumes = "multipart/form-data")
  public ProcessReport uploadAllocations(@RequestParam("file") MultipartFile file,
      @RequestParam(value = "test", required = false, defaultValue = "false") boolean doTest) {
    return importer.importAllocations(file, doTest);
  }
}

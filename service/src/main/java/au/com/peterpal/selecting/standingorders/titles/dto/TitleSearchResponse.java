package au.com.peterpal.selecting.standingorders.titles.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;

import java.util.List;

@Data
@EqualsAndHashCode
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
public class TitleSearchResponse {
  private Page<TitleSearchDetail> details;
  private List<TitleSearchStatusCount> statusCounts;
}

package au.com.peterpal.selecting.standingorders.allocation.control;

import static java.util.stream.Collectors.groupingBy;

import au.com.peterpal.selecting.standingorders.allocation.commands.AllocationCategorySearchResponse;
import au.com.peterpal.selecting.standingorders.allocation.commands.AllocationSearchResponse;
import au.com.peterpal.selecting.standingorders.allocation.commands.SearchAllocationsByStandingOrder;
import au.com.peterpal.selecting.standingorders.allocation.dto.AllocationDateType;
import au.com.peterpal.selecting.standingorders.allocation.dto.QAllocationCategoryAgg;
import au.com.peterpal.selecting.standingorders.allocation.dto.SearchAllocationRequest;
import au.com.peterpal.selecting.standingorders.allocation.dto.SearchAllocationResponse;
import au.com.peterpal.selecting.standingorders.allocation.model.*;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.QCustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.QBranch;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.category.entity.QCategory;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.QCustomer;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.QFund;
import au.com.peterpal.selecting.standingorders.standingorder.model.QStandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.QTerm;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import java.util.*;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Log4j2
@Repository
public class AllocationRepositoryCustomImpl implements AllocationRepositoryCustom {

  public static final String DESC = "desc";
  @PersistenceContext private final EntityManager entityManager;

  private QStandingOrder standingOrder = QStandingOrder.standingOrder;
  private QCustomerStandingOrder customerStandingOrder =
      QCustomerStandingOrder.customerStandingOrder;
  private QAllocation allocation = QAllocation.allocation;
  private QRelease release = QRelease.release;
  private QCustomer customer = QCustomer.customer;
  private QTerm term = QTerm.term;
  private QCategory category = QCategory.category;
  private QBranch branch = QBranch.branch;
  private QFund fund = QFund.fund;
  private QAllocationCategoryAgg allocationCategoryAgg =
      QAllocationCategoryAgg.allocationCategoryAgg;

  public AllocationRepositoryCustomImpl(EntityManager entityManager) {
    this.entityManager = entityManager;
  }

  @Override
  public Page<Allocation> search(SearchAllocationsByStandingOrder searchRequest) {

    Pageable pageable = searchRequest.getPageRequest();

    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    JPAQuery<Allocation> query =
        factory
            .selectFrom(allocation)
            .innerJoin(allocation.customerStandingOrder, customerStandingOrder)
            .innerJoin(customerStandingOrder.standingOrder, standingOrder)
            .innerJoin(customerStandingOrder.customer, customer)
            .leftJoin(allocation.categories, category)
            .where(buildCondition(searchRequest));
    query.orderBy(customer.code.asc(), category.code.asc());

    return fetchAndCompressCategoriesToOneRow(pageable, query);
  }

  @Override
  public Page<AllocationSearchResponse> search(
      SearchAllocationRequest searchRequest, Pageable pageable) {

    JPAQueryFactory factory = new JPAQueryFactory(entityManager);

    BooleanBuilder condition = getCondition(searchRequest);
    JPAQuery<AllocationSearchResponse> query =
        factory
            .select(
                Projections.bean(
                    AllocationSearchResponse.class,
                    allocation.allocationId,
                    customerStandingOrder.customerStandingOrderId.as("customerStandingOrderId"),
                    customerStandingOrder.as("customerStandingOrder"),
                    customerStandingOrder.customer.customerId.as("customerId"),
                    customerStandingOrder.customer.code.as("customerCode"),
                    customerStandingOrder.customer.name.as("customerName"),
                    allocation.branch.branchId.as("branchId"),
                    allocation.branch.code.as("branchCode"),
                    allocation.branch.name.as("branchName"),
                    allocation.branch.status.as("branchStatus"),
                    allocation.fund.fundId.as("fundId"),
                    allocation.fund.code.as("fundCode"),
                    allocation.fund.name.as("fundName"),
                    allocation.allocationPreference.allocationPreferenceId.as("preferenceId"),
                    allocation.status.as("status"),
                    allocation.customerReference.as("customerReference"),
                    allocation.deliveryInstructions.as("deliveryInstructions"),
                    allocation.notes.as("notes"),
                    customerStandingOrder.standingOrder.standingOrderNumber.as(
                        "standingOrderNumber"),
                    customerStandingOrder.standingOrder.description.as("standingOrderDescription"),
                    customerStandingOrder.standingOrder.standingOrderId.as("standingOrderId"),
                    allocation.collectionCode.as("collectionCode"),
                    allocation.baParent.allocationId.as("baParentId"),
                    release.actionType,
                    release.initialAssignmentRule,
                    release.quantity,
                    release.hardbackQuantity,
                    release.paperbackQuantity))
            .distinct()
            .from(allocation)
            .innerJoin(allocation.releases, release)
            .innerJoin(allocation.customerStandingOrder, customerStandingOrder)
            .innerJoin(customerStandingOrder.standingOrder, standingOrder)
            .innerJoin(customerStandingOrder.customer, customer)
            .leftJoin(allocation.categories, category)
            .leftJoin(allocation.branch, branch)
            .leftJoin(allocation.fund, fund)
            .leftJoin(term)
            .on(term.standingOrder.eq(standingOrder));
    if ("categories".equalsIgnoreCase(searchRequest.getSortByKey())) {
      query = buildQueryForCategoriesSort(factory);
    }
    query = query.where(condition);
    addSort(searchRequest, query);
    long total = query.fetchCount();

    if (Objects.nonNull(pageable)) {
      query.offset(pageable.getOffset()).limit(pageable.getPageSize());
    }

    List<AllocationSearchResponse> allocations = query.fetch();

    List<AllocationId> allocationIds =
        allocations.stream()
            .map(AllocationSearchResponse::getAllocationId)
            .collect(Collectors.toList());
    Map<AllocationId, List<Release>> mapOfReleases = fetchReleases(factory, allocationIds);

    Map<AllocationId, List<AllocationCategorySearchResponse>> mapOfCategories =
        fetchCategories(searchRequest, factory, allocationIds);

    for (AllocationSearchResponse allocation : allocations) {
      List<Release> releaseList = mapOfReleases.get(allocation.getAllocationId());
      allocation.setInitialReleases(
          CollectionUtils.isNotEmpty(releaseList) ? releaseList.get(0) : null);
      List<AllocationCategorySearchResponse> allocationCategories =
          mapOfCategories.get(allocation.getAllocationId());
      allocation.setCategories(
          CollectionUtils.isNotEmpty(allocationCategories)
              ? allocationCategories.stream()
                  .map(AllocationCategorySearchResponse::getCategoryCode)
                  .collect(Collectors.toList())
              : new ArrayList<>());
    }
    return new PageImpl<>(allocations, pageable, total);
  }

  private void addSort(
      SearchAllocationRequest searchRequest, JPAQuery<AllocationSearchResponse> query) {
    if (searchRequest.getSortByKey() != null) {
      if (searchRequest.getSortByKey().equalsIgnoreCase("standingOrderDescription")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(customerStandingOrder.standingOrder.description.desc());
        } else {
          query.orderBy(customerStandingOrder.standingOrder.description.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("standingOrderNumber")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(customerStandingOrder.standingOrder.standingOrderNumber.desc());
        } else {
          query.orderBy(customerStandingOrder.standingOrder.standingOrderNumber.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("fundCode")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(allocation.fund.code.desc());
        } else {
          query.orderBy(allocation.fund.code.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("actionType")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(release.actionType.desc());
        } else {
          query.orderBy(release.actionType.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("initialAssignmentRule")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(release.initialAssignmentRule.desc());
        } else {
          query.orderBy(release.initialAssignmentRule.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("collectionCode")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(allocation.collectionCode.desc());
        } else {
          query.orderBy(allocation.collectionCode.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("customerReference")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(allocation.customerReference.desc());
        } else {
          query.orderBy(allocation.customerReference.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("deliveryInstructions")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(allocation.deliveryInstructions.desc());
        } else {
          query.orderBy(allocation.deliveryInstructions.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("notes")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(allocation.notes.desc());
        } else {
          query.orderBy(allocation.notes.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("quantity")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(release.quantity.desc());
        } else {
          query.orderBy(release.quantity.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("hardbackQuantity")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(release.hardbackQuantity.desc());
        } else {
          query.orderBy(release.hardbackQuantity.asc());
        }
      } else if (searchRequest.getSortByKey().equalsIgnoreCase("paperbackQuantity")) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(release.paperbackQuantity.desc());
        } else {
          query.orderBy(release.paperbackQuantity.asc());
        }
      } else if ("categories".equalsIgnoreCase(searchRequest.getSortByKey())) {
        if (searchRequest.getSortByDirection().equalsIgnoreCase(DESC)) {
          query.orderBy(allocationCategoryAgg.category.desc());
        } else {
          query.orderBy(allocationCategoryAgg.category.asc());
        }
      }
    }
  }

  private JPAQuery<AllocationSearchResponse> buildQueryForCategoriesSort(JPAQueryFactory factory) {
    return factory
        .select(
            Projections.bean(
                AllocationSearchResponse.class,
                allocation.allocationId,
                customerStandingOrder.customerStandingOrderId.as("customerStandingOrderId"),
                customerStandingOrder.as("customerStandingOrder"),
                customerStandingOrder.customer.customerId.as("customerId"),
                customerStandingOrder.customer.code.as("customerCode"),
                customerStandingOrder.customer.name.as("customerName"),
                allocation.branch.branchId.as("branchId"),
                allocation.branch.code.as("branchCode"),
                allocation.branch.name.as("branchName"),
                allocation.branch.status.as("branchStatus"),
                allocation.fund.fundId.as("fundId"),
                allocation.fund.code.as("fundCode"),
                allocation.fund.name.as("fundName"),
                allocation.allocationPreference.allocationPreferenceId.as("preferenceId"),
                allocation.status.as("status"),
                allocation.customerReference.as("customerReference"),
                allocation.deliveryInstructions.as("deliveryInstructions"),
                allocation.notes.as("notes"),
                customerStandingOrder.standingOrder.standingOrderNumber.as("standingOrderNumber"),
                customerStandingOrder.standingOrder.description.as("standingOrderDescription"),
                customerStandingOrder.standingOrder.standingOrderId.as("standingOrderId"),
                allocation.collectionCode.as("collectionCode"),
                allocation.baParent.allocationId.as("baParentId"),
                release.actionType,
                release.initialAssignmentRule,
                release.quantity,
                release.hardbackQuantity,
                release.paperbackQuantity,
                allocationCategoryAgg.category.as("minCategory")))
        .distinct()
        .from(allocation)
        .innerJoin(allocation.releases, release)
        .innerJoin(allocation.customerStandingOrder, customerStandingOrder)
        .innerJoin(customerStandingOrder.standingOrder, standingOrder)
        .innerJoin(customerStandingOrder.customer, customer)
        .leftJoin(allocation.categories, category)
        .leftJoin(allocation.branch, branch)
        .leftJoin(allocation.fund, fund)
        .leftJoin(allocationCategoryAgg)
        .on(allocation.allocationId.eq(allocationCategoryAgg.allocationId))
        .leftJoin(term)
        .on(term.standingOrder.eq(standingOrder));
  }

  private BooleanBuilder getCondition(SearchAllocationRequest searchRequest) {
    BooleanBuilder condition = new BooleanBuilder();

    condition.and(allocation.branch.isNull());
    condition.and(allocation.baParent.isNull());
    condition.and(release.releaseType.eq(ReleaseType.INITIAL));
    if (Objects.nonNull(searchRequest.getStatus())) {
      condition.and(allocation.status.eq(searchRequest.getStatus()));
    }

    if (StringUtils.isNotBlank(searchRequest.getCustomerCode())) {
      condition.and(customer.code.containsIgnoreCase(searchRequest.getCustomerCode()));
    }

    if (CollectionUtils.isNotEmpty(searchRequest.getCategories())) {
      condition.and(category.code.in(searchRequest.getCategories()));
    }

    if (StringUtils.isNotBlank(searchRequest.getFund())) {
      condition.and(allocation.fund.code.containsIgnoreCase(searchRequest.getFund()));
    }

    if (StringUtils.isNotBlank(searchRequest.getCustomerReference())) {
      condition.and(
          allocation.customerReference.containsIgnoreCase(searchRequest.getCustomerReference()));
    }

    if (StringUtils.isNotBlank(searchRequest.getDescription())) {
      condition.and(
          customerStandingOrder.standingOrder.description.containsIgnoreCase(
              searchRequest.getDescription()));
    }

    if (StringUtils.isNotBlank(searchRequest.getTerm())) {
      BooleanBuilder andTermCondition =
          new BooleanBuilder(
              standingOrder.in(
                  JPAExpressions.selectFrom(standingOrder)
                      .innerJoin(term)
                      .on(term.standingOrder.eq(standingOrder))
                      .where(
                          term.value
                              .toLowerCase()
                              .contains(searchRequest.getTerm().toLowerCase())
                              .or(
                                  term.value
                                      .toLowerCase()
                                      .contains(
                                          searchRequest
                                              .getTermWithAddedOrRemovedSpaceAfterComma())))));
      condition.and(andTermCondition);
    }
    if (searchRequest.getDateType() == AllocationDateType.ADDED) {
      if (Objects.nonNull(searchRequest.getStartDate())) {
        condition.and(
            new BooleanBuilder(
                allocation.dateAdded.after(searchRequest.getStartDate().atStartOfDay())));
      }
      if (Objects.nonNull(searchRequest.getEndDate())) {
        condition.and(
            new BooleanBuilder(
                allocation.dateAdded.before(searchRequest.getEndDate().atTime(23, 59, 59))));
      }
    } else if (searchRequest.getDateType() == AllocationDateType.UPDATED) {
      if (Objects.nonNull(searchRequest.getStartDate())) {
        condition.and(
            new BooleanBuilder(
                allocation.dateUpdated.after(searchRequest.getStartDate().atStartOfDay())));
      }
      if (Objects.nonNull(searchRequest.getEndDate())) {
        condition.and(
            new BooleanBuilder(
                allocation.dateUpdated.before(searchRequest.getEndDate().atTime(23, 59, 59))));
      }
    }
    if (StringUtils.isNotBlank(searchRequest.getKeyword())
        && Objects.nonNull(searchRequest.getKeywordEntity())) {
      switch (searchRequest.getKeywordEntity()) {
        case DESCRIPTION:
          condition.and(
              new BooleanBuilder(
                  standingOrder.description.likeIgnoreCase(likeExp(searchRequest.getKeyword()))));
          break;
        case NOTE:
          condition.and(
              new BooleanBuilder(
                  allocation.notes.likeIgnoreCase(likeExp(searchRequest.getKeyword()))));
          break;
        case CUSTOMER_REFERENCE:
          condition.and(
              new BooleanBuilder(
                  allocation.customerReference.likeIgnoreCase(
                      likeExp(searchRequest.getKeyword()))));
          break;
        case DELIVERY_INSTRUCTION:
          condition.and(
              new BooleanBuilder(
                  allocation.deliveryInstructions.likeIgnoreCase(
                      likeExp(searchRequest.getKeyword()))));
          break;
        case COLLECTION:
          condition.and(
              new BooleanBuilder(
                  allocation.collectionCode.likeIgnoreCase(likeExp(searchRequest.getKeyword()))));
          break;
      }
    }
    return condition;
  }

  private Map<AllocationId, List<AllocationCategorySearchResponse>> fetchCategories(
      SearchAllocationRequest searchRequest,
      JPAQueryFactory factory,
      List<AllocationId> allocationIds) {
    BooleanBuilder filterCategoryCondition = new BooleanBuilder();
    filterCategoryCondition.and(allocation.allocationId.in(allocationIds));
    if (CollectionUtils.isNotEmpty(searchRequest.getCategories())) {
      filterCategoryCondition.and(category.code.in(searchRequest.getCategories()));
    }
    JPAQuery<AllocationCategorySearchResponse> categoryQuery =
        factory
            .select(
                Projections.bean(
                    AllocationCategorySearchResponse.class,
                    allocation.allocationId.as("allocationId"),
                    category.code.as("categoryCode")))
            .from(allocation)
            .innerJoin(allocation.categories, category)
            .where(filterCategoryCondition);
    List<AllocationCategorySearchResponse> categories = categoryQuery.fetch();
    return categories.stream()
        .collect(groupingBy(AllocationCategorySearchResponse::getAllocationId));
  }

  private Map<AllocationId, List<Release>> fetchReleases(
      JPAQueryFactory factory, List<AllocationId> allocationIds) {
    JPAQuery<Release> releaseQuery =
        factory
            .select(release)
            .from(allocation)
            .innerJoin(allocation.releases, release)
            .where(
                allocation
                    .allocationId
                    .in(allocationIds)
                    .and(release.releaseType.eq(ReleaseType.INITIAL)));
    List<Release> releases = releaseQuery.fetch();
    return releases.stream().collect(groupingBy(r -> r.getAllocation().getAllocationId()));
  }

  @Override
  public List<SearchAllocationResponse.CustomerResponse> findCustomersWithAllocation() {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    return factory
        .select(
            Projections.bean(
                SearchAllocationResponse.CustomerResponse.class,
                customer.customerId,
                customer.code))
        .from(allocation)
        .innerJoin(allocation.customerStandingOrder, customerStandingOrder)
        .innerJoin(customerStandingOrder.customer, customer)
        .groupBy(customer.customerId)
        .orderBy(customer.code.asc())
        .fetch();
  }

  @Override
  public List<Allocation> getAllocations(StandingOrderId soId, long limit) {
    JPAQuery<Allocation> query = buildQuery(soId);

    if (limit > 0) {
      query = query.limit(limit);
    }
    return query.fetch();
  }

  private JPAQuery<Allocation> buildQuery(StandingOrderId soId) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    return factory
        .selectFrom(allocation)
        .innerJoin(allocation.customerStandingOrder, customerStandingOrder)
        .innerJoin(customerStandingOrder.standingOrder, standingOrder)
        .where(
            customerStandingOrder
                .customerStandingOrderStatus
                .eq(StandingOrderStatus.ACTIVE)
                .and(allocation.branch.isNull())
                .and(
                    allocation
                        .status
                        .eq(AllocationStatus.ACTIVE)
                        .or(allocation.status.eq(AllocationStatus.PAUSED)))
                .and(standingOrder.standingOrderId.eq(soId)));
  }

  @Override
  public List<Allocation> getAllocations(StandingOrderId soId) {
    return getAllocations(soId, -1);
  }

  @Override
  public List<Allocation> findAllPausedAllocations(List<StandingOrderId> standingOrderIds) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    return factory
        .selectFrom(allocation)
        .innerJoin(allocation.customerStandingOrder, customerStandingOrder)
        .innerJoin(customerStandingOrder.standingOrder, standingOrder)
        .where(
            customerStandingOrder
                .customerStandingOrderStatus
                .eq(StandingOrderStatus.ACTIVE)
                .and(allocation.branch.isNull())
                .and(allocation.status.eq(AllocationStatus.PAUSED))
                .and(standingOrder.standingOrderId.in(standingOrderIds)))
        .fetch();
  }

  @Override
  public List<Allocation> getAllocationsByStandingOrderIds(List<StandingOrderId> standingOrderIds) {
    JPAQueryFactory factory = new JPAQueryFactory(entityManager);
    return factory
        .selectFrom(allocation)
        .innerJoin(allocation.customerStandingOrder, customerStandingOrder)
        .innerJoin(customerStandingOrder.standingOrder, standingOrder)
        .where(
            customerStandingOrder
                .customerStandingOrderStatus
                .eq(StandingOrderStatus.ACTIVE)
                .and(allocation.branch.isNull())
                .and(standingOrder.standingOrderId.in(standingOrderIds)))
        .fetch();
  }

  @Override
  public List<Allocation> searchAllocation(
      SearchAllocationRequest searchRequest, List<AllocationId> excludedAllocationIds) {

    JPAQueryFactory factory = new JPAQueryFactory(entityManager);

    BooleanBuilder condition = getCondition(searchRequest);
    if (CollectionUtils.isNotEmpty(excludedAllocationIds)) {
      condition.and(allocation.allocationId.notIn(excludedAllocationIds));
    }
    JPAQuery<Allocation> query =
        factory
            .selectFrom(allocation)
            .distinct()
            .innerJoin(allocation.releases, release)
            .innerJoin(allocation.customerStandingOrder, customerStandingOrder)
            .innerJoin(customerStandingOrder.standingOrder, standingOrder)
            .innerJoin(customerStandingOrder.customer, customer)
            .leftJoin(allocation.categories, category)
            .leftJoin(allocation.branch, branch)
            .leftJoin(allocation.fund, fund)
            .leftJoin(term)
            .on(term.standingOrder.eq(standingOrder));
    query = query.where(condition);

    return query.fetch();
  }

  private static PageImpl<Allocation> fetchAndCompressCategoriesToOneRow(
      Pageable pageable, JPAQuery<Allocation> query) {

    long total = query.fetchCount();

    if (Objects.nonNull(pageable)) {
      query.offset(pageable.getOffset()).limit(pageable.getPageSize());
    }

    List<Allocation> allocations = query.fetch();

    Map<AllocationId, Set<Category>> allocationCategoriesMap = new HashMap<>();

    allocations.forEach(
        e ->
            allocationCategoriesMap
                .computeIfAbsent(e.getAllocationId(), k -> new HashSet<>())
                .addAll(e.getCategories()));

    allocations.forEach(
        e -> e.setCategories(new ArrayList<>(allocationCategoriesMap.get(e.getAllocationId()))));

    List<Allocation> allocationsDeduplicated = new ArrayList<>(new LinkedHashSet<>(allocations));

    return new PageImpl<>(allocationsDeduplicated, pageable, total);
  }

  private Predicate buildCondition(SearchAllocationsByStandingOrder searchRequest) {
    BooleanBuilder condition = new BooleanBuilder();

    condition.and(standingOrder.standingOrderId.eq(searchRequest.getStandingOrderId()));

    if (Objects.nonNull(searchRequest.getCustomerCode())) {
      condition.and(customer.code.eq(searchRequest.getCustomerCode()));
    }

    if (Objects.nonNull(searchRequest.getAllocationStatus())) {
      condition.and(allocation.status.eq(searchRequest.getAllocationStatus()));
    }

    if (CollectionUtils.isNotEmpty(searchRequest.getCategories())) {
      condition.and(category.code.in(searchRequest.getCategories()));
    }

    condition.and(allocation.branch.isNull());
    return condition;
  }

  private StringExpression likeExp(String text) {
    return Expressions.asString("%").concat(text).concat("%");
  }
}

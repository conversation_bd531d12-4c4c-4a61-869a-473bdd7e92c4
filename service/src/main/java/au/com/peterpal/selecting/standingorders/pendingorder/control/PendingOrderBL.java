package au.com.peterpal.selecting.standingorders.pendingorder.control;

import static java.util.Objects.*;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.summingInt;

import au.com.peterpal.common.audit.EventPublisher;
import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.selecting.standingorders.allocation.control.AllocationRepository;
import au.com.peterpal.selecting.standingorders.allocation.model.*;
import au.com.peterpal.selecting.standingorders.allocationpreference.control.AllocationPreferenceRepository;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreference;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceStatus;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundService;
import au.com.peterpal.selecting.standingorders.ext.supplier.control.SupplierRepository;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.Supplier;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.CwProductInfo;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.*;
import au.com.peterpal.selecting.standingorders.pendingorder.events.*;
import au.com.peterpal.selecting.standingorders.pendingorder.model.BranchDistribution;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import au.com.peterpal.selecting.standingorders.standingorder.control.CategoryService;
import au.com.peterpal.selecting.standingorders.standingorder.control.ProductSearchService;
import au.com.peterpal.selecting.standingorders.standingorder.dto.ProductInfo;
import au.com.peterpal.selecting.standingorders.titles.control.TitleService;
import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleStatus;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Log4j2
@Transactional
@Component
@RequiredArgsConstructor
public class PendingOrderBL {

  private final PendingOrderService pendingOrderService;
  private final SubmitPendingOrderService submitPendingOrderService;
  private final TitleService titleService;
  private final SupplierRepository supplierRepository;
  private final EventPublisher eventPublisher;
  private final MessageChannel pendingOrderSubmittedIntegrationChannel;
  private final FundService fundService;
  private final AllocationPreferenceRepository allocPrefRepo;
  private final ProductSearchService productSearchService;

  private final POCreatorFactory poCreatorFactory;
  private final CategoryService categoryService;
  private final AllocationRepository allocationRepository;

  public List<PendingOrder> create(Allocation allocation, Title title, List<ProductInfo> products) {
    Optional.ofNullable(allocation)
        .orElseThrow(() -> new IllegalArgumentException("Allocation must not be null"));
    Optional.ofNullable(title)
        .orElseThrow(() -> new IllegalArgumentException("Title must not be null"));
    Optional.ofNullable(products)
        .orElseThrow(() -> new IllegalArgumentException("Products must not be null"));
    return createInternal(allocation, title, products);
  }

  private List<PendingOrder> createInternal(
      Allocation allocation, Title title, List<ProductInfo> products) {
    products.forEach(
        p -> {
          if (!org.springframework.util.StringUtils.hasText(p.getIsbn())) {
            throw new IllegalArgumentException("ISBN must not be null");
          }
        });

    Release release = allocation.getRelease(ReleaseType.INITIAL);
    if (ActionType.IGNORE.equals(release.getActionType())) {
      log.warn(
          () ->
              String.format(
                  "Release %s for allocation %s has action type of IGNORE. No PO will be created.",
                  release.getReleaseId(), allocation.getAllocationId()));
      return Collections.EMPTY_LIST;
    }

    List<String> categoryCodes =
        Optional.ofNullable(title.getCategory())
            .map(c -> Lists.newArrayList(c.getCode()))
            .orElse(Lists.newArrayList());

    List<AllocationPreference> allocationPreferences =
        allocPrefRepo.findByCustomer_CodeAndStatusAndCategories_CodeIn(
            allocation.getCustomerStandingOrder().getCustomer().getCode(),
            AllocationPreferenceStatus.ACTIVE,
            categoryCodes);

    AllocationPreference preference =
        allocationPreferences.isEmpty() ? null : allocationPreferences.get(0);

    Optional<POCreator> poCreator = poCreatorFactory.getCreator(release.getInitialAssignmentRule());

    List<Allocation> branchAllocations =
        allocationRepository.findAllByBaParentAllocationIdAndStatus(
            allocation.getAllocationId(), AllocationStatus.ACTIVE);

    List<PendingOrder> pendingOrders = Lists.newArrayList();
    if (poCreator.isPresent()) {
      pendingOrders =
          poCreator
              .get()
              .create(
                  POCreateInfo.builder()
                      .title(title)
                      .products(products)
                      .release(release)
                      .allocation(allocation)
                      .pref(preference)
                      .branchAllocations(branchAllocations)
                      .build());
    }

    pendingOrders =
        pendingOrders.stream()
            .filter(
                po ->
                    // ignore creating PO if duplicate
                    pendingOrderService.noDuplicationByTitleFormatAndAllocation(
                        title.getTitleId(), po.getFormat(), allocation.getAllocationId()))
            .collect(Collectors.toList());

    createPendingOrders(pendingOrders);

    return pendingOrders;
  }

  public List<PendingOrder> createPendingOrders(List<PendingOrder> pendingOrders) {
    pendingOrders.forEach(
        pendingOrder -> {
          pendingOrder = pendingOrderService.save(pendingOrder);
          eventPublisher.publishEvent(PendingOrderCreated.from(pendingOrder, null));
        });

    return pendingOrders;
  }

  public UUID handle(UpdatePendingOrderRequest request, String username) {

    PendingOrder pendingOrder = pendingOrderService.findById(request.getPendingOrderId());
    if (!pendingOrder.isNew()) {
      throw new BusinessException(
          String.format(
              "Can not update Pending order %s already been submitted or processed.",
              pendingOrder.getPendingOrderId()));
    }

    // validate fund must exist in db if it appears in the request
    if (nonNull(request.getFundId())) {
      fundService.getFund(request.getFundId().getId(), true);
    }

    if (StringUtils.isNotBlank(request.getCategory())) {
      Category category = categoryService.getCategoryByCode(request.getCategory());
      pendingOrder = pendingOrder.withCategory(category);
    }

    pendingOrder =
        pendingOrder
            .withCustomerReference(
                StringUtils.defaultString(
                    request.getCustomerReference(), pendingOrder.getCustomerReference()))
            .withDeliveryInstructions(
                StringUtils.defaultString(
                    request.getDeliveryInstructions(), pendingOrder.getDeliveryInstructions()))
            .withCollectionCode(
                StringUtils.defaultString(
                    request.getCollectionCode(), pendingOrder.getCollectionCode()))
            .withNotes(StringUtils.defaultString(request.getNotes(), pendingOrder.getNotes()))
            .withQuantity(
                Optional.ofNullable(request.getQuantity()).orElse(pendingOrder.getQuantity()))
            .withFund(
                Optional.ofNullable(request.getFundId())
                    .map(fundId -> fundService.getFund(fundId.getId()))
                    .orElse(pendingOrder.getFund()))
            .withOrderStatus(request.getStatus());

    pendingOrderService.save(pendingOrder);

    PendingOrderUpdated pendingOrderUpdated = PendingOrderUpdated.from(pendingOrder, username);
    eventPublisher.publishEvent(pendingOrderUpdated);
    return pendingOrderUpdated.getId();
  }

  public SubmitPendingOrderResponse submitPendingOrders(
      SubmitPendingOrderRequest request, String username) {
    SubmitPendingOrderResult submitPendingOrderResult = null;
    try {
      submitPendingOrderResult = submitPendingOrderService.submitPendingOrders(request, username);
    } catch (DataIntegrityViolationException e) {
      log.info(e.getMessage(), e);
      throw new BusinessException("The pending order is processed.");
    }

    List<PendingOrder> acceptAndSubmitPendingOrders =
        submitPendingOrderResult.getAcceptAndSubmitPendingOrders();
    if (CollectionUtils.isNotEmpty(acceptAndSubmitPendingOrders)) {
      acceptAndSubmitPendingOrders.forEach(
          pendingOrder ->
              eventPublisher.publishEvent(
                  PendingOrderSubmitted.from(pendingOrder.getPendingOrderId(), username)));

      //     send JMS message
      pendingOrderSubmittedIntegrationChannel.send(
          MessageBuilder.withPayload(
                  acceptAndSubmitPendingOrders.stream()
                      .map(PendingOrder::getPendingOrderId)
                      .collect(Collectors.toList()))
              .build());
    }
    List<PendingOrder> cancelPendingOrders = submitPendingOrderResult.getCancelPendingOrders();
    cancelPendingOrders.forEach(
        pendingOrder ->
            eventPublisher.publishEvent(
                PendingOrderCancelled.from(pendingOrder.getPendingOrderId(), username)));
    SubmitPendingOrderResponse response =
        SubmitPendingOrderResponse.of(
            acceptAndSubmitPendingOrders.size(), cancelPendingOrders.size());
    log.debug("Submitted pending orders under title {}, result {}", request.getTitleId(), response);
    return response;
  }

  public List<PendingOrder> cancelPendingOrders(
      List<PendingOrderId> pendingOrderIds, String username) {
    List<PendingOrder> pendingOrders = pendingOrderService.findByIds(pendingOrderIds);

    if (pendingOrders.stream().anyMatch(po -> po.isInvalid() || po.isSubmittedOrProcessed())) {
      throw new BusinessException(
          "Selection can't contains Submitted, Processed or Invalid Pending Orders");
    }

    List<PendingOrder> cancelledPendingOrders =
        pendingOrders.stream()
            .map(
                pendingOrder -> {
                  pendingOrder.setOrderStatus(PendingOrderStatus.CANCELLED);
                  return pendingOrder;
                })
            .collect(Collectors.toList());
    cancelledPendingOrders = pendingOrderService.saveAll(cancelledPendingOrders);

    cancelledPendingOrders.forEach(
        pendingOrder ->
            eventPublisher.publishEvent(
                PendingOrderCancelled.from(pendingOrder.getPendingOrderId(), username)));

    return cancelledPendingOrders;
  }

  public List<PendingOrder> invalidatePendingOrders(
      List<PendingOrderId> pendingOrderIds, String username) {
    List<PendingOrder> invalidatedPendingOrders =
        pendingOrderIds.stream().map(pendingOrderService::findById).collect(Collectors.toList());
    if (!invalidatedPendingOrders.stream().allMatch(PendingOrder::isNew)) {
      throw new BusinessException("Selection contains Pending Order with non-NEW status");
    }

    invalidatedPendingOrders =
        invalidatedPendingOrders.stream()
            .map(
                pendingOrder -> {
                  pendingOrder.setOrderStatus(PendingOrderStatus.INVALID);
                  return pendingOrder;
                })
            .collect(Collectors.toList());
    invalidatedPendingOrders = pendingOrderService.saveAll(invalidatedPendingOrders);

    invalidatedPendingOrders.forEach(
        pendingOrder ->
            eventPublisher.publishEvent(
                PendingOrderInvalidated.from(pendingOrder.getPendingOrderId(), username)));

    return invalidatedPendingOrders;
  }

  public void deletePendingOrders(List<PendingOrder> pendingOrders) {
    List<PendingOrderDeleted> pendingOrderDeletedEvents =
        pendingOrders.stream()
            .map(po -> PendingOrderDeleted.from(po.getPendingOrderId(), "system"))
            .collect(Collectors.toList());

    pendingOrderService.deletePendingOrders(pendingOrders);
    pendingOrderDeletedEvents.forEach(eventPublisher::publishEvent);
  }

  public void handle(UpdateProductDetailRequest request, String username) {
    Supplier supplier =
        Optional.ofNullable(request.getSupplierId())
            .map(sid -> supplierRepository.getOne(request.getSupplierId()))
            .orElse(null);

      List<PendingOrder> pendingOrders =
        pendingOrderService
            .findByOrderedProductReferenceAndTitleTitleId(request.getIsbn(), request.getTitleId())
            .stream()
            .filter(po -> !po.isSubmittedOrProcessed() && !po.isInvalid())
            .peek(
                p -> {
                  if (nonNull(supplier)) {
                    p.setSupplier(supplier);
                  }
                  if (nonNull(request.getCurrencyCode())) {
                    p.setCurrencyCode(request.getCurrencyCode());
                  }
                  if (nonNull(request.getPrice())) {
                    p.setPrice(request.getPrice());
                  }
                  if (nonNull(request.getPublicationDate())) {
                    p.setPublicationDate(request.getPublicationDate());
                  }
                })
            .collect(Collectors.toList());
    pendingOrders = pendingOrderService.saveAll(pendingOrders);

    pendingOrders.forEach(
        pendingOrder ->
            eventPublisher.publishEvent(PendingOrderUpdated.from(pendingOrder, username)));
  }

  public PendingOrderId addOrderNumber(PendingOrderId pendingOrderId, String orderNumber) {
    log.debug("update pending order {} with order number {}", pendingOrderId, orderNumber);
    PendingOrder pendingOrder = pendingOrderService.findById(pendingOrderId);
    pendingOrder.setOrderStatus(PendingOrderStatus.PROCESSED);
    pendingOrder.setOrderNumber(orderNumber);
    pendingOrder = pendingOrderService.save(pendingOrder);

    titleService.markTitleAsProcessed(pendingOrder.getTitle().getTitleId());

    return pendingOrderId;
  }

  TitleStatus reEvaluateTitleStatus(Title title) {
    TitleStatus titleStatus;
    List<PendingOrder> pendingOrderList = pendingOrderService.findByTitle(title.getTitleId());
    if (pendingOrderList.stream().allMatch(PendingOrder::isProcessedInvalidCancelled)) {
      titleStatus = TitleStatus.PROCESSED;
    } else {
      titleStatus = title.getTitleStatus();
    }

    return titleStatus;
  }

  public Page<PendingOrderResponse> handle(PendingOrderRequest request, Pageable pageRequest) {
    Affirm.of(request).notNull("PendingOrderRequest must not be null");
    return pendingOrderService.searchAllByCustomerId(request.getCustomerId(), pageRequest);
  }

  public List<PendingOrderResponse> handle(PendingOrderByTitleRequest request) {
    return pendingOrderService.searchAllByTitle(
        request.getTitleId(), request.getStatus(), request.getCategory());
  }

  public List<ProductDetailDto> findProductsByTitle(TitleId titleId) {
    List<ProcessFormatResponse> processFormatResponses =
        pendingOrderService.findByTitleGroupByFormat(titleId);
    return processFormatResponses.stream()
        .map(
            p -> {
              Optional<CwProductInfo> productInfo =
                  productSearchService.searchProductByIsbn(p.getIsbn());
              return new ProductDetailDto(
                  p.getIsbn(),
                  p.getFormat(),
                  productInfo.map(CwProductInfo::getEditionStatement).orElse(null),
                  productInfo
                      .map(CwProductInfo::getPublishers)
                      .map(
                          publisherInfos ->
                              CollectionUtils.isEmpty(publisherInfos)
                                  ? StringUtils.EMPTY
                                  : publisherInfos.get(0).getPublisherName())
                      .orElse(null),
                  nonNull(p.getPublicationDate())
                      ? p.getPublicationDate()
                      : productInfo.map(CwProductInfo::getPublicationDate).orElse(null),
                  p.getPrice(),
                  p.getCurrencyCode(),
                  nonNull(p.getSupplier())
                      ? new ProductDetailDto.Supplier(
                          p.getSupplier().getName(), p.getSupplier().getId())
                      : null);
            })
        .collect(Collectors.toList());
  }

  @Transactional
  public List<PendingOrderId> bulkUpdate(BulkUpdatePendingOrderRequest request, String username) {
    for (PendingOrderId pendingOrderId : request.getPendingOrderId()) {

      PendingOrder pendingOrder = pendingOrderService.findById(pendingOrderId);
      if (!pendingOrder.isNew()) {
        throw new BusinessException(
            String.format(
                "Can not update Pending order %s already been submitted or processed.",
                pendingOrder.getPendingOrderId()));
      }

      if (nonNull(request.getFundId())) {
        fundService.getFund(request.getFundId().getId(), true);
      }

      if (StringUtils.isNotBlank(request.getCategory())) {
        Category category = categoryService.getCategoryByCode(request.getCategory());
        pendingOrder = pendingOrder.withCategory(category);
      }

      pendingOrder =
          pendingOrder
              .withCustomerReference(
                  StringUtils.defaultString(
                      request.getCustomerReference(), pendingOrder.getCustomerReference()))
              .withDeliveryInstructions(
                  StringUtils.defaultString(
                      request.getDeliveryInstructions(), pendingOrder.getDeliveryInstructions()))
              .withCollectionCode(
                  StringUtils.defaultString(
                      request.getCollectionCode(), pendingOrder.getCollectionCode()))
              .withNotes(StringUtils.defaultString(request.getNotes(), pendingOrder.getNotes()))
              .withQuantity(
                  Optional.ofNullable(request.getQuantity()).orElse(pendingOrder.getQuantity()))
              .withFund(
                  Optional.ofNullable(request.getFundId())
                      .map(fundId -> fundService.getFund(fundId.getId()))
                      .orElse(pendingOrder.getFund()));

      pendingOrderService.save(pendingOrder);

      PendingOrderUpdated pendingOrderUpdated = PendingOrderUpdated.from(pendingOrder, username);
      eventPublisher.publishEvent(pendingOrderUpdated);
    }
    return request.getPendingOrderId();
  }

  public List<PendingOrder> updatePendingOrder(Title title, Allocation allocation) {
    List<PendingOrder> pendingOrders =
        findAllNewPendingOrderByTitleIdAllocationId(
            title.getTitleId(), allocation.getAllocationId());
    if (CollectionUtils.isNotEmpty(pendingOrders)) {
      pendingOrderService.deletePendingOrders(pendingOrders);
    }
    return create(allocation, title, title.getProductInfoFromAcceptedProducts());
  }

  public List<PendingOrder> copyOrCreateAdditionalPendingOrder(
      Title destinationTitle, List<TitleId> sourceTitleIds, Allocation allocation) {
    Optional<POCreator> poCreator = getPoCreator(allocation);
    if (poCreator.isPresent()) {
      // clean up destination title
      pendingOrderService.deletePendingOrders(
          findAllNewPendingOrderByTitleIdAllocationId(
              destinationTitle.getTitleId(), allocation.getAllocationId()));

      CopyPendingOrderDto copyPendingOrderDto =
          findPendingOrderAggregation(destinationTitle, sourceTitleIds, allocation);
      List<PendingOrder> pendingOrders;
      if (MapUtils.isNotEmpty(copyPendingOrderDto.getPendingOrderDetailMap())) {
        List<PendingOrder> copiedPendingOrders = poCreator.get().copy(copyPendingOrderDto);
        pendingOrders = createPendingOrders(copiedPendingOrders);
      } else {
        pendingOrders =
            create(
                allocation,
                destinationTitle,
                destinationTitle.getProductInfoFromAcceptedProducts());

        pendingOrders.forEach(
            po -> {
              CopyPendingOrderDto.PendingOrderDetail copyDetail =
                  copyPendingOrderDto.getPendingOrderDetailMap().get(po.getFormat());
              if (Objects.nonNull(copyDetail)) {
                handle(
                    UpdateProductDetailRequest.of(
                        po.getTitle().getTitleId(),
                        po.getOrderedProductReference(),
                        Optional.ofNullable(copyDetail.getSupplier())
                            .map(Supplier::getSupplierId)
                            .orElse(null),
                        copyDetail.getPrice(),
                        copyDetail.getCurrencyCode(),
                        copyDetail.getPublicationDate()),
                    "system");
              }
            });
      }

      return pendingOrders;
    }

    return Lists.newArrayList();
  }

  public List<PendingOrder> findAllNewPendingOrderByTitleIdAllocationId(
      TitleId titleId, AllocationId allocationId) {
    return pendingOrderService.findAllNewPendingOrderByTitleIdAllocationId(titleId, allocationId);
  }

  public List<PendingOrder> findAllPendingOrderByTitleIdAllocationId(
      TitleId titleId, AllocationId allocationId) {
    return pendingOrderService.findAllPendingOrderByTitleIdAllocationId(titleId, allocationId);
  }

  private CopyPendingOrderDto findPendingOrderAggregation(
      Title destinationTitle, List<TitleId> sourceTitleIds, Allocation allocation) {

    Map<ReleaseFormat, CopyPendingOrderDto.PendingOrderDetail> pendingOrderDetailMap =
        pendingOrderService
            .findAllByTitleIdsAllocationId(sourceTitleIds, allocation.getAllocationId())
            .stream()
            .collect(groupingBy(PendingOrder::getFormat))
            .entrySet()
            .stream()
            .map(
                entry -> {
                  List<PendingOrder> pendingOrders = entry.getValue();
                  return CopyPendingOrderDto.PendingOrderDetail.builder()
                      .isbn(extract(pendingOrders, PendingOrder::getOrderedProductReference))
                      .fund(extract(pendingOrders, PendingOrder::getFund))
                      .format(entry.getKey())
                      .deliveryInstruction(
                          extract(pendingOrders, PendingOrder::getDeliveryInstructions))
                      .notes(extract(pendingOrders, PendingOrder::getNotes))
                      .price(extract(pendingOrders, PendingOrder::getPrice))
                      .publicationDate(extract(pendingOrders, PendingOrder::getPublicationDate))
                      .currencyCode(extract(pendingOrders, PendingOrder::getCurrencyCode))
                      .supplier(extract(pendingOrders, PendingOrder::getSupplier))
                      .customerReference(extract(pendingOrders, PendingOrder::getCustomerReference))
                      .collectionCode(extract(pendingOrders, PendingOrder::getCollectionCode))
                      .category(extract(pendingOrders, PendingOrder::getCategory))
                      .quantityTaken(
                          pendingOrders.stream()
                              .map(PendingOrder::getQuantity)
                              .mapToInt(Integer::valueOf)
                              .sum())
                      .brantchQtyTakenMap(
                          pendingOrders.stream()
                              .map(PendingOrder::getBranchDistributions)
                              .flatMap(Collection::stream)
                              .collect(
                                  groupingBy(
                                      BranchDistribution::getAllocationId,
                                      summingInt(BranchDistribution::getQuantity))))
                      .build();
                })
            .collect(
                Collectors.toMap(
                    CopyPendingOrderDto.PendingOrderDetail::getFormat, Function.identity()));

    return CopyPendingOrderDto.builder()
        .title(destinationTitle)
        .allocation(allocation)
        .branchAllocations(
            allocationRepository.findAllByBaParentAllocationIdAndStatus(
                allocation.getAllocationId(), AllocationStatus.ACTIVE))
        .pendingOrderDetailMap(pendingOrderDetailMap)
        .build();
  }

  private <T> T extract(List<PendingOrder> pendingOrders, Function<PendingOrder, T> func) {
    return pendingOrders.stream().map(func).filter(Objects::nonNull).findAny().orElse(null);
  }

  private Optional<POCreator> getPoCreator(Allocation allocation) {
    return poCreatorFactory.getCreator(
        allocation.getRelease(ReleaseType.INITIAL).getInitialAssignmentRule());
  }
}

package au.com.peterpal.selecting.standingorders.config;

import au.com.peterpal.common.security.config.SecurityAdapter;
import au.com.peterpal.common.security.core.util.FeignClientKeycloakInterceptor;
import feign.RequestInterceptor;
import lombok.extern.log4j.Log4j2;
import org.keycloak.adapters.springsecurity.KeycloakConfiguration;
import org.keycloak.adapters.springsecurity.filter.KeycloakAuthenticationProcessingFilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;

@Log4j2
@KeycloakConfiguration
public class SecurityConfig extends SecurityAdapter {

  private static final String ROLE_USER = "User";

  @Value("${security.enabled:true}")
  private boolean securityEnabled;

  @Override
  public void configure(HttpSecurity http) throws Exception {
    super.configure(http);

    http.authorizeRequests()
        .antMatchers("/api/**")
        .hasRole(ROLE_USER)
        .anyRequest()
        .authenticated();
  }

  @Override
  public void configure(WebSecurity web) throws Exception {
    super.configure(web);

    if (!securityEnabled) web.ignoring().antMatchers("/**");
  }

  @Bean
  public FilterRegistrationBean filterRegistration(KeycloakAuthenticationProcessingFilter filter) {
    FilterRegistrationBean registration = new FilterRegistrationBean(filter);
    registration.setEnabled(securityEnabled);
    return registration;
  }

  @Bean
  public RequestInterceptor requestInterceptor() {
    return new FeignClientKeycloakInterceptor();
  }
}

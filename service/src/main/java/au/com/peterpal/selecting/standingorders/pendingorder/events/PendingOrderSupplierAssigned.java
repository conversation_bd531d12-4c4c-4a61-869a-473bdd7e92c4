package au.com.peterpal.selecting.standingorders.pendingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.ext.supplier.entity.SupplierId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;

@Getter
@SuperBuilder
public class PendingOrderSupplierAssigned extends DomainEvent {

  @NotNull @NonNull private SupplierId supplierId;

  public static PendingOrderSupplierAssigned from(
      PendingOrderId pendingOrderId, SupplierId supplierId, String username) {
    return PendingOrderSupplierAssigned.builder()
        .id(pendingOrderId.getId())
        .supplierId(supplierId)
        .username(username)
        .build();
  }
}

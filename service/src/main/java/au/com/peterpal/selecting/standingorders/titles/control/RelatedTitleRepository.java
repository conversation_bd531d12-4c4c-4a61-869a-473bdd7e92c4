package au.com.peterpal.selecting.standingorders.titles.control;

import au.com.peterpal.selecting.standingorders.titles.entity.*;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface RelatedTitleRepository extends JpaRepository<RelatedTitle, RelatedTitleId> {
    Optional<RelatedTitle> findByRelatedTitleTitleId(TitleId titleId);
    List<RelatedTitle> findAllByOriginalTitleTitleId(TitleId originalTitleId);
}

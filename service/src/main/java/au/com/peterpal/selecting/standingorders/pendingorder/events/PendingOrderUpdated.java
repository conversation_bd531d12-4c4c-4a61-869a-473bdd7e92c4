package au.com.peterpal.selecting.standingorders.pendingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.common.utils.StringAffirm;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

@Getter
@SuperBuilder
public class PendingOrderUpdated extends DomainEvent {

  @NotNull private PendingOrderId pendingOrderId;

  private String customerReference;

  private String category;

  private String deliveryInstructions;

  private String collectionCode;

  private String notes;

  private FundId fundId;

  private Integer quantity;

  private BigDecimal price;

  private LocalDate publicationDate;

  private PendingOrderStatus status;

  public static PendingOrderUpdated from(PendingOrder request, String username) {
    Optional<PendingOrderUpdated> opPoc =
        Optional.ofNullable(request)
            .map(
                po -> {
                  Fund fund = request.getFund();
                  Category category = request.getCategory();
                  return PendingOrderUpdated.builder()
                      .id(request.getPendingOrderId().getId())
                      .username(
                          StringAffirm.of(username).hasText() ? username : "standing-order-service")
                      .pendingOrderId(request.getPendingOrderId())
                      .customerReference(request.getCustomerReference())
                      .fundId(fund != null ? fund.getFundId() : null)
                      .quantity(request.getQuantity())
                      .price(request.getPrice())
                      .publicationDate(request.getPublicationDate())
                      .category(category != null ? category.getCode() : null)
                      .collectionCode(request.getCollectionCode())
                      .deliveryInstructions(request.getDeliveryInstructions())
                      .notes(request.getNotes())
                      .status(request.getOrderStatus())
                      .build();
                });
    return opPoc.orElseThrow(
        () -> new IllegalArgumentException("Can not create PendingOrderUpdated event from null"));
  }
}

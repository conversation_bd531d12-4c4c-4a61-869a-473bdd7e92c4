//package au.com.peterpal.selecting.standingorders.standingorder.model;
//
//import com.fasterxml.jackson.annotation.JsonInclude;
//import lombok.*;
//
//import javax.persistence.*;
//import java.math.BigDecimal;
//import java.time.LocalDate;
//
//@With
//@Data
//@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
//@AllArgsConstructor(access = AccessLevel.PRIVATE)
//@Builder(toBuilder = true)
//@EqualsAndHashCode(onlyExplicitlyIncluded = true)
//@ToString(onlyExplicitlyIncluded = true)
//@JsonInclude(JsonInclude.Include.NON_NULL)
//@Entity
//@Table(name = "ad_hoc_product")
//public class AdHocProduct {
//  @EqualsAndHashCode.Include @NonNull @EmbeddedId private AdHocProductId adHocProductId;
//  private String category;
//  private String titleWithoutPrefix;
//  private String subtitle;
//  private String series;
//  private String isbn;
//  private String personName;
//  private String corporateName;
//  private String imprint;
//  private LocalDate publicationDate;
//  private BigDecimal price;
//
//  @ManyToOne
//  @JoinColumn(name = "titleId", nullable = false)
//  private Title title;
//
//  public static AdHocProduct fromTitle(Title title) {
//    return AdHocProduct.builder()
//        .adHocProductId(new AdHocProductId())
//        .category(title.getCategory())
//        .titleWithoutPrefix(title.getTitle())
//        .subtitle(title.getSubtitle())
//        .series(title.getSeries())
//        .isbn(title.getIsbn())
//        .personName(title.getPersonName())
//        .corporateName(title.getCorporateName())
//        .imprint(title.getImprint())
//        .publicationDate(title.getPublicationDate())
//        .title(title)
//        .build();
//  }
//}

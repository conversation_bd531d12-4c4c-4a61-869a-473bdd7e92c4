package au.com.peterpal.selecting.standingorders.utils;

public class StringHelper {
  private String str;

  public static StringHelper of(String str) {
    return new StringHelper(str);
  }

  private StringHelper(String str) {
    this.str = str;
  }

  public boolean hasText() {
    return (str != null && !str.isEmpty() && containsText(str));
  }

  public String trim() {
    return str != null ? str.trim() : null;
  }

  private static boolean containsText(CharSequence str) {
    int strLen = str.length();
    for (int i = 0; i < strLen; i++) {
      if (!Character.isWhitespace(str.charAt(i))) {
        return true;
      }
    }
    return false;
  }
}

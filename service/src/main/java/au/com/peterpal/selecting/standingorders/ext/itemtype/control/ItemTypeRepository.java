package au.com.peterpal.selecting.standingorders.ext.itemtype.control;

import au.com.peterpal.selecting.standingorders.ext.itemtype.model.ItemType;
import au.com.peterpal.selecting.standingorders.ext.itemtype.model.ItemTypeId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ItemTypeRepository extends JpaRepository<ItemType, ItemTypeId> {
  Optional<ItemType> findByCode(String code);
}

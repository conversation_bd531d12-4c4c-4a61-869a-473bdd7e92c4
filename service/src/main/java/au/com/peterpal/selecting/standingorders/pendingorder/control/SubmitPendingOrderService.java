package au.com.peterpal.selecting.standingorders.pendingorder.control;

import au.com.peterpal.common.rest.validation.BusinessException;
import au.com.peterpal.selecting.standingorders.ext.supplier.boundary.dto.SupplierStatus;
import au.com.peterpal.selecting.standingorders.lock.control.PendingOrderSubmittedRepository;
import au.com.peterpal.selecting.standingorders.lock.entity.PendingOrderSubmitted;
import au.com.peterpal.selecting.standingorders.lock.entity.PendingOrderSubmittedId;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.SubmitPendingOrderRequest;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.SubmitPendingOrderResult;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import com.google.common.collect.Lists;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Log4j2
@Component
@RequiredArgsConstructor
public class SubmitPendingOrderService {
  private final PendingOrderService pendingOrderService;
  private final PendingOrderSubmittedRepository pendingOrderSubmittedRepository;

  @Transactional
  public SubmitPendingOrderResult submitPendingOrders(
      SubmitPendingOrderRequest request, String username) {

    List<PendingOrder> cancelledPendingOrders = Lists.newArrayList();
    List<PendingOrder> acceptedPendingOrders = Lists.newArrayList();

    List<PendingOrder> allPendingOrders =
        pendingOrderService.findAllValidPendingOrdersByTitle(request.getTitleId());

    boolean allPendingOrderCovered =
        allPendingOrders.stream()
            .map(PendingOrder::getPendingOrderId)
            .allMatch(
                pid ->
                    request.getAcceptedPendingOrderIds().contains(pid)
                        || request.getCancelledPendingOrderIds().contains(pid));

    if (!allPendingOrderCovered) {
      throw new BusinessException("All pending orders must be updated prior to submitting");
    }

    boolean allCancelled =
        allPendingOrders.stream()
            .map(PendingOrder::getPendingOrderId)
            .allMatch(pid -> request.getCancelledPendingOrderIds().contains(pid));

    if (allCancelled) {
      throw new BusinessException("All pending orders are cancelled. Please reject the title.");
    }

    // handle cancelled pending orders
    if (CollectionUtils.isNotEmpty(request.getCancelledPendingOrderIds())) {
      cancelledPendingOrders = cancelPendingOrders(request.getCancelledPendingOrderIds());
      request
          .getCancelledPendingOrderIds()
          .forEach(
              r ->
                  pendingOrderSubmittedRepository.saveAndFlush(
                      PendingOrderSubmitted.builder()
                          .pendingOrderSubmittedId(new PendingOrderSubmittedId())
                          .pendingOrderId(r.getId())
                          .build()));
    }

    // handle accepted pending orders
    if (CollectionUtils.isNotEmpty(request.getAcceptedPendingOrderIds())) {
      acceptedPendingOrders =
          acceptAndSubmitPendingOrders(request.getAcceptedPendingOrderIds(), username);
      request
          .getAcceptedPendingOrderIds()
          .forEach(
              r ->
                  pendingOrderSubmittedRepository.saveAndFlush(
                      PendingOrderSubmitted.builder()
                          .pendingOrderSubmittedId(new PendingOrderSubmittedId())
                          .pendingOrderId(r.getId())
                          .build()));
    }

    return SubmitPendingOrderResult.of(cancelledPendingOrders, acceptedPendingOrders);
  }

  public List<PendingOrder> acceptAndSubmitPendingOrders(
      List<PendingOrderId> pendingOrderIds, String username) {
    List<PendingOrder> pendingOrders = pendingOrderService.findByIds(pendingOrderIds);

    // validate accepted pending orders before submitting
    pendingOrders.forEach(this::validatePendingOrderBeforeSubmit);

    List<PendingOrder> submittedPendingOrders =
        pendingOrders.stream()
            .map(
                pendingOrder -> {
                  pendingOrder.setOrderStatus(PendingOrderStatus.SUBMITTED);
                  pendingOrder.setSubmittedBy(StringUtils.defaultString(username, "system"));
                  pendingOrder.setSubmittedDate(LocalDate.now());
                  return pendingOrder;
                })
            .collect(Collectors.toList());

    submittedPendingOrders = pendingOrderService.saveAll(submittedPendingOrders);

    return submittedPendingOrders;
  }

  public List<PendingOrder> cancelPendingOrders(List<PendingOrderId> pendingOrderIds) {
    List<PendingOrder> pendingOrders = pendingOrderService.findByIds(pendingOrderIds);

    if (pendingOrders.stream().anyMatch(po -> po.isInvalid() || po.isSubmittedOrProcessed())) {
      throw new BusinessException(
          "Selection can't contains Submitted, Processed or Invalid Pending Orders");
    }

    List<PendingOrder> cancelledPendingOrders =
        pendingOrders.stream()
            .map(
                pendingOrder -> {
                  pendingOrder.setOrderStatus(PendingOrderStatus.CANCELLED);
                  return pendingOrder;
                })
            .collect(Collectors.toList());
    cancelledPendingOrders = pendingOrderService.saveAll(cancelledPendingOrders);

    return cancelledPendingOrders;
  }

  void validatePendingOrderBeforeSubmit(PendingOrder pendingOrder) {
    if (!pendingOrder.hasSupplier()) {
      throw new BusinessException(
          String.format(
              "No Supplier specified for format %s, Please specify a supplier.",
              pendingOrder.getFormat()));
    }

    if (SupplierStatus.INACTIVE.equals(pendingOrder.getSupplier().getStatus())) {
      throw new BusinessException(
          String.format("Supplier %s is INACTIVE", pendingOrder.getSupplier().getCode()));
    }

    if (!pendingOrder.isNew()) {
      throw new BusinessException(
          String.format(
              "Pending order %s-%s is not in NEW state.",
              pendingOrder.getCustomer().getCode(), pendingOrder.getFormat()));
    }

    if (pendingOrder.isInvalid()) {
      throw new BusinessException(
          String.format(
              "Pending order %s-%s is in invalid state.",
              pendingOrder.getCustomer().getCode(), pendingOrder.getFormat()));
    }

    if ((pendingOrder.getQuantity() <= 0)) {
      throw new BusinessException(
          String.format(
              "Pending order %s-%s has quantity less than zero.",
              pendingOrder.getCustomer().getCode(), pendingOrder.getFormat()));
    }
  }
}

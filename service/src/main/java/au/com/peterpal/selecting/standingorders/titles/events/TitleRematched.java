package au.com.peterpal.selecting.standingorders.titles.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.titles.dto.RematchResult;
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProduct;
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedProductId;
import au.com.peterpal.selecting.standingorders.titles.entity.MatchedStandingOrder;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Setter
@Getter
@SuperBuilder
public class TitleRematched extends DomainEvent {
  List<MatchedProductId> productsNoLongerMatchedIds;
  List<String> productsNoLongerMatchedIsbns;
  List<StandingOrderId> standingOrdersNoLongerMatchedIds;
  List<StandingOrderId> newMatchedStandingOrderIds;
  List<PendingOrderId> invalidatedPendingOrders;

  public static TitleRematched from(UUID titleId, String username, RematchResult rematchResult) {
    return TitleRematched.builder()
        .id(titleId)
        .username(username)
        .productsNoLongerMatchedIds(
            Optional.ofNullable(rematchResult.getProductsNoLongerMatched())
                .orElse(Lists.newArrayList())
                .stream()
                .map(MatchedProduct::getMatchedProductId)
                .collect(Collectors.toList()))
        .productsNoLongerMatchedIsbns(
            Optional.ofNullable(rematchResult.getProductsNoLongerMatched())
                .orElse(Lists.newArrayList())
                .stream()
                .map(MatchedProduct::getIsbn)
                .collect(Collectors.toList()))
        .standingOrdersNoLongerMatchedIds(
            Optional.ofNullable(rematchResult.getStandingOrdersNoLongerMatched())
                .orElse(Lists.newArrayList())
                .stream()
                .map(MatchedStandingOrder::getStandingOrder)
                .map(StandingOrder::getStandingOrderId)
                .collect(Collectors.toList()))
        .newMatchedStandingOrderIds(
            Optional.ofNullable(rematchResult.getNewMatchedStandingOrders())
                .orElse(Lists.newArrayList())
                .stream()
                .map(MatchedStandingOrder::getStandingOrder)
                .map(StandingOrder::getStandingOrderId)
                .collect(Collectors.toList()))
        .invalidatedPendingOrders(rematchResult.getInvalidatedPendingOrders())
        .build();
  }
}

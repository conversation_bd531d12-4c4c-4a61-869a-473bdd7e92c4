package au.com.peterpal.selecting.standingorders.ext.budget.entity;

import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "budget_related_fund")
public class BudgetRelatedFund {
    @EqualsAndHashCode.Include
    @ToString.Include
    @NonNull
    @NotNull
    @EmbeddedId
    private BudgetRelatedFundId budgetRelatedFundId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "fund_id", referencedColumnName = "id")
    private Fund fund;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "budget_id", referencedColumnName = "id")
    private Budget budget;
}

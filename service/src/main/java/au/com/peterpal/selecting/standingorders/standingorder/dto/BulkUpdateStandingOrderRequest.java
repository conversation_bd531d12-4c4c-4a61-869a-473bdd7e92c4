package au.com.peterpal.selecting.standingorders.standingorder.dto;

import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.*;

@With
@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
@ToString
public class BulkUpdateStandingOrderRequest {

  @NotNull @NonNull
  @Builder.Default
  Boolean appendNotes = true;

  StandingOrderRequest searchRequest;
  List<StandingOrderId> includedStandingOrderIds;
  List<StandingOrderId> excludedStandingOrderIds;

  String updatedDescription;
  String updatedNotes;
  StandingOrderStatus updatedStatus;
}

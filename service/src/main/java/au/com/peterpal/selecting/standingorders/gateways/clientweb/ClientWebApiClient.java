package au.com.peterpal.selecting.standingorders.gateways.clientweb;

import au.com.peterpal.selecting.standingorders.config.FeignConfig;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.ClientWebProductInfo;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.ProductData;
import au.com.peterpal.selecting.standingorders.gateways.clientweb.dto.CwProductInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(name = "client-web-api", url = "${catalogue-service.url}", configuration = FeignConfig.class)
public interface ClientWebApiClient {

  @GetMapping(
      path = "/v1/products/{productId}",
      produces = MediaType.APPLICATION_JSON_VALUE)
  ClientWebProductInfo getProductById(@PathVariable("productId") Integer productId);

  /**
   * Get products by search criteria.
   * <p>
   * Search parameters:<br>
   * titleWithoutPrefix<br>
   * personNameInverted<br>
   * imprintName<br>
   * productFormat<br>
   * seriesTitle<br>
   * pageSize<br>
   * offset<br>
   * </p>
   * @param params search parameters
   * @return the products and paging information
   */
  @GetMapping(
      path = "/v1/products/search",
      produces = MediaType.APPLICATION_JSON_VALUE
  )
  ProductData search(@RequestParam String titleWithoutPrefix,
                     @RequestParam String personNameInverted,
                     @RequestParam String imprintName,
                     @RequestParam List<String> productForms,
                     @RequestParam String seriesTitle,
                     @RequestParam Integer pageSize,
                     @RequestParam Integer offset);

  @GetMapping(
      path = "/v1/products/find-by-isbn/{isbn}",
      produces = MediaType.APPLICATION_JSON_VALUE)
  CwProductInfo getProductByIsbn(@PathVariable("isbn") String isbn);
}

package au.com.peterpal.selecting.standingorders.admin.boundary;

import au.com.peterpal.selecting.standingorders.ext.branch.control.dto.BranchInfo;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.Branch;
import au.com.peterpal.selecting.standingorders.ext.customer.control.CustomerService;
import au.com.peterpal.selecting.standingorders.ext.customer.dto.CustomerInfo;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Log4j2
@RequiredArgsConstructor
@RequestMapping("/api/customer")
@SecurityRequirement(name = "BearerAuth")
public class CustomerController {

  private final CustomerService service;

  @GetMapping("/{id}/branches")
  @Operation(summary = "Get branches for customer id")
  public List<BranchInfo> getBranches(
      @RequestHeader(value = "username", defaultValue = "") String username,
      @PathVariable UUID id
  ) {
    List<Branch> branches = service.getBranches(CustomerId.of(id)).stream()
        .sorted(Comparator.comparing(b -> b.getCode()))
        .collect(Collectors.toList());
    return BranchInfo.from(branches);
  }

  @GetMapping
  @Operation(summary = "Get all customers")
  public List<CustomerInfo> getAll() {
    log.info(() -> "Get all customers");
    return CustomerInfo.from(service.getAll(Sort.by(Sort.Direction.ASC, "name")));
  }
}

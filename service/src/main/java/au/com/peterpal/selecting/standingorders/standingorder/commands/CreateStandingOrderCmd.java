package au.com.peterpal.selecting.standingorders.standingorder.commands;

import au.com.peterpal.selecting.standingorders.standingorder.model.Term;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@ToString
public class CreateStandingOrderCmd {

  private String username;

  private String standingOrderNumber;

  private String description;

  private String notes;

  private List<Term> terms;

}

package au.com.peterpal.selecting.standingorders.ext.supplier.boundary.dto;

import au.com.peterpal.selecting.standingorders.ext.supplier.entity.SupplierId;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import javax.validation.constraints.NotNull;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SupplierMessage {

  @EqualsAndHashCode.Include
  @ToString.Include
  @NonNull
  @NotNull
  private SupplierId supplierId;

  @NotNull
  @NonNull
  private SupplierStatus status;
  private String name;
  private String code;
}

package au.com.peterpal.selecting.standingorders.customerstandingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import au.com.peterpal.selecting.standingorders.customerstandingorder.dto.CreateOrUpdateAllocation;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrder;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.UUID;

@Getter
@SuperBuilder
public class CustomerStandingOrderCreated extends DomainEvent {

  private StandingOrderStatus customerStandingOrderStatus;

  private List<CreateOrUpdateAllocation> allocations;

  private Customer customer;

  private StandingOrder standingOrder;

  public static CustomerStandingOrderCreated from(
      Customer customer,
      StandingOrder standingOrder,
      List<CreateOrUpdateAllocation> allocations,
      String username) {

    return CustomerStandingOrderCreated.builder()
        .id(UUID.randomUUID())
        .customer(customer)
        .customerStandingOrderStatus(StandingOrderStatus.ACTIVE)
        .standingOrder(standingOrder)
        .allocations(allocations)
        .username(username)
        .build();
  }
}

package au.com.peterpal.selecting.standingorders.customerstandingorder.control;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseId;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleasePreferenceId;
import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class ReleaseService {

  private final ReleaseRepository releaseRepository;

  public ReleaseService(ReleaseRepository releaseRepository) {
    this.releaseRepository = releaseRepository;
  }

  public Release findByReleasePrefIdAndAllocationId(
      ReleasePreferenceId releasePreferenceId, AllocationId allocationId) {

    return releaseRepository.findByAllocation_AllocationIdAndPreference_ReleasePreferenceId(
        allocationId, releasePreferenceId);
  }

  public Release findById(ReleaseId id) {
    return releaseRepository
        .findById(id)
        .orElseThrow(() -> new ResourceNotFoundException(Release.class, String.valueOf(id)));
  }
}

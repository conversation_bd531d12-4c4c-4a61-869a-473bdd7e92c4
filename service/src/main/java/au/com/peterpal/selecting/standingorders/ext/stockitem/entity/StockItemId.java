package au.com.peterpal.selecting.standingorders.ext.stockitem.entity;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Embeddable
@NoArgsConstructor
public class StockItemId extends UuidEntityId {

  public static StockItemId of(@NotNull UUID id) {
    return new StockItemId(id);
  }

  public static StockItemId of(@NotBlank String id) {
    return new StockItemId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public StockItemId(@NotNull UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public StockItemId(@NotBlank String id) {
    super(UUID.fromString(id));
  }

  @Override
  public UUID getId() {
    return super.getId();
  }
}

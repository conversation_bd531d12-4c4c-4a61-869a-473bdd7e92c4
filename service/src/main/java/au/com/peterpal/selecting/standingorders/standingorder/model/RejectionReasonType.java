package au.com.peterpal.selecting.standingorders.standingorder.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "rejection_reason_type")
public class RejectionReasonType {

  @EqualsAndHashCode.Include @ToString.Include @NonNull @NotNull @EmbeddedId
  private RejectionReasonTypeId rejectionReasonTypeId;

  @NonNull @NotNull private String name;

  @NonNull
  @NotNull
  @Enumerated(EnumType.STRING)
  private RejectionReasonStatus status;

  @NonNull
  @NotNull
  @Builder.Default
  private Boolean displayable = Boolean.TRUE;
}

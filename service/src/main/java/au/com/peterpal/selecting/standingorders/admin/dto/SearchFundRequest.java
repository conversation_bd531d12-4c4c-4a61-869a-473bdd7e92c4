package au.com.peterpal.selecting.standingorders.admin.dto;

import au.com.peterpal.selecting.standingorders.ext.budget.entity.FundType;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.boundary.dto.FundStatus;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@ToString
public class SearchFundRequest {
    String code;
    CustomerId customerId;
    String staff;
    FundStatus status;
    LocalDate currentFinStartDate;
    FundType type;
    List<CategoryId> categoryIds;
}

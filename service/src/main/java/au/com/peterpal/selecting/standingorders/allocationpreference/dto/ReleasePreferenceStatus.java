package au.com.peterpal.selecting.standingorders.allocationpreference.dto;

import au.com.peterpal.common.rest.validation.ResourceNotFoundException;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.SmallFormatPaperbackRule;
import com.google.common.base.Enums;
import lombok.*;

@With
@Value
@Setter(AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class ReleasePreferenceStatus {

  private String releaseType;

  private String actionType;

  private Integer quantity;

  private String initialAssignmentRule;

  private String smallFormatPaperbackRule;

  static final String INVALID_ACTION = "Invalid Action Type.";
  static final String INVALID_RELEASE_TYPE = "Invalid Release Type.";
  static final String INVALID_ASSIGNMENT_RULE = "Invalid Assignment Rule.";
  static final String INVALID_SMALL_FORMAT_PAPER_BACK_RULE = "Invalid Small Format Paper Back Rule.";
  static final String EMPTY_ACTION = "actionType must not be null";

  public static ReleasePreferenceStatus from(UpdateReleasePreference preference) {
    return ReleasePreferenceStatus.builder()
        .releaseType(preference.getReleaseType())
        .actionType(preference.getActionType())
        .initialAssignmentRule(preference.getInitialAssignmentRule())
        .smallFormatPaperbackRule(preference.getSmallFormatPaperbackRule())
        .build();
  }

  public void validateStatusAndRequiredFields() {

    if (actionType.isEmpty()) {
      throw new ResourceNotFoundException(EMPTY_ACTION);
    }

    if ((!Enums.getIfPresent(ActionType.class, actionType).isPresent()) && !actionType.isEmpty()) {
      throw new ResourceNotFoundException(INVALID_ACTION);
    }

    this.validateActionTypeEqualToIgnore();
    this.validateReleaseEqualToInitialAndActionNoEqualToIgnore();
  }

  private void validateActionTypeEqualToIgnore() {

    if (ActionType.valueOf(actionType).equals(ActionType.IGNORE) && releaseType.isEmpty()) {
      throw new ResourceNotFoundException(INVALID_RELEASE_TYPE);
    }

    if ((!Enums.getIfPresent(ReleaseType.class, releaseType).isPresent()) && !releaseType.isEmpty()
        || releaseType.isEmpty()) {
      throw new ResourceNotFoundException(INVALID_RELEASE_TYPE);
    }
  }

  private void validateReleaseEqualToInitialAndActionNoEqualToIgnore() {

    if (ReleaseType.valueOf(releaseType).equals(ReleaseType.INITIAL)
        && !ActionType.valueOf(actionType).equals(ActionType.IGNORE)) {

      if (initialAssignmentRule.isEmpty() && smallFormatPaperbackRule.isEmpty()) {
        throw new ResourceNotFoundException(
            INVALID_ASSIGNMENT_RULE + "\n" + INVALID_SMALL_FORMAT_PAPER_BACK_RULE);
      }

      if (initialAssignmentRule.isEmpty()) {
        throw new ResourceNotFoundException(INVALID_ASSIGNMENT_RULE);
      }

      if (smallFormatPaperbackRule.isEmpty()) {
        throw new ResourceNotFoundException(INVALID_SMALL_FORMAT_PAPER_BACK_RULE);
      }
    }

    this.validateInitialAssignmentRuleAndSmallFormatPaperbackRule();
  }

  private void validateInitialAssignmentRuleAndSmallFormatPaperbackRule() {
    if ((!Enums.getIfPresent(AssignmentRule.class, initialAssignmentRule).isPresent())
        && !initialAssignmentRule.isEmpty()) {
      throw new ResourceNotFoundException(INVALID_ASSIGNMENT_RULE);
    }

    if ((!Enums.getIfPresent(SmallFormatPaperbackRule.class, smallFormatPaperbackRule).isPresent())
        && !smallFormatPaperbackRule.isEmpty()) {
      throw new ResourceNotFoundException(INVALID_SMALL_FORMAT_PAPER_BACK_RULE);
    }
  }
}

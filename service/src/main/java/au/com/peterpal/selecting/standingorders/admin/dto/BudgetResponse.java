package au.com.peterpal.selecting.standingorders.admin.dto;

import java.math.BigDecimal;
import lombok.*;
import org.springframework.data.domain.Page;

@With
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BudgetResponse {
  private Page<BudgetResponsePage> budgetResponsePages;
  private BigDecimal totalBudgetAmount;
  private BigDecimal totalOnOrderAmount;
  private BigDecimal totalInvoicedAmount;
  private BigDecimal totalSpentAmount;
  private BigDecimal totalRemainingAmountMonthly;
  private BigDecimal totalUtilisedMonthly;
  private BigDecimal totalRemainingAmountYearly;
  private BigDecimal totalUtilisedYearly;
  private BigDecimal totalBudgetAmountYearly;
  private BigDecimal totalBudgetAmountMonthly;
}

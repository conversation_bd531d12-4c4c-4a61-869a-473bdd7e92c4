package au.com.peterpal.selecting.standingorders.catalog.model;

/**
 * @docRoot
 * Based on Onix List 91
 */
public enum CountryCode {

	AU ("AU", "Australia", ""),
	GB ("GB", "United Kingdom", ""),
	US ("US", "United States", ""),
	NZ ("NZ", "New Zealand", ""),
	AD ("AD", "Andorra", ""),
	AE ("AE", "United Arab Emirates", ""),
	AF ("AF", "Afghanistan", ""),
	AG ("AG", "Antigua and Barbuda", ""),
	AI ("AI", "Anguilla", ""),
	AL ("AL", "Albania", ""),
	AM ("AM", "Armenia", ""),
	AN ("AN", "Netherlands Antilles", ""),
	AO ("AO", "Angola", ""),
	AQ ("AQ", "Antarctica", ""),
	AR ("AR", "Argentina", ""),
	AS ("AS", "American Samoa", ""),
	AT ("AT", "Austria", ""),
	AW ("AW", "Aruba", ""),
	AX ("AX", "Aland Islands", ""),
	AZ ("AZ", "Azerbaijan", ""),
	BA ("BA", "Bosnia and Herzegovina", ""),
	BB ("BB", "Barbados", ""),
	BD ("BD", "Bangladesh", ""),
	BE ("BE", "Belgium", ""),
	BF ("BF", "Burkina Faso", ""),
	BG ("BG", "Bulgaria", ""),
	BH ("BH", "Bahrain", ""),
	BI ("BI", "Burundi", ""),
	BJ ("BJ", "Benin", ""),
	BL ("BL", "Saint Barthelemy", ""),
	BM ("BM", "Bermuda", ""),
	BN ("BN", "Brunei Darussalam", ""),
	BO ("BO", "Bolivia", ""),
	BR ("BR", "Brazil", ""),
	BS ("BS", "Bahamas", ""),
	BT ("BT", "Bhutan", ""),
	BV ("BV", "Bouvet Island", ""),
	BW ("BW", "Botswana", ""),
	BY ("BY", "Belarus", ""),
	BZ ("BZ", "Belize", ""),
	CA ("CA", "Canada", ""),
	CC ("CC", "Cocos (Keeling) Islands", ""),
	CD ("CD", "Congo, Democratic Republic of the", ""),
	CF ("CF", "Central African Republic", ""),
	CG ("CG", "Congo", ""),
	CH ("CH", "Switzerland", ""),
	CI ("CI", "Cote D'Ivoire", ""),
	CK ("CK", "Cook Islands", ""),
	CL ("CL", "Chile", ""),
	CM ("CM", "Cameroon", ""),
	CN ("CN", "China", ""),
	CO ("CO", "Colombia", ""),
	CR ("CR", "Costa Rica", ""),
	CS ("CS", "Serbia and Montenegro", "DEPRECATED, replaced by ME - Montenegro and RS - Serbia"),
	CU ("CU", "Cuba", ""),
	CV ("CV", "Cape Verde", ""),
	CX ("CX", "Christmas Island", ""),
	CY ("CY", "Cyprus", ""),
	CZ ("CZ", "Czech Republic", ""),
	DE ("DE", "Germany", ""),
	DJ ("DJ", "Djibouti", ""),
	DK ("DK", "Denmark", ""),
	DM ("DM", "Dominica", ""),
	DO ("DO", "Dominican Republic", ""),
	DZ ("DZ", "Algeria", ""),
	EC ("EC", "Ecuador", ""),
	EE ("EE", "Estonia", ""),
	EG ("EG", "Egypt", ""),
	EH ("EH", "Western Sahara", ""),
	ER ("ER", "Eritrea", ""),
	ES ("ES", "Spain", ""),
	ET ("ET", "Ethiopia", ""),
	FI ("FI", "Finland", ""),
	FJ ("FJ", "Fiji", ""),
	FK ("FK", "Falkland Islands (Malvinas)", ""),
	FM ("FM", "Micronesia, Federated States of", ""),
	FO ("FO", "Faroe Islands", ""),
	FR ("FR", "France", ""),
	GA ("GA", "Gabon", ""),
	GD ("GD", "Grenada", ""),
	GE ("GE", "Georgia", ""),
	GF ("GF", "French Guiana", ""),
	GG ("GG", "Guernsey", ""),
	GH ("GH", "Ghana", ""),
	GI ("GI", "Gibraltar", ""),
	GL ("GL", "Greenland", ""),
	GM ("GM", "Gambia", ""),
	GN ("GN", "Guinea", ""),
	GP ("GP", "Guadeloupe", ""),
	GQ ("GQ", "Equatorial Guinea", ""),
	GR ("GR", "Greece", ""),
	GS ("GS", "South Georgia and the South Sandwich Islands", ""),
	GT ("GT", "Guatemala", ""),
	GU ("GU", "Guam", ""),
	GW ("GW", "Guinea-Bissau", ""),
	GY ("GY", "Guyana", ""),
	HK ("HK", "Hong Kong", ""),
	HM ("HM", "Heard Island and McDonald Islands", ""),
	HN ("HN", "Honduras", ""),
	HR ("HR", "Croatia", ""),
	HT ("HT", "Haiti", ""),
	HU ("HU", "Hungary", ""),
	ID ("ID", "Indonesia", ""),
	IE ("IE", "Ireland", ""),
	IL ("IL", "Israel", ""),
	IM ("IM", "Isle of Man", ""),
	IN ("IN", "India", ""),
	IO ("IO", "British Indian Ocean Territory", ""),
	IQ ("IQ", "Iraq", ""),
	IR ("IR", "Iran, Islamic Republic of", ""),
	IS ("IS", "Iceland", ""),
	IT ("IT", "Italy", ""),
	JE ("JE", "Jersey", ""),
	JM ("JM", "Jamaica", ""),
	JO ("JO", "Jordan", ""),
	JP ("JP", "Japan", ""),
	KE ("KE", "Kenya", ""),
	KG ("KG", "Kyrgyzstan", ""),
	KH ("KH", "Cambodia", ""),
	KI ("KI", "Kiribati", ""),
	KM ("KM", "Comoros", ""),
	KN ("KN", "Saint Kitts and Nevis", ""),
	KP ("KP", "Korea, Democratic People's Republic of", ""),
	KR ("KR", "Korea, Republic of", ""),
	KW ("KW", "Kuwait", ""),
	KY ("KY", "Cayman Islands", ""),
	KZ ("KZ", "Kazakhstan", ""),
	LA ("LA", "Lao People's Democratic Republic", ""),
	LB ("LB", "Lebanon", ""),
	LC ("LC", "Saint Lucia", ""),
	LI ("LI", "Liechtenstein", ""),
	LK ("LK", "Sri Lanka", ""),
	LR ("LR", "Liberia", ""),
	LS ("LS", "Lesotho", ""),
	LT ("LT", "Lithuania", ""),
	LU ("LU", "Luxembourg", ""),
	LV ("LV", "Latvia", ""),
	LY ("LY", "Libyan Arab Jamahiriya", ""),
	MA ("MA", "Morocco", ""),
	MC ("MC", "Monaco", ""),
	MD ("MD", "Moldova, Republic of", ""),
	ME ("ME", "Montenegro", ""),
	MF ("MF", "Saint Martin, French part", ""),
	MG ("MG", "Madagascar", ""),
	MH ("MH", "Marshall Islands", ""),
	MK ("MK", "Macedonia, the former Yugoslav Republic of", ""),
	ML ("ML", "Mali", ""),
	MM ("MM", "Myanmar", ""),
	MN ("MN", "Mongolia", ""),
	MO ("MO", "Macao", ""),
	MP ("MP", "Northern Mariana Islands", ""),
	MQ ("MQ", "Martinique", ""),
	MR ("MR", "Mauritania", ""),
	MS ("MS", "Montserrat", ""),
	MT ("MT", "Malta", ""),
	MU ("MU", "Mauritius", ""),
	MV ("MV", "Maldives", ""),
	MW ("MW", "Malawi", ""),
	MX ("MX", "Mexico", ""),
	MY ("MY", "Malaysia", ""),
	MZ ("MZ", "Mozambique", ""),
	NA ("NA", "Namibia", ""),
	NC ("NC", "New Caledonia", ""),
	NE ("NE", "Niger", ""),
	NF ("NF", "Norfolk Island", ""),
	NG ("NG", "Nigeria", ""),
	NI ("NI", "Nicaragua", ""),
	NL ("NL", "Netherlands", ""),
	NO ("NO", "Norway", ""),
	NP ("NP", "Nepal", ""),
	NR ("NR", "Nauru", ""),
	NU ("NU", "Niue", ""),
	OM ("OM", "Oman", ""),
	PA ("PA", "Panama", ""),
	PE ("PE", "Peru", ""),
	PF ("PF", "French Polynesia", ""),
	PG ("PG", "Papua New Guinea", ""),
	PH ("PH", "Philippines", ""),
	PK ("PK", "Pakistan", ""),
	PL ("PL", "Poland", ""),
	PM ("PM", "Saint Pierre and Miquelon", ""),
	PN ("PN", "Pitcairn", ""),
	PR ("PR", "Puerto Rico", ""),
	PS ("PS", "Palestinian Territory, Occupied", ""),
	PT ("PT", "Portugal", ""),
	PW ("PW", "Palau", ""),
	PY ("PY", "Paraguay", ""),
	QA ("QA", "Qatar", ""),
	RE ("RE", "Reunion", ""),
	RO ("RO", "Romania", ""),
	RS ("RS", "Serbia", ""),
	RU ("RU", "Russian Federation", ""),
	RW ("RW", "Rwanda", ""),
	SA ("SA", "Saudi Arabia", ""),
	SB ("SB", "Solomon Islands", ""),
	SC ("SC", "Seychelles", ""),
	SD ("SD", "Sudan", ""),
	SE ("SE", "Sweden", ""),
	SG ("SG", "Singapore", ""),
	SH ("SH", "Saint Helena", ""),
	SI ("SI", "Slovenia", ""),
	SJ ("SJ", "Svalbard and Jan Mayen", ""),
	SK ("SK", "Slovakia", ""),
	SL ("SL", "Sierra Leone", ""),
	SM ("SM", "San Marino", ""),
	SN ("SN", "Senegal", ""),
	SO ("SO", "Somalia", ""),
	SR ("SR", "Suriname", ""),
	ST ("ST", "Sao Tome and Principe", ""),
	SV ("SV", "El Salvador", ""),
	SY ("SY", "Syrian Arab Republic", ""),
	SZ ("SZ", "Swaziland", ""),
	TC ("TC", "Turks and Caicos Islands", ""),
	TD ("TD", "Chad", ""),
	TF ("TF", "French Southern Territories", ""),
	TG ("TG", "Togo", ""),
	TH ("TH", "Thailand", ""),
	TJ ("TJ", "Tajikistan", ""),
	TK ("TK", "Tokelau", ""),
	TL ("TL", "Timor-Leste", ""),
	TM ("TM", "Turkmenistan", ""),
	TN ("TN", "Tunisia", ""),
	TO ("TO", "Tonga", ""),
	TR ("TR", "Turkey", ""),
	TT ("TT", "Trinidad and Tobago", ""),
	TV ("TV", "Tuvalu", ""),
	TW ("TW", "Taiwan, Province of China", ""),
	TZ ("TZ", "Tanzania, United Republic of", ""),
	UA ("UA", "Ukraine", ""),
	UG ("UG", "Uganda", ""),
	UM ("UM", "United States Minor Outlying Islands", ""),
	UY ("UY", "Uruguay", ""),
	UZ ("UZ", "Uzbekistan", ""),
	VA ("VA", "Holy See (Vatican City State)", ""),
	VC ("VC", "Saint Vincent and the Grenadines", ""),
	VE ("VE", "Venezuela, Bolivarian Republic of", ""),
	VG ("VG", "Virgin Islands, British", ""),
	VI ("VI", "Virgin Islands, US", ""),
	VN ("VN", "Viet Nam", ""),
	VU ("VU", "Vanuatu", ""),
	WF ("WF", "Wallis and Futuna", ""),
	WS ("WS", "Samoa", ""),
	YE ("YE", "Yemen", ""),
	YT ("YT", "Mayotte", ""),
	YU ("YU", "Yugoslavia", "DEPRECATED, replaced by ME - Montenegro and RS - Serbia"),
	ZA ("ZA", "South Africa", ""),
	ZM ("ZM", "Zambia", ""),
	ZW ("ZW", "Zimbabwe", ""),
	AU_CT  ("AU-CT", "Australian Capital Territory", ""),
	AU_NS  ("AU-NS", "New South Wales", ""),
	AU_NT  ("AU-NT", "Northern Territory", ""),
	AU_QL  ("AU-QL", "Queensland", ""),
	AU_SA  ("AU-SA", "South Australia", ""),
	AU_TS  ("AU-TS", "Tasmania", ""),
	AU_VI  ("AU-VI", "Victoria", ""),
	AU_WA  ("AU-WA", "Western Australia", ""),
	CA_AB  ("CA-AB", "Alberta", ""),
	CA_BC  ("CA-BC", "British Columbia", ""),
	CA_MB  ("CA-MB", "Manitoba", ""),
	CA_NB  ("CA-NB", "New Brunswick", ""),
	CA_NL  ("CA-NL", "Newfoundland and Labrador", ""),
	CA_NS  ("CA-NS", "Nova Scotia", ""),
	CA_NT  ("CA-NT", "Northwest Territories", ""),
	CA_NU  ("CA-NU", "Nunavut", ""),
	CA_ON  ("CA-ON", "Ontario", ""),
	CA_PE  ("CA-PE", "Prince Edward Island", ""),
	CA_QC  ("CA-QC", "Quebec", ""),
	CA_SK  ("CA-SK", "Saskatchewan", ""),
	CA_YT  ("CA-YT", "Yukon Territory", ""),
	ES_CN  ("ES-CN", "Canary Islands", ""),
	GB_AIR  ("GB-AIR", "UK airside", "Airside outlets at UK international airports only"),
	GB_APS  ("GB-APS", "UK airports", "All UK airports, including both airside and other outlets"),
	GB_CHA  ("GB-CHA", "Channel Islands", ""),
	GB_ENG  ("GB-ENG", "England", ""),
	GB_EWS  ("GB-EWS", "England, Wales, Scotland", "UK excluding Northern Ireland"),
	GB_IOM  ("GB-IOM", "Isle of Man", ""),
	GB_NIR  ("GB-NIR", "Northern Ireland", ""),
	GB_SCT  ("GB-SCT", "Scotland", ""),
	GB_WLS  ("GB-WLS", "Wales", ""),
	US_AK ("US-AK", "Alaska", ""),
	US_AL ("US-AL", "Alabama", ""),
	US_AR ("US-AR", "Arkansas", ""),
	US_AZ ("US-AZ", "Arizona", ""),
	US_CA ("US-CA", "California", ""),
	US_CO ("US-CO", "Colorado", ""),
	US_CT ("US-CT", "Connecticut", ""),
	US_DC ("US-DC", "District of Columbia", ""),
	US_DE ("US-DE", "Delaware", ""),
	US_FL ("US-FL", "Florida", ""),
	US_GA ("US-GA", "Georgia", ""),
	US_HI ("US-HI", "Hawaii", ""),
	US_IA ("US-IA", "Iowa", ""),
	US_ID ("US-ID", "Idaho", ""),
	US_IL ("US-IL", "Illinois", ""),
	US_IN ("US-IN", "Indiana", ""),
	US_KS ("US-KS", "Kansas", ""),
	US_KY ("US-KY", "Kentucky", ""),
	US_LA ("US-LA", "Louisiana", ""),
	US_MA ("US-MA", "Massachusetts", ""),
	US_MD ("US-MD", "Maryland", ""),
	US_ME ("US-ME", "Maine", ""),
	US_MI ("US-MI", "Michigan", ""),
	US_MN ("US-MN", "Minnesota", ""),
	US_MO ("US-MO", "Missouri", ""),
	US_MS ("US-MS", "Mississippi", ""),
	US_MT ("US-MT", "Montana", ""),
	US_NC ("US-NC", "North Carolina", ""),
	US_ND ("US-ND", "North Dakota", ""),
	US_NE ("US-NE", "Nebraska", ""),
	US_NH ("US-NH", "New Hampshire", ""),
	US_NJ ("US-NJ", "New Jersey", ""),
	US_NM ("US-NM", "New Mexico", ""),
	US_NV ("US-NV", "Nevada", ""),
	US_NY ("US-NY", "New York", ""),
	US_OH ("US-OH", "Ohio", ""),
	US_OK ("US-OK", "Oklahoma", ""),
	US_OR ("US-OR", "Oregon", ""),
	US_PA ("US-PA", "Pennsylvania", ""),
	US_RI ("US-RI", "Rhode Island", ""),
	US_SC ("US-SC", "South Carolina", ""),
	US_SD ("US-SD", "South Dakota", ""),
	US_TN ("US-TN", "Tennessee", ""),
	US_TX ("US-TX", "Texas", ""),
	US_UT ("US-UT", "Utah", ""),
	US_VA ("US-VA", "Virginia", ""),
	US_VT ("US-VT", "Vermont", ""),
	US_WA ("US-WA", "Washington", ""),
	US_WI ("US-WI", "Wisconsin", ""),
	US_WV ("US-WV", "West Virginia", ""),
	US_WY ("US-WY", "Wyoming", ""),
	ROW  ("ROW", "Rest of world", "World except as otherwise specified"),
	WORLD  ("WORLD", "World", "");

	private final String code;
	private final String description;
	private final String notes;

	CountryCode(String code, String description, String notes) {
		this.code = code;
		this.description = description;
		this.notes = notes;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public String getNotes() {
		return notes;
	}

	public static CountryCode mapOnixCode(String onixCode) {
		for (CountryCode value : CountryCode.values()) {
			if (value.code.equals(onixCode)) {
				return value;
			}
		}
		throw new IllegalArgumentException("Invalid " + CountryCode.class.getSimpleName() + ": " + onixCode);
	}

	@Override
	public String toString() {
		return this.description;
	}
}

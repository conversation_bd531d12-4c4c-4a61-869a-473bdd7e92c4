package au.com.peterpal.selecting.standingorders.config;


import au.com.peterpal.selecting.standingorders.allocationpreference.model.AssignmentRule;
import au.com.peterpal.selecting.standingorders.pendingorder.control.FirstAvailableRule;
import au.com.peterpal.selecting.standingorders.pendingorder.control.POCreator;
import au.com.peterpal.selecting.standingorders.pendingorder.control.SplitRule;
import java.util.HashMap;
import java.util.Map;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class POCreatorConfig {

  @Bean
  public Map<AssignmentRule, POCreator> creatorMap() {
    Map<AssignmentRule, POCreator> map = new HashMap<>();
    map.put(AssignmentRule.FIRST_AVAILABLE, new FirstAvailableRule());
    map.put(AssignmentRule.SPLIT, new SplitRule());
    return map;
  }
}

package au.com.peterpal.selecting.standingorders.pendingorder.control;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.ReleaseFormat;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.PendingOrderStandingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.ProcessFormatResponse;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface PendingOrderRepositoryCustom {
  Page<PendingOrder> searchAllByCustomer(CustomerId customerId, Pageable pageRequest);

  List<PendingOrderStandingOrder> searchAllByTitle(
      TitleId titleId, PendingOrderStatus status, String category);

  List<ProcessFormatResponse> findByTitleGroupByFormat(TitleId titleId);

  List<PendingOrder> findByTitleIdAndStandingOrderId(
      TitleId titleId, List<StandingOrderId> standingOrderIds);

  List<PendingOrder> findAllByTitleIdAndFormatAndAllocationId(
      TitleId titleId, ReleaseFormat format, AllocationId allocationId);

  List<PendingOrder> findAllByTitleIdAllocationId(TitleId titleId, AllocationId allocationId, PendingOrderStatus status);

  List<PendingOrder> findAllByTitleIdsAllocationId(
      List<TitleId> titleIds, AllocationId allocationId);
}

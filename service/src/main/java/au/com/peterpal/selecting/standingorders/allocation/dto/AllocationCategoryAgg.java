package au.com.peterpal.selecting.standingorders.allocation.dto;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;

import javax.persistence.Entity;
import javax.persistence.Id;
import lombok.Data;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;
import org.hibernate.annotations.Synchronize;

@Entity
@Data
@Subselect(
    "select a.id as id, min(c.code) as category from allocation a \n"
        + "inner join allocation_categories ac on a.id = ac.allocation_id \n"
        + "inner join category c on c.id = ac.categories_id \n"
        + "group by a.id ")
@Synchronize({
  "allocation",
  "allocation_categories",
  "category"
})
@Immutable
public class AllocationCategoryAgg {
  @Id private AllocationId allocationId;
  private String category;
}

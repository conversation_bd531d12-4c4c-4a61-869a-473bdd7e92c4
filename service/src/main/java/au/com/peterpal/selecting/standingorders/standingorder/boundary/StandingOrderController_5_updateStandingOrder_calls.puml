@startuml

/' diagram meta data start
config=CallConfiguration;
{
  "rootMethod": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController#updateStandingOrder(String,StandingOrderId,UpdateStandingOrderRequest)",
  "projectClassification": {
    "searchMode": "OpenProject", // OpenProject, AllProjects
    "includedProjects": "",
    "pathEndKeywords": "*.impl",
    "isClientPath": "",
    "isClientName": "",
    "isTestPath": "",
    "isTestName": "",
    "isMappingPath": "",
    "isMappingName": "",
    "isDataAccessPath": "",
    "isDataAccessName": "",
    "isDataStructurePath": "",
    "isDataStructureName": "",
    "isInterfaceStructuresPath": "",
    "isInterfaceStructuresName": "",
    "isEntryPointPath": "",
    "isEntryPointName": ""
  },
  "graphRestriction": {
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "removeByInheritance": "", // inheritance/annotation based filtering is done in a second step
    "removeByAnnotation": "",
    "removeByClassPackage": "", // cleanup the graph after inheritance/annotation based filtering is done
    "removeByClassName": "",
    "cutMappings": false,
    "cutEnum": true,
    "cutTests": true,
    "cutClient": true,
    "cutDataAccess": true,
    "cutInterfaceStructures": true,
    "cutDataStructures": true,
    "cutGetterAndSetter": true,
    "cutConstructors": true
  },
  "graphTraversal": {
    "forwardDepth": 3,
    "backwardDepth": 3,
    "classPackageExcludeFilter": "",
    "classPackageIncludeFilter": "",
    "classNameExcludeFilter": "",
    "classNameIncludeFilter": "",
    "methodNameExcludeFilter": "",
    "methodNameIncludeFilter": "",
    "hideMappings": false,
    "hideDataStructures": false,
    "hidePrivateMethods": true,
    "hideInterfaceCalls": true, // indirection: implementation -> interface (is hidden) -> implementation
    "onlyShowApplicationEntryPoints": false // root node is included
  },
  "details": {
    "aggregation": "GroupByClass", // ByClass, GroupByClass, None
    "showMethodParametersTypes": false,
    "showMethodParametersNames": false,
    "showMethodReturnType": false,
    "showPackageLevels": 2,
    "showCallOrder": false,
    "edgeMode": "MethodsOnly", // TypesOnly, MethodsOnly, TypesAndMethods, MethodsAndDirectTypeUsage
    "showDetailedClassStructure": false
  },
  "rootClass": "au.com.peterpal.selecting.standingorders.standingorder.boundary.StandingOrderController"
}
diagram meta data end '/



digraph g {
    rankdir="LR"
    splines=polyline


'nodes
subgraph cluster_98689 {
   	label=com
	labeljust=l
	fillcolor="#ececec"
	style=filled

   subgraph cluster_1300071644 {
   	label=peterpal
	labeljust=l
	fillcolor="#d8d8d8"
	style=filled

   subgraph cluster_1122492677 {
   	label=StandingOrderUpdated
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderUpdated586215038XXXbuilder0[
	label="+ builder()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderUpdated

null"
	fontcolor=darkgreen
];

StandingOrderUpdated586215038XXXfrom1763286297[
	label="+ from()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderUpdated

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1665136640 {
   	label=TermsRequest
	labeljust=l
	fillcolor=white
	style=filled

   TermsRequest666806762XXXfrom1857943656[
	label="+ from()"
	style=filled
	fillcolor=white
	tooltip="TermsRequest

null"
	fontcolor=darkgreen
];
}

subgraph cluster_1774816029 {
   	label=StandingOrderController
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderController1189585491XXXupdateStandingOrder540275774[
	label="+ updateStandingOrder()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderController

null"
	penwidth=4
	fontcolor=darkgreen
];
}

subgraph cluster_1867065251 {
   	label=Affirm
	labeljust=l
	fillcolor=white
	style=filled

   Affirm1046088404XXXnotNull1808118735[
	label="+ notNull()"
	style=filled
	fillcolor=white
	tooltip="Affirm

&#10;  Affirm that the object is not {@code null}&#10;  \<pre\>&#10;  Validator.of(value).notNull(\"The value must not be null\");&#10;  \</pre\>&#10;  @param message&#10; "
	fontcolor=darkgreen
];

Affirm1046088404XXXof1939501217[
	label="+ of()"
	style=filled
	fillcolor=white
	tooltip="Affirm

null"
	fontcolor=darkgreen
];
}

subgraph cluster_614879794 {
   	label=StandingOrderBL
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrderBL1335247380XXXhandle550102296[
	label="+ handle()"
	style=filled
	fillcolor=white
	tooltip="StandingOrderBL

null"
	fontcolor=darkgreen
];
}

subgraph cluster_943466892 {
   	label=StandingOrder
	labeljust=l
	fillcolor=white
	style=filled

   StandingOrder843015776XXXaddTerm1670069428[
	label="+ addTerm()"
	style=filled
	fillcolor=white
	tooltip="StandingOrder

null"
	fontcolor=darkgreen
];

StandingOrder843015776XXXaddTerm2603148[
	label="+ addTerm()"
	style=filled
	fillcolor=white
	tooltip="StandingOrder

null"
	fontcolor=darkgreen
];

StandingOrder843015776XXXtoBuilder0[
	label="+ toBuilder()"
	style=filled
	fillcolor=white
	tooltip="StandingOrder

null"
	fontcolor=darkgreen
];
}
}
}

'edges
StandingOrder843015776XXXaddTerm1670069428 -> StandingOrder843015776XXXaddTerm2603148;
StandingOrderBL1335247380XXXhandle550102296 -> Affirm1046088404XXXnotNull1808118735;
StandingOrderBL1335247380XXXhandle550102296 -> Affirm1046088404XXXof1939501217;
StandingOrderBL1335247380XXXhandle550102296 -> StandingOrder843015776XXXaddTerm1670069428;
StandingOrderBL1335247380XXXhandle550102296 -> StandingOrder843015776XXXtoBuilder0;
StandingOrderBL1335247380XXXhandle550102296 -> StandingOrderUpdated586215038XXXfrom1763286297;
StandingOrderBL1335247380XXXhandle550102296 -> TermsRequest666806762XXXfrom1857943656;
StandingOrderController1189585491XXXupdateStandingOrder540275774 -> StandingOrderBL1335247380XXXhandle550102296;
StandingOrderUpdated586215038XXXfrom1763286297 -> StandingOrderUpdated586215038XXXbuilder0;

}
@enduml

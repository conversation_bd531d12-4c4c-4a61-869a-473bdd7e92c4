package au.com.peterpal.selecting.standingorders.ext.customer.dto;

import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.utils.Affirm;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class CustomerInfo {

  @NonNull @NotNull private CustomerId id;
  @NonNull @NotNull private String code;
  private String name;

  public static List<CustomerInfo> from(List<Customer> customerList) {
    return Optional.ofNullable(customerList)
      .map(List::stream)
      .orElseGet(Stream::empty)
      .map(CustomerInfo::from)
      .filter(Objects::nonNull)
      .collect(Collectors.toList());
  }

  public static CustomerInfo from(Customer customer) {
    Affirm.of(customer).notNull("Customer must not be null");

    return CustomerInfo.builder()
        .code(customer.getCode())
        .id(customer.getCustomerId())
        .name(customer.getName())
        .build();
  }
}

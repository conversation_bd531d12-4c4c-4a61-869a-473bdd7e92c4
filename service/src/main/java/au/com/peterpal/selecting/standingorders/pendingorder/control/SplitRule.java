package au.com.peterpal.selecting.standingorders.pendingorder.control;

import au.com.peterpal.selecting.standingorders.allocation.model.Allocation;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ActionType;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.ReleaseType;
import au.com.peterpal.selecting.standingorders.ext.category.entity.Category;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.Fund;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.CopyPendingOrderDto;
import au.com.peterpal.selecting.standingorders.pendingorder.dto.POCreateInfo;
import au.com.peterpal.selecting.standingorders.pendingorder.model.BranchDistribution;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrder;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderId;
import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import au.com.peterpal.selecting.standingorders.titles.entity.Title;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public class SplitRule extends AbstractPOCreator {

  @Override
  protected List<PendingOrder> createPOList(POCreateInfo info) {

    Allocation allocation = info.getAllocation();
    Release release = info.getRelease();
    Title title = info.getTitle();

    boolean isSingleProductSelected =
        CollectionUtils.isNotEmpty(info.getProducts()) && info.getProducts().size() == 1;

    List<PendingOrder> pendingOrders =
        info.getProducts().stream()
            .map(
                p -> {
                  Integer quantity =
                      isSingleProductSelected
                          ? release.getQuantity()
                          : release.getQuantity(p.getFormat());
                  return PendingOrder.builder()
                      .pendingOrderId(PendingOrderId.of(UUID.randomUUID()))
                      .customerReference(
                          getCustomerRef(allocation.getCustomerReference(), info.getPref()))
                      .orderStatus(PendingOrderStatus.NEW)
                      .customer(allocation.getCustomerStandingOrder().getCustomer())
                      .fund(getFund(p.getFormat(), release, allocation, info.getPref()))
                      .orderedProductReference(p.getIsbn())
                      .format(p.getFormat())
                      .release(release)
                      .quantity(quantity)
                      .category(title.getCategory())
                      .collectionCode(allocation.getCollectionCode())
                      .deliveryInstructions(
                          getDeliveryInst(allocation.getDeliveryInstructions(), info.getPref()))
                      .notes(getNotes(allocation.getNotes(), info.getPref()))
                      .title(title)
                      .price(p.getPrice())
                      .publicationDate(p.getPubDate())
                      .currencyCode(p.getCurrencyCode())
                      .build();
                })
            .filter(po -> po.getQuantity() > 0)
            .collect(Collectors.toList());

    List<Allocation> eligibleBranchAllocations =
        Optional.ofNullable(info.getBranchAllocations()).orElse(Lists.newArrayList()).stream()
            .filter(a -> a.findRelease(ReleaseType.INITIAL).isPresent())
            .filter(a -> a.getRelease(ReleaseType.INITIAL).getActionType() != ActionType.IGNORE)
            .sorted(Comparator.comparing(a -> a.getBranch().getCode()))
            .collect(Collectors.toList());
    if (CollectionUtils.isNotEmpty(pendingOrders)
        && CollectionUtils.isNotEmpty(eligibleBranchAllocations)) {
      buildBranchDistributions(pendingOrders, eligibleBranchAllocations, title.getCategory());
    }

    return pendingOrders;
  }

  void buildBranchDistributions(
      List<PendingOrder> pendingOrders, List<Allocation> branchAllocations, Category category) {
    Map<AllocationId, Integer> totalQuantityAcquiredMap = Maps.newHashMap();

    for (PendingOrder pendingOrder : pendingOrders) {
      pendingOrder.setBranchDistributions(Sets.newHashSet());
      Integer quantityOutstanding = pendingOrder.getQuantity();
      for (Allocation branchAllocation : branchAllocations) {
        Release release = branchAllocation.getRelease(ReleaseType.INITIAL);
        Integer quantityAcquired =
            totalQuantityAcquiredMap.computeIfAbsent(branchAllocation.getAllocationId(), s -> 0);

        // fully taken, need to be skipped
        if (release.getQuantity().equals(quantityAcquired)) {
          continue;
        }

        Integer branchQuantity =
            quantityOutstanding >= (release.getQuantity() - quantityAcquired)
                ? (release.getQuantity() - quantityAcquired)
                : quantityOutstanding;
        quantityOutstanding -= branchQuantity;
        totalQuantityAcquiredMap.put(
            branchAllocation.getAllocationId(), quantityAcquired + branchQuantity);

        pendingOrder
            .getBranchDistributions()
            .add(
                BranchDistribution.from(
                    branchAllocation,
                    category,
                    Optional.ofNullable(release.getFund(pendingOrder.getFormat()))
                        .map(Fund::getCode)
                        .orElse(null),
                    branchQuantity));

        if (quantityOutstanding == 0) {
          break;
        }
      }
    }
  }

  @Override
  public List<PendingOrder> copy(CopyPendingOrderDto request) {
    Allocation allocation = request.getAllocation();
    Release release = request.getAllocation().getRelease(ReleaseType.INITIAL);
    Title title = request.getTitle();

    boolean isSingleProductSelected =
        CollectionUtils.isNotEmpty(title.getProductInfoFromAcceptedProducts())
            && title.getProductInfoFromAcceptedProducts().size() == 1;

    List<PendingOrder> copiedPendingOrders =
        request.getPendingOrderDetailMap().values().stream()
            .map(
                pendingOrderDetail -> {
                  Integer quantity =
                      isSingleProductSelected
                          ? release.getQuantity()
                          : release.getQuantity(pendingOrderDetail.getFormat());
                  if (quantity > pendingOrderDetail.getQuantityTaken()) {
                    int deltaQuantity = quantity - pendingOrderDetail.getQuantityTaken();
                    return PendingOrder.builder()
                        .pendingOrderId(PendingOrderId.of(UUID.randomUUID()))
                        .customerReference(pendingOrderDetail.getCustomerReference())
                        .orderStatus(PendingOrderStatus.NEW)
                        .customer(allocation.getCustomerStandingOrder().getCustomer())
                        .fund(pendingOrderDetail.getFund())
                        .orderedProductReference(pendingOrderDetail.getIsbn())
                        .format(pendingOrderDetail.getFormat())
                        .release(release)
                        .quantity(deltaQuantity)
                        .category(pendingOrderDetail.getCategory())
                        .collectionCode(pendingOrderDetail.getCollectionCode())
                        .deliveryInstructions(pendingOrderDetail.getDeliveryInstruction())
                        .notes(pendingOrderDetail.getNotes())
                        .title(title)
                        .price(pendingOrderDetail.getPrice())
                        .publicationDate(pendingOrderDetail.getPublicationDate())
                        .currencyCode(pendingOrderDetail.getCurrencyCode())
                        .supplier(pendingOrderDetail.getSupplier())
                        .build();
                  } else {
                    return null;
                  }
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

    if (CollectionUtils.isNotEmpty(copiedPendingOrders)
        && CollectionUtils.isNotEmpty(request.getBranchAllocations())) {
      copyBranchDistributionss(copiedPendingOrders, request, request.getBranchAllocations());
    }

    return copiedPendingOrders;
  }

  void copyBranchDistributionss(
      List<PendingOrder> copiedPendingOrders,
      CopyPendingOrderDto request,
      List<Allocation> branchAllocations) {

    Map<AllocationId, Integer> quantityTakenMap =
        request.getPendingOrderDetailMap().values().stream()
            .map(CopyPendingOrderDto.PendingOrderDetail::getBrantchQtyTakenMap)
            .map(Map::entrySet)
            .flatMap(Collection::stream)
            .collect(
                Collectors.groupingBy(
                    Map.Entry::getKey, Collectors.summingInt(Map.Entry::getValue)));

    Map<AllocationId, Integer> totalQuantityAcquiredMap = Maps.newHashMap();

    for (PendingOrder pendingOrder : copiedPendingOrders) {
      pendingOrder.setBranchDistributions(Sets.newHashSet());
      Integer quantityOutstanding = pendingOrder.getQuantity();
      for (Allocation branchAllocation : branchAllocations) {
        Integer releaseQuantity = branchAllocation.getRelease(ReleaseType.INITIAL).getQuantity();
        Integer availableQty =
            (releaseQuantity
                - Optional.ofNullable(quantityTakenMap.get(branchAllocation.getAllocationId()))
                    .orElse(0));
        Integer quantityAcquired =
            totalQuantityAcquiredMap.computeIfAbsent(branchAllocation.getAllocationId(), s -> 0);

        // fully taken, need to be skipped
        if (availableQty.equals(quantityAcquired)) {
          continue;
        }

        Integer branchQuantity =
            quantityOutstanding >= (availableQty - quantityAcquired)
                ? (availableQty - quantityAcquired)
                : quantityOutstanding;
        quantityOutstanding -= branchQuantity;
        totalQuantityAcquiredMap.put(
            branchAllocation.getAllocationId(), quantityAcquired + branchQuantity);

        pendingOrder
            .getBranchDistributions()
            .add(
                BranchDistribution.from(
                    branchAllocation,
                    pendingOrder.getCategory(),
                    Optional.of(pendingOrder)
                        .map(PendingOrder::getFund)
                        .map(Fund::getCode)
                        .orElse(null),
                    branchQuantity));

        if (quantityOutstanding == 0) {
          break;
        }
      }
    }
  }
}

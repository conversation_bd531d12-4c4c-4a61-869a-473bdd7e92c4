package au.com.peterpal.selecting.standingorders.pendingorder.dto;

import au.com.peterpal.selecting.standingorders.pendingorder.model.PendingOrderStatus;
import au.com.peterpal.selecting.standingorders.titles.entity.TitleId;
import lombok.NonNull;
import lombok.Value;
import javax.validation.constraints.NotNull;

@Value
public class PendingOrderByTitleRequest {
  @NonNull @NotNull TitleId titleId;
  PendingOrderStatus status;
  String category;
}

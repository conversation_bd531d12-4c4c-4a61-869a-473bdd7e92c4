package au.com.peterpal.selecting.standingorders.customerstandingorder.dto;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.With;

@With
@Data
@Setter
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class UpdateBranchAllocationRequest {
  @NotEmpty private List<@Valid BranchAllocationInfo> branchAllocationInfos;
  @Builder.Default private Boolean rematchAllocationAfterUpdate = Boolean.FALSE;
}

package au.com.peterpal.selecting.standingorders.standingorder.dto;

import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Value;
import lombok.With;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@With
@Value
@NoArgsConstructor(access = AccessLevel.PRIVATE, force = true)
@AllArgsConstructor(staticName = "of")
@Builder
public class StandingOrderRequest {

  private StandingOrderStatus standingOrderStatus;
  private CustomerId customerId;
  private String standingOrderNumber;
  private String description;
  private String term;
  private List<String> categories;
  private String notes;
  @JsonIgnore
  private String sortByKey;
  @JsonIgnore
  private String sortByDirection;
  public String getTerm() {
    return StringUtils.trimToEmpty(term).replaceAll("[ ]+", " ");
  }

  @JsonIgnore
  public String getTermWithAddedOrRemovedSpaceAfterComma() {
    String termOpposite = StringUtils.contains(getTerm(), ", ") ?
        org.apache.commons.lang3.StringUtils.replace(getTerm(), ", ", ",") :
        StringUtils.replace(getTerm(), ",", ", ");
    return termOpposite.toLowerCase();
  }
}

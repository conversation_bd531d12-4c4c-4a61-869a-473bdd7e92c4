package au.com.peterpal.selecting.standingorders.ext.customer.control.dto;

import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import javax.validation.constraints.NotNull;

@With
@Data
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@RequiredArgsConstructor(staticName = "of")
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerMessage {

  @EqualsAndHashCode.Include
  @ToString.Include
  @NonNull
  @NotNull
  private CustomerId customerId;

  @NonNull
  @NotNull
  private String code;

  @NotNull
  @NotNull
  private String name;

  @NotNull
  @NotNull
  private CustomerStatus status;
}

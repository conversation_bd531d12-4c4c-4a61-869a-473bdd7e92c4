package au.com.peterpal.selecting.standingorders.ext.customer.control;

import au.com.peterpal.common.utils.ext.SyncService;
import au.com.peterpal.selecting.standingorders.ext.customer.control.dto.CustomerMessage;
import au.com.peterpal.selecting.standingorders.ext.customer.control.dto.CustomerStatus;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.Customer;
import java.util.List;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class CustomerSyncService extends
    SyncService<CustomerRepository, CustomerMessage, Customer> {

  private CustomerRepository repo;

  public CustomerSyncService(CustomerRepository customerRepository) {
    super(customerRepository);
    repo = customerRepository;
  }

  @Override
  public CustomerMessage handle(CustomerMessage msg) {
    return CustomerStatus.INACTIVE.equals(msg.getStatus()) ? msg : super.handle(msg);
  }

  @Override
  protected Object getId(CustomerMessage msg) {
    return msg.getCustomerId();
  }

  @Override
  protected CustomerMessage update(Customer existing, CustomerMessage customerMessage) {
    existing.setName(customerMessage.getName());
    existing.setCode(customerMessage.getCode());
    save(existing);
    return customerMessage;
  }

  @Override
  protected CustomerMessage create(CustomerMessage customerMessage) {
    String msg = "Customer %s will not be created because already exists. msg %s";
    List<Customer> list = repo.findAllByCode(customerMessage.getCode());
    if (!list.isEmpty()) {
      log.warn(() -> String.format(msg, customerMessage.getCode(), customerMessage));
    } else {
      save(Customer.builder()
          .customerId(customerMessage.getCustomerId())
          .code(customerMessage.getCode())
          .name(customerMessage.getName())
          .build());
    }
    return customerMessage;
  }
}

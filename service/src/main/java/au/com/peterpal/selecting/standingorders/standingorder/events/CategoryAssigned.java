package au.com.peterpal.selecting.standingorders.standingorder.events;

import au.com.peterpal.common.audit.events.DomainEvent;
import java.util.UUID;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

@Getter
@SuperBuilder
public class CategoryAssigned extends DomainEvent {

  public static CategoryAssigned of(String subject, String category, String username) {
    return CategoryAssigned.builder()
        .id(UUID.randomUUID())
        .username(StringUtils.isBlank(username) ? "system" : username)
        .subject(subject)
        .category(category)
        .build();
  }

  private String subject;
  private String category;
}

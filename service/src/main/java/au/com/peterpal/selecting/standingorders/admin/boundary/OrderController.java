package au.com.peterpal.selecting.standingorders.admin.boundary;

import au.com.peterpal.selecting.standingorders.admin.dto.SearchOrderRequest;
import au.com.peterpal.selecting.standingorders.admin.dto.SearchStaffRequest;
import au.com.peterpal.selecting.standingorders.ext.budget.entity.FundType;
import au.com.peterpal.selecting.standingorders.ext.category.entity.CategoryId;
import au.com.peterpal.selecting.standingorders.ext.fund.control.FundService;
import au.com.peterpal.selecting.standingorders.ext.order.control.OrderService;
import au.com.peterpal.selecting.standingorders.ext.order.entity.Order;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Log4j2
@RequestMapping("/api/orders")
@RestController
@AllArgsConstructor
@SecurityRequirement(name = "BearerAuth")
public class OrderController {

  private final OrderService orderService;
  private final FundService fundService;

  @GetMapping("/staff")
  @Operation(
      summary =
          "Get Staff by customerCode, fund code, fund type, orderOrInvoicedDate. The default of orderOrInvoicedDate is start date of current financial year")
  public Set<String> getAllStaffs(
      @RequestParam(required = false) String customerCode,
      @RequestParam(required = false) List<String> fundCodes,
      @RequestParam(required = false) FundType fundType,
      @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate orderOrInvoicedDate) {
    if (Objects.isNull(orderOrInvoicedDate)) {
      orderOrInvoicedDate = fundService.getCurrentFinStartDate();
    }

    SearchStaffRequest searchStaffRequest =
        SearchStaffRequest.builder()
            .customerCode(customerCode)
            .fundCodes(fundCodes)
            .fundType(fundType)
            .orderOrInvoicedDate(orderOrInvoicedDate)
            .build();
    log.debug("Getting all staffs with search request {}", searchStaffRequest);

    return orderService.getAllStaffs(searchStaffRequest);
  }

  @GetMapping("/search")
  public Page<Order> search(
      @RequestParam(required = false) String customerCode,
      @RequestParam(required = false) String fundCode,
      @RequestParam(required = false) String staff,
      @RequestParam(required = false) FundType fundType,
      @RequestParam(required = false) List<CategoryId> categoryIds,
      @RequestParam(required = false)
          @DateTimeFormat(iso = DateTimeFormat.ISO.DATE, pattern = "yyyy-MM-dd")
          LocalDate orderDate,
      @PageableDefault(size = 20, direction = Sort.Direction.ASC, sort = "orderNumber")
          Pageable pageRequest) {
    SearchOrderRequest searchOrderRequest =
        SearchOrderRequest.builder()
            .customerCode(customerCode)
            .fundCode(fundCode)
            .staff(staff)
            .fundType(fundType)
            .orderDate(orderDate)
            .categoryIds(categoryIds)
            .build();
    log.debug("Searching orders with search request {}", searchOrderRequest);
    return orderService.search(searchOrderRequest, pageRequest);
  }
}

package au.com.peterpal.selecting.standingorders.allocation.commands;

import au.com.peterpal.selecting.standingorders.allocation.model.AllocationId;
import au.com.peterpal.selecting.standingorders.allocation.model.AllocationStatus;
import au.com.peterpal.selecting.standingorders.allocation.model.Release;
import au.com.peterpal.selecting.standingorders.allocationpreference.model.AllocationPreferenceId;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrder;
import au.com.peterpal.selecting.standingorders.customerstandingorder.model.CustomerStandingOrderId;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchId;
import au.com.peterpal.selecting.standingorders.ext.branch.entity.BranchStatus;
import au.com.peterpal.selecting.standingorders.ext.customer.entity.CustomerId;
import au.com.peterpal.selecting.standingorders.ext.fund.entity.FundId;
import au.com.peterpal.selecting.standingorders.standingorder.model.StandingOrderId;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

@Data
@Builder
@NoArgsConstructor(access = AccessLevel.PUBLIC, force = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AllocationSearchResponse {
  private AllocationId allocationId;
  private CustomerStandingOrderId customerStandingOrderId;
  private CustomerStandingOrder customerStandingOrder;

  private CustomerId customerId;
  private String customerCode;
  private String customerName;

  private BranchId branchId;

  private String branchCode;

  private String branchName;
  private BranchStatus branchStatus;

  private FundId fundId;
  private String fundCode;
  private String fundName;

  private AllocationPreferenceId preferenceId;

  private AllocationStatus status;
  private List<String> categories;
  private String customerReference;
  private String deliveryInstructions;
  private String notes;
  private Release initialReleases;

  private String standingOrderNumber;
  private String standingOrderDescription;

  private StandingOrderId standingOrderId;
  private String collectionCode;

  private AllocationId baParentId;
  private Integer quantity;
  private Integer hardbackQuantity;
  private Integer paperbackQuantity;
  @JsonIgnore
  private String minCategory;
}

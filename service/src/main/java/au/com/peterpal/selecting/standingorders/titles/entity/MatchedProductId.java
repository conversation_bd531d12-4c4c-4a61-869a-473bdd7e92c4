package au.com.peterpal.selecting.standingorders.titles.entity;

import au.com.peterpal.common.ddd.ids.UuidEntityId;
import com.fasterxml.jackson.annotation.JsonCreator;

import javax.persistence.Embeddable;
import javax.validation.constraints.NotEmpty;
import java.util.UUID;

@Embeddable
public class MatchedProductId extends UuidEntityId {

  public static MatchedProductId of(@NotEmpty UUID id) {
    return new MatchedProductId(id);
  }

  public static MatchedProductId of(@NotEmpty String id) {
    return new MatchedProductId(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public MatchedProductId(@NotEmpty UUID id) {
    super(id);
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public MatchedProductId(@NotEmpty String id) {
    super(UUID.fromString(id));
  }

  public MatchedProductId() {}

  @Override
  public @NotEmpty UUID getId() {
    return super.getId();
  }
}
